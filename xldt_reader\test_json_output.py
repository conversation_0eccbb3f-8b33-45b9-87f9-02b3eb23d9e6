#!/usr/bin/env python3
"""
Test script to demonstrate the improved JSON output with float32 arrays.
"""

import json
from xldt_reader_standalone import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_json_output():
    """Test the JSON output format with float32 arrays."""
    print("🧪 Testing Enhanced JSON Output Format")
    print("=" * 50)
    
    # Initialize reader
    config_path = "config/IRS/IRS_SL_Conf.csv"
    reader = XLDTReader(config_path=config_path)
    
    # Test with a sample file
    test_file = "inputs/ScanLaw_Central_Summer_16384.xldt"
    print(f"📁 Analyzing: {test_file}")
    
    try:
        # Read and parse the file
        data = reader.read_xldt_file(test_file)
        
        # Export to dictionary format
        json_data = reader.export_to_dict(data)
        
        print("\n📊 JSON Output Features:")
        print("-" * 30)
        
        # Show header with float values
        header = json_data['header']
        print(f"✅ Header values as float32:")
        print(f"   Format ID: {header['format_id']} (type: {type(header['format_id']).__name__})")
        print(f"   MM Slot: {header['mm_slot']} (type: {type(header['mm_slot']).__name__})")
        
        # Show section information
        sections = json_data['sections']
        print(f"\n✅ Section data formats:")
        
        for section_name, section_data in sections.items():
            if isinstance(section_data, dict) and 'length' in section_data:
                print(f"\n   📦 {section_name}:")
                print(f"      Length: {section_data['length']} (type: {type(section_data['length']).__name__})")
                print(f"      Order: {section_data['order']} (type: {type(section_data['order']).__name__})")
                
                # Show array data if available
                if 'data_float32_array' in section_data:
                    array = section_data['data_float32_array']
                    print(f"      🔢 Float32 Array: {len(array)} values")
                    print(f"         First 5 values: {array[:5]}")
                    print(f"         Data format: {section_data.get('data_format', 'N/A')}")
                
                elif 'data_uint32_array' in section_data:
                    array = section_data['data_uint32_array']
                    print(f"      🔢 UInt32 Array: {len(array)} values")
                    print(f"         Values: {array}")
                
                elif 'data_uint16_array' in section_data:
                    array = section_data['data_uint16_array']
                    print(f"      🔢 UInt16 Array: {len(array)} values")
                    print(f"         Values: {array}")
        
        # Show FDA section details (the main data section)
        if 'FDA' in sections:
            fda_section = sections['FDA']
            print(f"\n🎯 FDA Section Details:")
            print(f"   Total bytes: {fda_section['data_bytes']}")
            if 'data_float32_array' in fda_section:
                float_array = fda_section['data_float32_array']
                print(f"   Float32 values: {len(float_array)}")
                print(f"   Array length: {fda_section['array_length']}")
                print(f"   Data format: {fda_section['data_format']}")
                
                # Show some statistics
                non_zero_values = [v for v in float_array if v != 0.0]
                print(f"   Non-zero values: {len(non_zero_values)}")
                if non_zero_values:
                    print(f"   Min value: {min(non_zero_values):.6e}")
                    print(f"   Max value: {max(non_zero_values):.6e}")
                    print(f"   Sample values: {non_zero_values[:10]}")
        
        # Save a sample JSON file
        output_file = "sample_json_output.json"
        with open(output_file, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        print(f"\n💾 Sample JSON saved to: {output_file}")
        print(f"   File size: {len(json.dumps(json_data, indent=2)):,} characters")
        
        # Show JSON structure summary
        print(f"\n📋 JSON Structure Summary:")
        print(f"   - file_info: Basic file information")
        print(f"   - header: XLDT header with float32 values")
        print(f"   - sections: {len(sections)} sections with enhanced data")
        print(f"     * Float32 arrays for FDA section")
        print(f"     * UInt32/UInt16 arrays for other sections")
        print(f"     * Original hex data preserved")
        print(f"   - raw_hex: Complete file as hex string")
        
    except Exception as e:
        print(f"❌ Error: {e}")


def show_json_sample():
    """Show a sample of the JSON structure."""
    print(f"\n📄 Sample JSON Structure:")
    print("-" * 30)
    
    sample_structure = {
        "file_info": {
            "file_path": "inputs/ScanLaw_Central_Summer_16384.xldt",
            "file_size": 4950,
            "hex_length": 9900
        },
        "header": {
            "format_id": 1.0,
            "mm_slot": 5.0,
            "body_length": None
        },
        "sections": {
            "FDA": {
                "msdf_id": 1.0,
                "length": 1680.0,
                "order": 7.0,
                "data_hex": "000000000700002800...",
                "data_bytes": 1680.0,
                "data_float32_array": [0.0, 7.105433286831633e-15, 0.0, "..."],
                "array_length": 420.0,
                "data_format": "float32_little_endian_preferred"
            },
            "Header": {
                "msdf_id": 0.0,
                "length": 4.0,
                "order": 3.0,
                "data_hex": "134E7A18",
                "data_bytes": 4.0,
                "data_uint32_array": [410668563.0],
                "data_int32_array": [410668563.0],
                "data_uint16_array": [19987.0, 6266.0]
            }
        }
    }
    
    print(json.dumps(sample_structure, indent=2))


if __name__ == "__main__":
    test_json_output()
    show_json_sample()
    
    print(f"\n🎉 Enhanced JSON output testing complete!")
    print(f"   ✅ All numeric values exported as float32")
    print(f"   ✅ FDA data converted to float32 array")
    print(f"   ✅ Other sections include multiple interpretations")
    print(f"   ✅ Original hex data preserved")
    print(f"   ✅ Ready for use in GUI and CLI tools")
