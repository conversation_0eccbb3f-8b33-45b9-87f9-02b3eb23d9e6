"""
Algorithms Package
================

This package contains algorithm implementations for different instruments including:
- FCI (Flexible Combined Imager)
- IRS (InfraRed Sounder)
- UVN (Ultraviolet Visible Near-Infrared)
- LI (Lightning Imager)

Each module provides specialized functions for handling instrument-specific operations
such as telecommand stack generation, scan law processing, and memory management.
"""

# Version information
__version__ = '1.0.0'
__author__ = 'CDC Team'

# Import utils first as they have no instrument dependencies
from src.utils import xml_utils
from . import UVN_functions

# Initialize launchers
fci_functions_launcher = None
irs_functions_launcher = None
li_functions_launcher = None

# Independent imports for each instrument to avoid cross-dependencies
try:
    from .irs_functions.IRS_launcher import irs_functions_launcher
except ImportError:
    pass

try:
    from .fci_functions import fci_functions_launcher
except ImportError:
    pass

try:
    from .li_functions.LI_launcher import li_functions_launcher
except ImportError:
    pass

__all__ = [
    # Version info
    '__version__',
    '__author__',
    
    # Module exports - only export if successfully imported
    'irs_functions_launcher' if 'irs_functions_launcher' in locals() else None,
    'fci_functions_launcher' if 'fci_functions_launcher' in locals() else None,
    'li_functions_launcher' if 'li_functions_launcher' in locals() else None,
    'UVN_functions',
    'xml_utils'
]

# Filter out None values from __all__
__all__ = [x for x in __all__ if x is not None]
