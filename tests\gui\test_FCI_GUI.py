import pytest
from src.gui import create_app
from src.gui.FCI_GUI import FCI_GUI
from src.gui.frames import InstrumentHandler # Import InstrumentHandler
from src.gui.fci_gui_features.miss_sce_gui import MISS_SCE_GUI # Import MISS_SCE_GUI

def test_fci_gui_handle_activity(mock_app):
    # Create test activity parameters
    from src.utils.activity_params import ActivityParams
    act_params = ActivityParams()
    act_params.instrument = "FCI"
    act_params.activity = "Mission Scenario (SL+APC+SSL)"
    
    # Register FCI activities with the InstrumentHandler
    FCI_GUI.register_activities(InstrumentHandler)
    
    # Call handle_activity on InstrumentHandler
    InstrumentHandler.handle_activity(mock_app, act_params)
    
    # Verify show_frame was called with correct parameters
    mock_app.show_frame.assert_called_once()
    called_args, called_kwargs = mock_app.show_frame.call_args
    assert called_args[0] == MISS_SCE_GUI
    assert called_args[1].instrument == act_params.instrument
    assert called_args[1].activity == act_params.activity
