ID=1
VERSION=3.0
DOMAIN=MTI1
TYPE=PATCH
DESCRIPTION=VCU_RAMN,for_FCI_VCU_PIXMAPPING,channel_All
SOURCE=CDC SOET
CREATIONDATE=2025-05-08T11:31:58:819977
MODEL=
MODELVER=
DEVICE=VCU_RAMN
STARTADDR=0x0
ENDADDR=0x226
LENGTH=448
CHECKSUM=1B59
UNIT=2
START=0,COUNT=1,DATA=F40A
START=1,COUNT=1,DATA=C657
START=2,COUNT=1,DATA=54A7
START=3,COUNT=1,DATA=4868
START=4,COUNT=1,DATA=7F5D
START=5,COUNT=1,DATA=DE75
START=6,COUNT=1,DATA=2497
START=7,COUNT=1,DATA=69A9
START=8,COUNT=1,DATA=A35F
START=9,COUNT=1,DATA=4313
START=A,COUNT=1,DATA=21AB
START=B,COUNT=1,DATA=C6A6
START=C,COUNT=1,DATA=9906
START=D,COUNT=1,DATA=721F
START=E,COUNT=1,DATA=07D0
START=F,COUNT=1,DATA=5529
START=10,COUNT=1,DATA=F0A1
START=11,COUNT=1,DATA=774D
START=12,COUNT=1,DATA=F067
START=13,COUNT=1,DATA=BF21
START=14,COUNT=1,DATA=11E6
START=15,COUNT=1,DATA=3C13
START=16,COUNT=1,DATA=11E8
START=17,COUNT=1,DATA=4D83
START=18,COUNT=1,DATA=DBF3
START=19,COUNT=1,DATA=4364
START=1A,COUNT=1,DATA=E941
START=1B,COUNT=1,DATA=529B
START=1C,COUNT=1,DATA=7DA3
START=1D,COUNT=1,DATA=5AAE
START=1E,COUNT=1,DATA=4745
START=1F,COUNT=1,DATA=7612
START=20,COUNT=1,DATA=8B06
START=21,COUNT=1,DATA=7C08
START=22,COUNT=1,DATA=8151
START=23,COUNT=1,DATA=EF96
START=24,COUNT=1,DATA=0ADC
START=25,COUNT=1,DATA=795F
START=26,COUNT=1,DATA=EA2C
START=27,COUNT=1,DATA=08BC
START=28,COUNT=1,DATA=3518
START=29,COUNT=1,DATA=AFF9
START=2A,COUNT=1,DATA=2907
START=2B,COUNT=1,DATA=1B15
START=2C,COUNT=1,DATA=A340
START=2D,COUNT=1,DATA=5838
START=2E,COUNT=1,DATA=80C0
START=2F,COUNT=1,DATA=2319
START=30,COUNT=1,DATA=3482
START=31,COUNT=1,DATA=02F0
START=32,COUNT=1,DATA=3803
START=33,COUNT=1,DATA=12E0
START=34,COUNT=1,DATA=1401
START=35,COUNT=1,DATA=20A6
START=36,COUNT=1,DATA=D136
START=37,COUNT=1,DATA=32C3
START=38,COUNT=1,DATA=5555
START=39,COUNT=1,DATA=5555
START=3A,COUNT=1,DATA=5555
START=3B,COUNT=1,DATA=5555
START=3C,COUNT=1,DATA=5555
START=3D,COUNT=1,DATA=5555
START=3E,COUNT=1,DATA=5555
START=3F,COUNT=1,DATA=5555
START=40,COUNT=1,DATA=5555
START=41,COUNT=1,DATA=4451
START=42,COUNT=1,DATA=1000
START=43,COUNT=1,DATA=8022
START=44,COUNT=1,DATA=2A8A
START=45,COUNT=1,DATA=AAEA
START=46,COUNT=1,DATA=BBBE
START=47,COUNT=1,DATA=EFFB
START=48,COUNT=1,DATA=FFFF
START=49,COUNT=1,DATA=FFFF
START=4A,COUNT=1,DATA=FFFF
START=4B,COUNT=1,DATA=FFFF
START=4C,COUNT=1,DATA=FFFF
START=4D,COUNT=1,DATA=FFFF
START=4E,COUNT=1,DATA=FFEF
START=4F,COUNT=1,DATA=BAEA
START=50,COUNT=1,DATA=AA28
START=51,COUNT=1,DATA=8200
START=52,COUNT=1,DATA=1155
START=53,COUNT=1,DATA=5555
START=54,COUNT=1,DATA=9845
START=55,COUNT=1,DATA=201D
START=56,COUNT=1,DATA=FFFD
START=57,COUNT=1,DATA=FFEE
START=58,COUNT=1,DATA=BA95
START=59,COUNT=1,DATA=7555
START=5A,COUNT=1,DATA=5552
START=5B,COUNT=1,DATA=5654
START=5C,COUNT=1,DATA=1F7E
START=5D,COUNT=1,DATA=DDD5
START=5E,COUNT=1,DATA=7555
START=5F,COUNT=1,DATA=33F3
START=60,COUNT=1,DATA=FBFE
START=61,COUNT=1,DATA=CAA9
START=62,COUNT=1,DATA=8CBB
START=63,COUNT=1,DATA=FFFF
START=64,COUNT=1,DATA=F8E3
START=65,COUNT=1,DATA=8146
START=66,COUNT=1,DATA=5D57
START=67,COUNT=1,DATA=5DD7
START=68,COUNT=1,DATA=7DDF
START=69,COUNT=1,DATA=FDC3
START=6A,COUNT=1,DATA=2A80
START=6B,COUNT=1,DATA=0080
START=6C,COUNT=1,DATA=222A
START=6D,COUNT=1,DATA=ABBB
START=6E,COUNT=1,DATA=F1C0
START=6F,COUNT=1,DATA=0AAA
START=70,COUNT=1,DATA=F94F
START=71,COUNT=1,DATA=87FE
START=72,COUNT=1,DATA=7D2E
START=73,COUNT=1,DATA=DE12
START=74,COUNT=1,DATA=75F6
START=75,COUNT=1,DATA=97F1
START=76,COUNT=1,DATA=39F2
START=77,COUNT=1,DATA=97AB
START=78,COUNT=1,DATA=A75F
START=79,COUNT=1,DATA=7DEE
START=7A,COUNT=1,DATA=AD3B
START=7B,COUNT=1,DATA=1421
START=7C,COUNT=1,DATA=F281
START=7D,COUNT=1,DATA=508B
START=7E,COUNT=1,DATA=6C50
START=7F,COUNT=1,DATA=7055
START=80,COUNT=1,DATA=9D61
START=81,COUNT=1,DATA=605E
START=82,COUNT=1,DATA=B0BA
START=83,COUNT=1,DATA=3826
START=84,COUNT=1,DATA=4D76
START=85,COUNT=1,DATA=0666
START=86,COUNT=1,DATA=B892
START=87,COUNT=1,DATA=5AA5
START=88,COUNT=1,DATA=0984
START=89,COUNT=1,DATA=1860
START=8A,COUNT=1,DATA=10C6
START=8B,COUNT=1,DATA=0111
START=8C,COUNT=1,DATA=39D8
START=8D,COUNT=1,DATA=FCB0
START=8E,COUNT=1,DATA=D3A9
START=8F,COUNT=1,DATA=2E6F
START=90,COUNT=1,DATA=BB7E
START=91,COUNT=1,DATA=9098
START=92,COUNT=1,DATA=037B
START=93,COUNT=1,DATA=D1B0
START=94,COUNT=1,DATA=C0CE
START=95,COUNT=1,DATA=C5E3
START=96,COUNT=1,DATA=37CC
START=97,COUNT=1,DATA=4B44
START=98,COUNT=1,DATA=909E
START=99,COUNT=1,DATA=8FEF
START=9A,COUNT=1,DATA=F388
START=9B,COUNT=1,DATA=1BFB
START=9C,COUNT=1,DATA=D671
START=9D,COUNT=1,DATA=28D2
START=9E,COUNT=1,DATA=2CF1
START=9F,COUNT=1,DATA=F42C
START=A0,COUNT=1,DATA=AE2C
START=A1,COUNT=1,DATA=4F8B
START=A2,COUNT=1,DATA=1023
START=A3,COUNT=1,DATA=FB0B
START=A4,COUNT=1,DATA=26CC
START=A5,COUNT=1,DATA=D902
START=A6,COUNT=1,DATA=392B
START=A7,COUNT=1,DATA=6EE5
START=AC,COUNT=1,DATA=9236
START=AD,COUNT=1,DATA=2BFE
START=AE,COUNT=1,DATA=79F9
START=AF,COUNT=1,DATA=E617
START=B0,COUNT=1,DATA=E4EB
START=B1,COUNT=1,DATA=CCA5
START=B2,COUNT=1,DATA=6D99
START=B3,COUNT=1,DATA=09D8
START=B4,COUNT=1,DATA=AEC4
START=B5,COUNT=1,DATA=1A00
START=B6,COUNT=1,DATA=3010
START=B7,COUNT=1,DATA=AE6E
START=B8,COUNT=1,DATA=7C05
START=B9,COUNT=1,DATA=AAD8
START=BA,COUNT=1,DATA=60B0
START=BB,COUNT=1,DATA=DB93
START=BC,COUNT=1,DATA=5C9B
START=BD,COUNT=1,DATA=0CD9
START=BE,COUNT=1,DATA=24C8
START=BF,COUNT=1,DATA=2A19
START=C0,COUNT=1,DATA=EA04
START=C1,COUNT=1,DATA=C012
START=C2,COUNT=1,DATA=0896
START=C3,COUNT=1,DATA=EB16
START=C4,COUNT=1,DATA=0A39
START=C5,COUNT=1,DATA=8958
START=C6,COUNT=1,DATA=BB82
START=C7,COUNT=1,DATA=97E7
START=C8,COUNT=1,DATA=F709
START=C9,COUNT=1,DATA=6679
START=CA,COUNT=1,DATA=E5D1
START=CB,COUNT=1,DATA=81DC
START=CC,COUNT=1,DATA=6CED
START=CD,COUNT=1,DATA=A2FA
START=CE,COUNT=1,DATA=AA52
START=CF,COUNT=1,DATA=E3D8
START=D0,COUNT=1,DATA=8CC4
START=D1,COUNT=1,DATA=D762
START=D2,COUNT=1,DATA=2F41
START=D3,COUNT=1,DATA=A2BB
START=D4,COUNT=1,DATA=67BD
START=D5,COUNT=1,DATA=4A2D
START=D6,COUNT=1,DATA=126B
START=D7,COUNT=1,DATA=1644
START=D8,COUNT=1,DATA=104C
START=D9,COUNT=1,DATA=B2C7
START=DA,COUNT=1,DATA=C63E
START=DB,COUNT=1,DATA=18EF
START=DC,COUNT=1,DATA=2E7F
START=DD,COUNT=1,DATA=5836
START=DE,COUNT=1,DATA=57C4
START=DF,COUNT=1,DATA=4E8A
START=E0,COUNT=1,DATA=8F61
START=E1,COUNT=1,DATA=E385
START=E2,COUNT=1,DATA=0EEC
START=E3,COUNT=1,DATA=6BFE
START=E4,COUNT=1,DATA=DC9D
START=E5,COUNT=1,DATA=8DC9
START=E6,COUNT=1,DATA=D8DC
START=E7,COUNT=1,DATA=9D8D
START=E8,COUNT=1,DATA=C9D8
START=E9,COUNT=1,DATA=DC9D
START=EA,COUNT=1,DATA=8DC9
START=EB,COUNT=1,DATA=D8DC
START=EC,COUNT=1,DATA=9D8D
START=ED,COUNT=1,DATA=C9D8
START=EE,COUNT=1,DATA=DC9D
START=EF,COUNT=1,DATA=8DC9
START=F0,COUNT=1,DATA=D8DC
START=F1,COUNT=1,DATA=9D8D
START=F2,COUNT=1,DATA=C9D8
START=F3,COUNT=1,DATA=DC9D
START=F4,COUNT=1,DATA=8DC9
START=F5,COUNT=1,DATA=D8DC
START=F6,COUNT=1,DATA=9D8D
START=F7,COUNT=1,DATA=C9D8
START=F8,COUNT=1,DATA=DC9D
START=F9,COUNT=1,DATA=8DC9
START=FA,COUNT=1,DATA=D8DC
START=FB,COUNT=1,DATA=9D8D
START=FC,COUNT=1,DATA=C9D8
START=FD,COUNT=1,DATA=DC9D
START=FE,COUNT=1,DATA=8DC9
START=FF,COUNT=1,DATA=D8DC
START=100,COUNT=1,DATA=9D8D
START=101,COUNT=1,DATA=C9D8
START=102,COUNT=1,DATA=DC9D
START=103,COUNT=1,DATA=8DC9
START=104,COUNT=1,DATA=D8DC
START=105,COUNT=1,DATA=9D8D
START=106,COUNT=1,DATA=C9D8
START=107,COUNT=1,DATA=DC9D
START=108,COUNT=1,DATA=8DC9
START=109,COUNT=1,DATA=D8DC
START=10A,COUNT=1,DATA=9D8D
START=10B,COUNT=1,DATA=C9D8
START=10C,COUNT=1,DATA=DC9D
START=10D,COUNT=1,DATA=8DC9
START=10E,COUNT=1,DATA=D8DC
START=10F,COUNT=1,DATA=9D8D
START=110,COUNT=1,DATA=C9D8
START=111,COUNT=1,DATA=DC9D
START=112,COUNT=1,DATA=8DC9
START=113,COUNT=1,DATA=D8DC
START=114,COUNT=1,DATA=9D8D
START=115,COUNT=1,DATA=C9D8
START=116,COUNT=1,DATA=DC9D
START=117,COUNT=1,DATA=8DC9
START=118,COUNT=1,DATA=D8DC
START=119,COUNT=1,DATA=9D8D
START=11A,COUNT=1,DATA=C9D8
START=11B,COUNT=1,DATA=DC9D
START=121,COUNT=1,DATA=D615
START=122,COUNT=1,DATA=0A82
START=123,COUNT=1,DATA=C601
START=124,COUNT=1,DATA=B5C5
START=125,COUNT=1,DATA=635C
START=126,COUNT=1,DATA=6DB0
START=127,COUNT=1,DATA=B169
START=128,COUNT=1,DATA=F5C3
START=129,COUNT=1,DATA=C6FA
START=12A,COUNT=1,DATA=1738
START=12B,COUNT=1,DATA=686C
START=12C,COUNT=1,DATA=B6B2
START=12D,COUNT=1,DATA=CB6C
START=12E,COUNT=1,DATA=6F5C
START=12F,COUNT=1,DATA=DB7A
START=130,COUNT=1,DATA=C72C
START=131,COUNT=1,DATA=B5E3
START=132,COUNT=1,DATA=78F1
START=133,COUNT=1,DATA=EB5A
START=134,COUNT=1,DATA=3C35
START=135,COUNT=1,DATA=36C3
START=136,COUNT=1,DATA=A586
START=137,COUNT=1,DATA=8DBC
START=138,COUNT=1,DATA=7DAF
START=139,COUNT=1,DATA=EBF0
START=13A,COUNT=1,DATA=6860
START=13B,COUNT=1,DATA=7A86
START=13C,COUNT=1,DATA=A0B1
START=13D,COUNT=1,DATA=385B
START=13E,COUNT=1,DATA=36F2
START=13F,COUNT=1,DATA=349C
START=140,COUNT=1,DATA=F59E
START=141,COUNT=1,DATA=3F6E
START=142,COUNT=1,DATA=4E69
START=143,COUNT=1,DATA=3CBD
START=144,COUNT=1,DATA=4B4F
START=145,COUNT=1,DATA=AC35
START=146,COUNT=1,DATA=ED68
START=147,COUNT=1,DATA=FE2E
START=148,COUNT=1,DATA=1AF4
START=149,COUNT=1,DATA=005B
START=14A,COUNT=1,DATA=A7C8
START=14B,COUNT=1,DATA=0A36
START=14C,COUNT=1,DATA=12B9
START=14D,COUNT=1,DATA=31CE
START=14E,COUNT=1,DATA=D3E0
START=14F,COUNT=1,DATA=2A8A
START=150,COUNT=1,DATA=027A
START=151,COUNT=1,DATA=5D1A
START=152,COUNT=1,DATA=F9EB
START=153,COUNT=1,DATA=8062
START=154,COUNT=1,DATA=1A04
START=155,COUNT=1,DATA=D35B
START=156,COUNT=1,DATA=0BB8
START=157,COUNT=1,DATA=F2AA
START=158,COUNT=1,DATA=238D
START=159,COUNT=1,DATA=44D1
START=15A,COUNT=1,DATA=3288
START=15B,COUNT=1,DATA=0860
START=15C,COUNT=1,DATA=9A12
START=15D,COUNT=1,DATA=3E47
START=15E,COUNT=1,DATA=3BE8
START=15F,COUNT=1,DATA=EF5C
START=160,COUNT=1,DATA=9551
START=161,COUNT=1,DATA=58B3
START=162,COUNT=1,DATA=D15A
START=163,COUNT=1,DATA=64BF
START=164,COUNT=1,DATA=ADE2
START=165,COUNT=1,DATA=E8E3
START=166,COUNT=1,DATA=EC85
START=167,COUNT=1,DATA=9E6D
START=168,COUNT=1,DATA=4E77
START=169,COUNT=1,DATA=867E
START=16A,COUNT=1,DATA=C32C
START=16B,COUNT=1,DATA=8163
START=16C,COUNT=1,DATA=66D2
START=16D,COUNT=1,DATA=E51A
START=16E,COUNT=1,DATA=7247
START=16F,COUNT=1,DATA=0B23
START=170,COUNT=1,DATA=9CE9
START=171,COUNT=1,DATA=C84A
START=172,COUNT=1,DATA=4272
START=173,COUNT=1,DATA=0AD7
START=174,COUNT=1,DATA=FDD8
START=1B2,COUNT=1,DATA=7266
START=1B3,COUNT=1,DATA=1C88
START=1B4,COUNT=1,DATA=9625
START=1B5,COUNT=1,DATA=E7C5
START=1B6,COUNT=1,DATA=BD29
START=1B7,COUNT=1,DATA=D872
START=1B8,COUNT=1,DATA=269A
START=1B9,COUNT=1,DATA=9262
START=1BA,COUNT=1,DATA=85B6
START=1BB,COUNT=1,DATA=B2AA
START=1BC,COUNT=1,DATA=1100
START=1BD,COUNT=1,DATA=C13B
START=1BE,COUNT=1,DATA=9450
START=1BF,COUNT=1,DATA=74BD
START=1C0,COUNT=1,DATA=EBFA
START=1C1,COUNT=1,DATA=AAFF
START=1C2,COUNT=1,DATA=EBAB
START=1C3,COUNT=1,DATA=FBAF
START=1C4,COUNT=1,DATA=BEAF
START=1C5,COUNT=1,DATA=ABFB
START=1C6,COUNT=1,DATA=BAFA
START=1C7,COUNT=1,DATA=EFFF
START=1C8,COUNT=1,DATA=AFFA
START=1C9,COUNT=1,DATA=BBFB
START=1CA,COUNT=1,DATA=BBEA
START=1CB,COUNT=1,DATA=EBEE
START=1CC,COUNT=1,DATA=BEBA
START=1CD,COUNT=1,DATA=FBAE
START=1D3,COUNT=1,DATA=000B
START=1D4,COUNT=1,DATA=B371
START=1D5,COUNT=1,DATA=0A54
START=1D6,COUNT=1,DATA=247A
START=1D7,COUNT=1,DATA=B916
START=1D8,COUNT=1,DATA=2C54
START=1D9,COUNT=1,DATA=2A3B
START=1DA,COUNT=1,DATA=F938
START=1DB,COUNT=1,DATA=AFFB
START=1DC,COUNT=1,DATA=AB0B
START=1DD,COUNT=1,DATA=8C1B
START=1DE,COUNT=1,DATA=98D0
START=1DF,COUNT=1,DATA=11E3
START=1E0,COUNT=1,DATA=4F6E
START=1E1,COUNT=1,DATA=FC0A
START=1E2,COUNT=1,DATA=6E4D
START=1E3,COUNT=1,DATA=14D5
START=1E4,COUNT=1,DATA=5442
START=1E5,COUNT=1,DATA=DD96
START=1E6,COUNT=1,DATA=B207
START=1E7,COUNT=1,DATA=C4F0
START=1E8,COUNT=1,DATA=8635
START=1E9,COUNT=1,DATA=599D
START=1EA,COUNT=1,DATA=725B
START=1EB,COUNT=1,DATA=46F2
START=1EC,COUNT=1,DATA=E288
START=1ED,COUNT=1,DATA=9ECF
START=1EE,COUNT=1,DATA=3E29
START=20B,COUNT=1,DATA=5716
START=20C,COUNT=1,DATA=F8B7
START=20D,COUNT=1,DATA=3DC5
START=20E,COUNT=1,DATA=C3D0
START=20F,COUNT=1,DATA=01BC
START=210,COUNT=1,DATA=1DEB
START=211,COUNT=1,DATA=19BB
START=212,COUNT=1,DATA=B7ED
START=213,COUNT=1,DATA=4C70
START=214,COUNT=1,DATA=544E
START=215,COUNT=1,DATA=DB69
START=216,COUNT=1,DATA=8D0E
START=217,COUNT=1,DATA=68DD
START=218,COUNT=1,DATA=E491
START=219,COUNT=1,DATA=A728
START=21A,COUNT=1,DATA=EAB8
START=21B,COUNT=1,DATA=4B81
START=21C,COUNT=1,DATA=44FD
START=21D,COUNT=1,DATA=81B3
START=21E,COUNT=1,DATA=0BD1
START=21F,COUNT=1,DATA=F899
START=220,COUNT=1,DATA=184A
START=221,COUNT=1,DATA=9C22
START=222,COUNT=1,DATA=2F8C
START=223,COUNT=1,DATA=0D8E
START=224,COUNT=1,DATA=0E0C
START=225,COUNT=1,DATA=35B3
START=226,COUNT=1,DATA=B4C0