"""
XLDT Reader Example
==================

This example demonstrates how to use the XLDT reader to parse XLDT binary files.
"""

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from utils.xldt_reader import XLDTReader
from utils.import_utils import logger


def example_read_xldt_with_config():
    """Example: Read XLDT file with configuration."""
    print("=" * 60)
    print("Example 1: Reading XLDT file with IRS configuration")
    print("=" * 60)
    
    # Paths (adjust as needed)
    config_path = "config/IRS/IRS_SL_Conf.csv"
    xldt_file = "path/to/your/xldt/file.bin"  # Replace with actual file
    
    try:
        # Initialize reader with configuration
        reader = XLDTReader(config_path=config_path)
        
        # Check if XLDT file exists (for demo purposes, we'll skip if not found)
        if not os.path.exists(xldt_file):
            print(f"XLDT file not found: {xldt_file}")
            print("This is just an example - replace with your actual XLDT file path")
            return
        
        # Read and parse the XLDT file
        parsed_data = reader.read_xldt_file(xldt_file)
        
        # Display results
        print(f"Successfully parsed XLDT file: {xldt_file}")
        print(f"File size: {parsed_data['file_info']['file_size']} bytes")
        
        # Header information
        header = parsed_data['header']
        print(f"Format ID: {header.format_id}")
        print(f"MM Slot: {header.mm_slot}")
        
        # Sections
        print(f"Number of sections: {len(parsed_data['sections'])}")
        for section_name, section in parsed_data['sections'].items():
            if hasattr(section, 'length'):
                print(f"  {section_name}: {section.length} bytes")
        
        # Validate CRC
        if 'CRC' in parsed_data['sections']:
            crc_valid = reader.validate_crc(
                parsed_data['raw_hex'],
                parsed_data['sections']['CRC']
            )
            print(f"CRC Valid: {crc_valid}")
        
        # Export to readable format
        readable_data = reader.export_to_dict(parsed_data)
        print("\nExported data structure available for further processing")
        
    except Exception as e:
        print(f"Error: {e}")


def example_read_xldt_without_config():
    """Example: Read XLDT file without configuration (generic parsing)."""
    print("\n" + "=" * 60)
    print("Example 2: Reading XLDT file without configuration")
    print("=" * 60)
    
    xldt_file = "path/to/your/xldt/file.bin"  # Replace with actual file
    
    try:
        # Initialize reader without configuration
        reader = XLDTReader()
        
        # Check if XLDT file exists
        if not os.path.exists(xldt_file):
            print(f"XLDT file not found: {xldt_file}")
            print("This is just an example - replace with your actual XLDT file path")
            return
        
        # Read and parse the XLDT file
        parsed_data = reader.read_xldt_file(xldt_file)
        
        # Display results
        print(f"Successfully parsed XLDT file: {xldt_file}")
        print(f"File size: {parsed_data['file_info']['file_size']} bytes")
        
        # Header information
        header = parsed_data['header']
        print(f"Format ID: {header.format_id}")
        print(f"MM Slot: {header.mm_slot}")
        
        # Generic sections info
        sections = parsed_data['sections']
        if 'remaining_hex' in sections:
            print(f"Remaining data: {sections['remaining_bytes']} bytes")
            print(f"Note: {sections['note']}")
        
    except Exception as e:
        print(f"Error: {e}")


def example_create_and_read_xldt():
    """Example: Create a sample XLDT file and then read it."""
    print("\n" + "=" * 60)
    print("Example 3: Create and read a sample XLDT file")
    print("=" * 60)
    
    try:
        # Import the create_XLDT function
        from functions import create_XLDT
        
        # Create sample XLDT data
        # XLDT Header: 0001 (format) + 0005 (MM slot 5)
        # CRC: 1234 (dummy CRC)
        # Sample body data
        sample_hex_data = "000100051234DEADBEEFCAFEBABE"
        
        # Create temporary XLDT file
        temp_file = "temp_sample.bin"
        create_XLDT(sample_hex_data, temp_file)
        
        print(f"Created sample XLDT file: {temp_file}")
        
        # Now read it back
        reader = XLDTReader()
        parsed_data = reader.read_xldt_file(temp_file)
        
        # Display results
        print(f"Read back XLDT file: {temp_file}")
        print(f"File size: {parsed_data['file_info']['file_size']} bytes")
        
        header = parsed_data['header']
        print(f"Format ID: {header.format_id}")
        print(f"MM Slot: {header.mm_slot}")
        
        print(f"Raw hex data: {parsed_data['raw_hex']}")
        
        # Clean up
        if os.path.exists(temp_file):
            os.remove(temp_file)
            print(f"Cleaned up temporary file: {temp_file}")
        
    except Exception as e:
        print(f"Error: {e}")


def example_batch_processing():
    """Example: Batch process multiple XLDT files."""
    print("\n" + "=" * 60)
    print("Example 4: Batch processing XLDT files")
    print("=" * 60)
    
    # Directory containing XLDT files (adjust as needed)
    xldt_directory = "path/to/xldt/files"  # Replace with actual directory
    
    if not os.path.exists(xldt_directory):
        print(f"Directory not found: {xldt_directory}")
        print("This is just an example - replace with your actual XLDT directory")
        return
    
    try:
        # Initialize reader
        reader = XLDTReader(config_path="config/IRS/IRS_SL_Conf.csv")
        
        # Find all .bin files in directory
        xldt_files = [f for f in os.listdir(xldt_directory) if f.endswith('.bin')]
        
        print(f"Found {len(xldt_files)} XLDT files to process")
        
        results = []
        
        for xldt_file in xldt_files:
            file_path = os.path.join(xldt_directory, xldt_file)
            
            try:
                parsed_data = reader.read_xldt_file(file_path)
                
                # Extract key information
                file_info = {
                    'filename': xldt_file,
                    'size': parsed_data['file_info']['file_size'],
                    'format_id': parsed_data['header'].format_id,
                    'mm_slot': parsed_data['header'].mm_slot,
                    'sections_count': len(parsed_data['sections'])
                }
                
                # Validate CRC if available
                if 'CRC' in parsed_data['sections']:
                    file_info['crc_valid'] = reader.validate_crc(
                        parsed_data['raw_hex'],
                        parsed_data['sections']['CRC']
                    )
                
                results.append(file_info)
                print(f"✓ Processed: {xldt_file}")
                
            except Exception as e:
                print(f"✗ Error processing {xldt_file}: {e}")
        
        # Summary
        print(f"\nProcessing Summary:")
        print(f"Total files: {len(xldt_files)}")
        print(f"Successfully processed: {len(results)}")
        
        if results:
            print("\nFile Details:")
            for result in results:
                print(f"  {result['filename']}: {result['size']} bytes, "
                      f"MM slot {result['mm_slot']}, "
                      f"{result['sections_count']} sections")
        
    except Exception as e:
        print(f"Error: {e}")


def main():
    """Run all examples."""
    print("XLDT Reader Examples")
    print("=" * 60)
    
    # Run examples
    example_read_xldt_with_config()
    example_read_xldt_without_config()
    example_create_and_read_xldt()
    example_batch_processing()
    
    print("\n" + "=" * 60)
    print("Examples completed!")
    print("=" * 60)
    
    print("\nTo use the XLDT reader in your own code:")
    print("1. Import: from src.utils.xldt_reader import XLDTReader")
    print("2. Initialize: reader = XLDTReader(config_path='config/IRS/IRS_SL_Conf.csv')")
    print("3. Read file: data = reader.read_xldt_file('your_file.bin')")
    print("4. Process data as needed")
    
    print("\nFor command-line usage:")
    print("python src/utils/xldt_cli.py your_file.bin --config config/IRS/IRS_SL_Conf.csv")


if __name__ == "__main__":
    main()
