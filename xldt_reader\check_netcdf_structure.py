#!/usr/bin/env python3
"""
Check NetCDF structure to understand the expected format.
"""

import sys
sys.path.append('.')
from xldt_gui import XLDTReaderGUI
import tkinter as tk

def check_netcdf_structure():
    """Check the NetCDF data structure."""
    root = tk.Tk()
    root.withdraw()
    
    try:
        gui = XLDTReaderGUI(root)
        netcdf_data = gui.get_netcdf_data_for_scanlaw(16386)
        
        if netcdf_data:
            print('🔍 NetCDF Structure for Scan Law 16386:')
            print('=' * 50)
            
            for key, value in netcdf_data.items():
                try:
                    if hasattr(value, '__len__') and len(value) > 1:
                        shape = getattr(value, 'shape', len(value))
                        first_10 = list(value[:10])
                        print(f'{key}: {type(value)} shape={shape} first_10={first_10}')
                    else:
                        print(f'{key}: {value}')
                except (TypeError, ValueError) as e:
                    print(f'{key}: {value} (error: {e})')
                    
            print('\n🎯 Expected XLDT Structure Should Match:')
            print('=' * 50)
            
            # Group variables like in NetCDF display
            fda_vars = []
            dwell_vars = []
            lac_vars = []
            mpa_vars = []
            other_vars = []
            
            for key, value in netcdf_data.items():
                if key.startswith('fda_'):
                    fda_vars.append((key, value))
                elif key.startswith('dwell_'):
                    dwell_vars.append((key, value))
                elif key.startswith('lac_'):
                    lac_vars.append((key, value))
                elif key.startswith('mpa_'):
                    mpa_vars.append((key, value))
                else:
                    other_vars.append((key, value))
            
            # Display grouped structure
            if fda_vars:
                print('\n🔹 FDA Variables:')
                for var_name, var_data in sorted(fda_vars):
                    try:
                        if hasattr(var_data, '__len__') and len(var_data) > 10:
                            first_10 = ', '.join(map(str, var_data[:10]))
                            print(f'   {var_name} = [{first_10}]... (length: {len(var_data)})')
                        elif hasattr(var_data, '__len__') and len(var_data) > 1:
                            data_str = ', '.join(map(str, var_data))
                            print(f'   {var_name} = [{data_str}]')
                        else:
                            print(f'   {var_name} = {var_data}')
                    except (TypeError, ValueError) as e:
                        print(f'   {var_name} = {var_data} (error: {e})')
            
            for group_name, group_vars in [
                ('Dwell Position', dwell_vars),
                ('LAC Pointer', lac_vars), 
                ('MPA Profile', mpa_vars),
                ('Other', other_vars)
            ]:
                if group_vars:
                    print(f'\n🔹 {group_name} Variables:')
                    for var_name, var_data in sorted(group_vars):
                        if hasattr(var_data, '__len__') and len(var_data) > 10:
                            first_10 = ', '.join(map(str, var_data[:10]))
                            print(f'   {var_name} = [{first_10}]... (length: {len(var_data)})')
                        elif hasattr(var_data, '__len__') and len(var_data) > 1:
                            data_str = ', '.join(map(str, var_data))
                            print(f'   {var_name} = [{data_str}]')
                        else:
                            print(f'   {var_name} = {var_data}')
        else:
            print('❌ Could not load NetCDF data')
            
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
    finally:
        root.destroy()

if __name__ == "__main__":
    check_netcdf_structure()
