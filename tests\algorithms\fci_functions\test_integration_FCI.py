# Standard library imports
import os
import time
from typing import Generator
import pytest
from pytest import fixture, mark

# Local imports
from tests.helpers import (
    load_input_files, 
    assert_img_files, 
    assert_paf_files, 
    assert_TC_stacks,
    assert_bin_files
)
from src.utils.import_utils import basics, config
from src.functions import generate_outputs
from src.utils.activity_params import ActivityParams
from src.logger_wrapper import logger # Added import

@pytest.fixture
def suspend_capture(pytestconfig) -> Generator:
    """Use to make user interactions in the test or order stops for modify configurations file, HW..."""
    class suspend_guard:
        def __init__(self):
            self.capmanager = pytestconfig.pluginmanager.getplugin("capturemanager")

        def __enter__(self):
            self.capmanager.suspend_global_capture(in_=True)

        def __exit__(self, _1, _2, _3):
            self.capmanager.resume_global_capture()

    yield suspend_guard()

@pytest.fixture()
def test_params() -> ActivityParams:
    """Fixture providing default test parameters for FCI tests."""
    # Load input files
    load_input_files("FCI")
    
    # Create and return default test parameters
    return ActivityParams.create(
        activity="VCU Update",
        satellites=["MTG-I 1"],
        instrument="FCI",
        icid=0,
        icid_ver=0,
        side="Nominal",
        test_mode=True
    )

import shutil

@pytest.mark.usefixtures("test_params")
class Test_FCI:
    """This covers all Cases of CDC_Validation (EUM/FLO/TEN/22/1341742)"""

    def setup_method(self, method):
        # Clean output directory before each test for isolation
        output_dir = os.path.join(config.CONFIG_VALUES["output_folder"], "MTG-I 1")
        if os.path.exists(output_dir):
            shutil.rmtree(output_dir)
        os.makedirs(output_dir, exist_ok=True)
        # Reset the global cache if possible
        from src.utils.data_cache import cache
        cache.data.clear()

    def test_case_1(self, test_params: ActivityParams):
        """This covers Case 1 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Num Offset Update"""
        generate_outputs(
            test_params,
            lst_channel=config.CONFIG_INSTRUMENT[test_params.instrument]["channel_names"],
            lst_part=["Numerical Offset"],
            str_VCU_image_format="Combined Image"
        )
        self._verify_outputs("Test_1", test_params, check_paf=True, check_img=True)

    def test_case_2(self, test_params: ActivityParams):
        """This covers Case 2 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Num Offset Update"""
        generate_outputs(
            test_params,
            lst_channel=["FD-VIS0.8", "FD-NIR1.3"],
            lst_part=["Numerical Offset"],
            str_VCU_image_format="Combined Image",
        )
        self._verify_outputs("Test_2", test_params, check_paf=True, check_img=True)


    def test_case_3(self, test_params: ActivityParams):
        """This covers Case 3 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Num Offset Update"""
        generate_outputs(
            test_params,
            lst_channel=config.CONFIG_INSTRUMENT[test_params.instrument]["channel_names"],
            lst_part=["Numerical Offset"],
            str_VCU_image_format="Image per Channel",
        )
        self._verify_outputs("Test_3", test_params, check_paf=True, check_img=True)

    def test_case_4(self, test_params: ActivityParams):
        """This covers Case 4 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Num Offset Update"""
        test_params.side = "Nominal"
        generate_outputs(
            test_params,
            lst_channel=["FD-VIS0.8", "FD-NIR1.3"],
            lst_part=["Numerical Offset"],
            str_VCU_image_format="Image per Channel",
        )
        self._verify_outputs("Test_4", test_params, check_paf=True, check_img=True)

    def test_case_5(self, test_params: ActivityParams):
        """This covers Case 5 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Num Offset Update"""
        test_params.side = "Redundant"
        generate_outputs(
            test_params,
            lst_channel=config.CONFIG_INSTRUMENT[test_params.instrument]["channel_names"],
            lst_part=["Numerical Offset"],
            str_VCU_image_format="Combined Image",
        )
        self._verify_outputs("Test_5", test_params, check_paf=True, check_img=True)

    def test_case_6(self, test_params: ActivityParams):
        """This covers Case 6 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Pixel Mapping Update"""
        test_params.side = "Nominal"
        generate_outputs(
            test_params,
            lst_channel=config.CONFIG_INSTRUMENT[test_params.instrument]["channel_names"],
            lst_part=["Pixel Mapping"],
            str_VCU_image_format="Combined Image",
        )
        self._verify_outputs("Test_6", test_params, check_img=True)

    def test_case_7(self, test_params: ActivityParams):
        """This covers Case 7 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Pixel Mapping Update"""
        test_params.side = "Nominal"
        generate_outputs(
            test_params,
            lst_channel=["FD-VIS0.8", "FD-NIR1.3"],
            lst_part=["Pixel Mapping"],
            str_VCU_image_format="Combined Image",
        )
        self._verify_outputs("Test_7", test_params, check_img=True)

    def test_case_8(self, test_params: ActivityParams):
        """This covers Case 8 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Pixel Mapping Update"""
        test_params.side = "Nominal"
        generate_outputs(
            test_params,
            lst_channel=config.CONFIG_INSTRUMENT[test_params.instrument]["channel_names"],
            lst_part=["Pixel Mapping"],
            str_VCU_image_format="Image per Channel",
        )
        self._verify_outputs("Test_8", test_params, check_img=True)
        time.sleep(7)  # Let a bit of time as there are many files to delete afterword

    def test_case_9(self, test_params: ActivityParams):
        """This covers Case 9 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Pixel Mapping Update"""
        test_params.side = "Nominal"
        generate_outputs(
            test_params,
            lst_channel=["FD-VIS0.8", "FD-NIR1.3"],
            lst_part=["Pixel Mapping"],
            str_VCU_image_format="Image per Channel",
        )
        self._verify_outputs("Test_9", test_params, check_img=True)

    def test_case_10(self, test_params: ActivityParams):
        """This covers Case 10 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Pixel Mapping Update"""
        test_params.side = "Redundant"
        generate_outputs(
            test_params,
            lst_channel=config.CONFIG_INSTRUMENT[test_params.instrument]["channel_names"],
            lst_part=["Pixel Mapping"],
            str_VCU_image_format="Combined Image",
        )
        self._verify_outputs("Test_10", test_params, check_img=True)

    def test_case_11(self, test_params: ActivityParams):
        """This covers Case 11 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        VCU Pixel Mapping Update"""
        # Set correct parameters for PAF generation
        test_params.activity = "VCU Update"
        test_params.side = "Nominal"
        test_params.icid = 0
        test_params.icid_ver = 0
        
        # Generate outputs - both parts needed for complete VCU update PAF
        generate_outputs(
            test_params,
            lst_channel=config.CONFIG_INSTRUMENT[test_params.instrument]["channel_names"],
            lst_part=["Instconf"],
            str_VCU_image_format="Combined Image"
        )
        
        # Verify PAF files
        self._verify_outputs("Test_11", test_params, check_paf=True)

    @mark.skip(reason="FCI APC SL checksum are broken")
    def test_case_12(self, test_params: ActivityParams):
        """This covers Case 12 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        Mission Scenario 1"""
        test_params.activity = "Mission Scenario (SL+APC+SSL)"
        test_params.scan_law_id = 32768
        test_params.mm_slot = 1
        
        generate_outputs(test_params)
        self._verify_outputs("Test_12", test_params, check_paf=True, check_bin=True)

    @mark.skip(reason="FCI APC SL checksum are broken")
    def test_case_13(self, test_params: ActivityParams):
        """This covers Case 13 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        Mission Scenario 2"""
        test_params.activity = "Mission Scenario (SL+APC+SSL)"
        test_params.scan_law_id = 33024
        test_params.mm_slot = 2
        
        generate_outputs(test_params)
        self._verify_outputs("Test_13", test_params, check_paf=True, check_bin=True)

    def test_case_14(self, test_params: ActivityParams):
        """This covers Case 14 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        Repeat Sequence Update"""
        test_params.activity = "Repeat Sequence"
        test_params.repeat_sequence_id = 16384
        
        generate_outputs(test_params)
        self._verify_outputs("Test_14", test_params, check_paf=True)

    def test_case_15(self, test_params: ActivityParams):
        """This covers Case 15 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        Scan Encoder LUT Update Nominal"""
        test_params.activity = "Scan Encoder LUT"
        test_params.scan_encode_correction_lut_id = 1
        test_params.side = "Nominal"
        
        generate_outputs(test_params)
        self._verify_outputs("Test_15", test_params, check_tc=True)

    def test_case_16(self, test_params: ActivityParams):
        """This covers Case 16 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        Scan Encoder LUT Update Nominal"""
        test_params.activity = "Scan Encoder LUT"
        test_params.scan_encode_correction_lut_id = 1
        test_params.side = "Redundant"
        
        generate_outputs(test_params)
        self._verify_outputs("Test_16", test_params, check_tc=True)

    def _verify_outputs(
        self, 
        test_dir: str, 
        test_params: ActivityParams,
        check_paf: bool = False,
        check_img: bool = False,
        check_bin: bool = False,
        check_tc: bool = False
    ) -> None:
        """Helper method to verify test outputs against expected results.
        
        Args:
            test_dir: Test directory name (e.g., "Test_1")
            test_params: Current test parameters
            check_paf: Whether to verify PAF files
            check_img: Whether to verify IMG files
            check_bin: Whether to verify BIN files
            check_tc: Whether to verify TC Stack files
        """
        str_path_expected = os.path.join(
            config.CONFIG_VALUES["test_files"],
            test_params.instrument,
            "Output",
            test_dir
        )
        str_path_run = os.path.join(
            config.CONFIG_VALUES["output_folder"],
            test_params.satellites[0]
        )

        if check_paf:
            lst_path_expected = basics.files_in_dir(str_path_expected, ".xml", bln_full_path=True)
            lst_path_run = basics.files_in_dir(str_path_run, ".xml", bln_full_path=True)
            assert assert_paf_files(lst_path_expected, lst_path_run)

        if check_img:
            lst_path_expected = basics.files_in_dir(str_path_expected, ".IMG", bln_full_path=True)
            lst_path_run = basics.files_in_dir(str_path_run, ".IMG", bln_full_path=True)
            logger.info(f"Comparing IMG files for test: {test_dir}")
            logger.info(f"Expected IMG files: {lst_path_expected}")
            logger.info(f"Generated IMG files: {lst_path_run}")
            assert assert_img_files(lst_path_expected, lst_path_run)

        if check_bin:
            lst_path_expected = basics.files_in_dir(str_path_expected, ".bin", bln_full_path=True)
            lst_path_run = basics.files_in_dir(str_path_run, ".bin", bln_full_path=True)
            assert assert_bin_files(lst_path_expected, lst_path_run)

        if check_tc:
            lst_path_expected = basics.files_in_dir(str_path_expected, "TC_Stack", bln_full_path=True)
            lst_path_run = basics.files_in_dir(str_path_run, "TC_Stack", bln_full_path=True)
            assert assert_TC_stacks(lst_path_expected, lst_path_run)


        
    def test_case_pixel_mapping_verify(self, test_params: ActivityParams):
        """Run the pixel mapping test and compare output IMG files line by line with expected files, ignoring CREATIONDATE lines."""
        test_params.side = "Nominal"
        test_params.icid = 20480
        test_params.icid_ver = 0
        generate_outputs(
            test_params,
            lst_channel=config.CONFIG_INSTRUMENT[test_params.instrument]["channel_names"],
            lst_part=["Pixel Mapping"],
            str_VCU_image_format="Combined Image",
        )

        # Get expected and run IMG file lists
        str_path_expected = os.path.join(
            config.CONFIG_VALUES["test_files"],
            test_params.instrument,
            "Output",
            "test_case_pixel_mapping_verify"
        )
        str_path_run = os.path.join(
            config.CONFIG_VALUES["output_folder"],
            test_params.satellites[0]
        )
        lst_path_expected = basics.files_in_dir(str_path_expected, ".IMG", bln_full_path=True)
        lst_path_run = basics.files_in_dir(str_path_run, ".IMG", bln_full_path=True)

        # Sort to ensure matching order
        lst_path_expected.sort()
        lst_path_run.sort()
        assert len(lst_path_expected) == len(lst_path_run), f"Number of expected IMG files ({len(lst_path_expected)}) does not match generated ({len(lst_path_run)})"

        for exp_file, run_file in zip(lst_path_expected, lst_path_run):
            with open(exp_file, 'r') as f_exp, open(run_file, 'r') as f_run:
                exp_lines = f_exp.readlines()
                run_lines = f_run.readlines()
            assert len(exp_lines) == len(run_lines), f"File {os.path.basename(exp_file)}: line count mismatch ({len(exp_lines)} vs {len(run_lines)})"
            for i, (exp_line, run_line) in enumerate(zip(exp_lines, run_lines), 1):
                # Ignore lines starting with CREATIONDATE=
                if exp_line.strip().startswith("CREATIONDATE=") and run_line.strip().startswith("CREATIONDATE="):
                    continue
                assert exp_line == run_line, f"Mismatch in file {os.path.basename(exp_file)} at line {i}:\nExpected: {exp_line}\nGot:      {run_line}"
