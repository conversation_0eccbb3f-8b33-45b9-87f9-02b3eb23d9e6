["tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_1", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_10", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_11", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_12", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_13", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_14", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_15", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_16", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_2", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_3", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_4", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_5", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_6", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_7", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_8", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_9", "tests/algorithms/fci_functions/test_integration_FCI.py::Test_FCI::test_case_pixel_mapping_verify", "tests/algorithms/irs_functions/test_integration_IRS.py::Test_IRS::test_IRS_ACTTAB", "tests/algorithms/irs_functions/test_integration_IRS.py::Test_IRS::test_IRS_SAV_generation", "tests/algorithms/irs_functions/test_integration_IRS.py::Test_IRS::test_IRS_SELUT_scae1_lut1", "tests/algorithms/irs_functions/test_integration_IRS.py::Test_IRS::test_IRS_SL_generation", "tests/algorithms/irs_functions/test_integration_IRS.py::Test_IRS::test_IRS_SL_generation_16384_5", "tests/algorithms/irs_functions/test_integration_IRS.py::test_IRS_SAV_acttabid_16555", "tests/algorithms/irs_functions/test_integration_IRS.py::test_irs_sav_paf_date_update", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_1", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_10", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_12_1", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_12_2", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_13", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_15", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_16", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_2", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_3_a", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_3_b", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_4", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_5", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_6", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_7", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_8", "tests/algorithms/li_functions/test_integration_LI.py::Test_LI::test_case_9", "tests/algorithms/uvn_functions/converter/test_excel_converter.py::TestExcelConverter::test_excel_file_conversion", "tests/algorithms/uvn_functions/converter/test_memory_image.py::TestMemoryImgConverter::test_memory_img_conversion", "tests/algorithms/uvn_functions/converter/test_netcdf_converter.py::TestNetcdfConverter::test_convert_netcdf", "tests/algorithms/uvn_functions/integrationTests/test_excel_generic.py::TestExcelIntegration::test_img_memory", "tests/algorithms/uvn_functions/integrationTests/test_excel_generic.py::TestExcelIntegration::test_netcdf", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_mem_EEPROM_A_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_mem_EEPROM_A_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_mem_EEPROM_A_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_mem_EEPROM_A_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_mem_EEPROM_B_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_mem_EEPROM_B_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_mem_EEPROM_B_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_mem_EEPROM_B_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_memory_SDRAM_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_memory_SDRAM_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_memory_SDRAM_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_excel_memoryImage_types.py::TestExcelMemoryImageTypesIntegration::test_img_memory_SDRAM_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_mem_EEPROM_A_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_mem_EEPROM_A_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_mem_EEPROM_A_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_mem_EEPROM_A_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_mem_EEPROM_B_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_mem_EEPROM_B_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_mem_EEPROM_B_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_mem_EEPROM_B_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_memory_SDRAM_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_memory_SDRAM_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_memory_SDRAM_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_excel.py::TestMemoryImageTypesExcelIntegration::test_img_memory_SDRAM_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_mem_EEPROM_A_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_mem_EEPROM_A_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_mem_EEPROM_A_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_mem_EEPROM_A_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_mem_EEPROM_B_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_mem_EEPROM_B_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_mem_EEPROM_B_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_mem_EEPROM_B_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_memory_SDRAM_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_memory_SDRAM_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_memory_SDRAM_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_memory_images_netcdf.py::TestMemoryImageTypesNetcdfIntegration::test_img_memory_SDRAM_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_generic.py::TestNetcdfIntegration::test_excel", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_generic.py::TestNetcdfIntegration::test_img_memory", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_mem_EEPROM_A_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_mem_EEPROM_A_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_mem_EEPROM_A_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_mem_EEPROM_A_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_mem_EEPROM_B_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_mem_EEPROM_B_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_mem_EEPROM_B_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_mem_EEPROM_B_ICU_B_SSW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_memory_SDRAM_ICU_A_ASW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_memory_SDRAM_ICU_A_SSW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_memory_SDRAM_ICU_B_ASW", "tests/algorithms/uvn_functions/integrationTests/test_netcdf_memoryImage_types.py::TestNetcdfMemoryImageTypesIntegration::test_img_memory_SDRAM_ICU_B_SSW", "tests/algorithms/uvn_functions/utils/test_bit_dec_converter.py::TestBitDecimalConverter::test_convert_bit_to_dec", "tests/algorithms/uvn_functions/utils/test_bit_dec_converter.py::TestBitDecimalConverter::test_convert_dec_to_bit", "tests/algorithms/uvn_functions/utils/test_bit_dec_converter.py::TestBitDecimalConverter::test_convert_list_bit_to_dec", "tests/algorithms/uvn_functions/utils/test_bit_dec_converter.py::TestBitDecimalConverter::test_convert_list_dec_to_bit", "tests/gui/test_FCI_GUI.py::test_fci_gui_handle_activity", "tests/gui/test_IRS_GUI.py::test_irs_gui_handle_activity", "tests/gui/test_LI_GUI.py::test_li_gui_handle_activity", "tests/gui/test_UVN_GUI.py::test_uvn_gui_handle_activity", "tests/utils/test_checksum.py::TestChecksum::test_CRC16", "tests/utils/test_checksum.py::TestChecksum::test_fletcher16", "tests/utils/test_conversion_utils.py::test_dec2hex", "tests/utils/test_conversion_utils.py::test_hex2dec", "tests/utils/test_xml_utils.py::TestCheckXML::test_non_test_mode", "tests/utils/test_xml_utils.py::TestCheckXML::test_valid_xml", "tests/utils/test_xml_utils.py::TestCheckXML::test_validation_error"]