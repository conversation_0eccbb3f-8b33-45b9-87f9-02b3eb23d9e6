#!/usr/bin/env python3
"""
Test the CDC input format display for XLDT files.
"""

import os
from xldt_reader_standalone import XLDTReader, format_xldt_cdc_input_style

def test_cdc_format():
    """Test CDC input format display with a real XLDT file."""
    
    # Configuration and input file
    config_path = "../config/IRS/IRS_SL_Conf.csv"
    xldt_file = "inputs/ScanLaw_Central_Summer_16384.xldt"
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return
    
    if not os.path.exists(xldt_file):
        print(f"❌ XLDT file not found: {xldt_file}")
        return
    
    print("🔍 Testing CDC Input Format Display")
    print("=" * 50)
    
    try:
        # Create reader and parse file
        reader = XLDTReader(config_path=config_path)
        parsed_data = reader.read_xldt_file(xldt_file)
        
        # Display in CDC format
        cdc_format = format_xldt_cdc_input_style(parsed_data)
        print(cdc_format)
        
        print(f"\n✅ CDC format display completed successfully")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cdc_format()
