#!/usr/bin/env python3
"""
Search all scan law IDs to find the one that matches the expected pattern.
"""

import os
import sys
import numpy as np

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    import netCDF4 as nc
except ImportError:
    print("❌ netCDF4 not available")
    sys.exit(1)

def find_matching_pattern():
    """Search all scan law IDs for the expected pattern."""
    
    netcdf_file = "../assets/Input_Files/IRS/MTS1_IRS_SCANLAW_0000_v2.nc"
    
    if not os.path.exists(netcdf_file):
        print(f"❌ NetCDF file not found: {netcdf_file}")
        return
    
    print("🔍 Searching for Expected Pattern in All Scan Laws")
    print("=" * 60)
    
    expected_pattern = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]
    print(f"Expected pattern: {expected_pattern}")
    print("=" * 60)
    
    try:
        with nc.Dataset(netcdf_file, 'r') as dataset:
            # Get scan law IDs
            scan_law_ids = dataset.variables['scan_law_id'][:]
            print(f"Available scan law IDs: {scan_law_ids}")
            
            # Check each scan law ID
            for i, scan_law_id in enumerate(scan_law_ids):
                print(f"\n🔍 Checking Scan Law ID {scan_law_id} (index {i}):")
                print("-" * 40)
                
                # Get FDA pointer data
                fda_alpha = dataset.variables['fda_mp_pointer_alpha'][i, :]
                fda_epsilon = dataset.variables['fda_mp_pointer_epsilon'][i, :]
                
                # Convert to regular arrays if masked
                if hasattr(fda_alpha, 'data'):
                    fda_alpha = fda_alpha.data
                if hasattr(fda_epsilon, 'data'):
                    fda_epsilon = fda_epsilon.data
                
                # Remove filler values (255)
                filler_pos_alpha = np.where(fda_alpha == 255)[0]
                if len(filler_pos_alpha) > 0:
                    valid_alpha = fda_alpha[:filler_pos_alpha[0]]
                else:
                    valid_alpha = fda_alpha
                
                filler_pos_epsilon = np.where(fda_epsilon == 255)[0]
                if len(filler_pos_epsilon) > 0:
                    valid_epsilon = fda_epsilon[:filler_pos_epsilon[0]]
                else:
                    valid_epsilon = fda_epsilon
                
                # Check first 10 values of alpha
                actual_alpha = valid_alpha[:10] if len(valid_alpha) >= 10 else valid_alpha
                actual_epsilon = valid_epsilon[:10] if len(valid_epsilon) >= 10 else valid_epsilon
                
                print(f"   Alpha first 10:   {list(actual_alpha)}")
                print(f"   Epsilon first 10: {list(actual_epsilon)}")
                
                # Check if alpha matches expected pattern
                if len(actual_alpha) >= len(expected_pattern):
                    if np.array_equal(actual_alpha[:len(expected_pattern)], expected_pattern):
                        print(f"   ✅ ALPHA MATCHES expected pattern!")
                        print(f"   Full alpha array: {list(valid_alpha)}")
                        
                        # Save this data
                        with open(f"matching_scanlaw_{scan_law_id}_data.txt", "w") as f:
                            f.write(f"Scan Law ID: {scan_law_id}\n")
                            f.write(f"fda_mp_pointer_alpha = {list(valid_alpha)}\n")
                            f.write(f"fda_mp_pointer_epsilon = {list(valid_epsilon)}\n")
                        
                        print(f"   💾 Saved to matching_scanlaw_{scan_law_id}_data.txt")
                    else:
                        # Show differences
                        differences = []
                        for j, (expected, actual) in enumerate(zip(expected_pattern, actual_alpha)):
                            if expected != actual:
                                differences.append(f"pos {j}: expected {expected}, got {actual}")
                        if differences:
                            print(f"   ❌ Alpha differences: {'; '.join(differences[:3])}")
                
                # Check if epsilon matches expected pattern
                if len(actual_epsilon) >= len(expected_pattern):
                    if np.array_equal(actual_epsilon[:len(expected_pattern)], expected_pattern):
                        print(f"   ✅ EPSILON MATCHES expected pattern!")
                        print(f"   Full epsilon array: {list(valid_epsilon)}")
                    else:
                        # Show differences for epsilon too
                        differences = []
                        for j, (expected, actual) in enumerate(zip(expected_pattern, actual_epsilon)):
                            if expected != actual:
                                differences.append(f"pos {j}: expected {expected}, got {actual}")
                        if differences:
                            print(f"   ❌ Epsilon differences: {'; '.join(differences[:3])}")
                
                # Show valid data lengths
                print(f"   Valid lengths: alpha={len(valid_alpha)}, epsilon={len(valid_epsilon)}")
            
            print(f"\n📊 Summary:")
            print(f"   Searched {len(scan_law_ids)} scan law IDs")
            print(f"   Expected pattern: {expected_pattern}")
            print(f"   Check output files for any matches found")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    find_matching_pattern()
