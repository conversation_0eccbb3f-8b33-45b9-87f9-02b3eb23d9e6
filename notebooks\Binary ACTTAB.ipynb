{"cells": [{"cell_type": "markdown", "id": "6fc40522-c996-457f-9bde-a629bf6dfd5c", "metadata": {}, "source": ["In this notebook we convert an example ACTTAB from tcl to the binary representation we need to calculate its checksum for the telecommands for uploading it.\n", "\n", "According to Sec. 5.4.8 of [MTG-SSF-CISW-ICD-0095]:\n", "_\"The octet sequence over which the Checksum is calculated for the Activity Table corresponds to the format of the Activity Table Report TM defined in section 5.4.8, but omitting the intial “Act Tab Status” field and the final “Checksum” field.\"_"]}, {"cell_type": "markdown", "id": "e84fec70-a5e5-490f-8ced-607c5fbf55f2", "metadata": {}, "source": ["# Generating the Activity Sequences for the LAC Definitions"]}, {"cell_type": "code", "execution_count": 1, "id": "cee9446c-7400-4b69-9d7d-1521bc56a533", "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "from enum import Enum\n", "\n", "from icecream import ic\n", "\n", "def print_bytes(seq: bytes) -> None:\n", "    print(' '.join(f\"{b:0>2X}\" for b in seq))"]}, {"cell_type": "code", "execution_count": 2, "id": "f01351c6-0001-4d84-8851-c6142de92c69", "metadata": {}, "outputs": [], "source": ["class ActType(Enum):\n", "    MOVE_SCA = 0\n", "    MOVE_CCM = 1\n", "    MOVE_COM = 2\n", "    CHECK_SCA = 3\n", "    CHECK_COM = 4\n", "\n", "@dataclass\n", "class Activity:\n", "    acttype: ActType\n", "    parameter: int  # 1 byte\n", "    time_offset: int  # 3 bytes\n", "\n", "    def to_bytes(self) -> bytes:\n", "        return (\n", "            self.acttype.value.to_bytes(1, 'big')\n", "            + self.parameter.to_bytes(1, 'big')\n", "            + self.time_offset.to_bytes(3, 'big')\n", "        )\n", "\n", "    def __repr__(self):\n", "        return f\"<Activity: {self.acttype.name:<9} {self.parameter:>3} at t={self.time_offset}ms>\""]}, {"cell_type": "code", "execution_count": 3, "id": "e3e48568-c0c4-42a1-9940-095e81455398", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["01 0A 00 0D AC\n"]}], "source": ["print_bytes(Activity(ActType.MOVE_CCM, 10, 3500).to_bytes())"]}, {"cell_type": "code", "execution_count": 4, "id": "b04c048f-6f94-4c03-ae9b-176af9575e2d", "metadata": {}, "outputs": [], "source": ["from parse import parse\n", "\n", "parse_pattern = '[DSWH0APX \"{acttype}\"] [DSWH0AQX {parameter}] [DSWH0ARX {time_offset}]'\n", "def parse_line(line: str) -> Activity:\n", "    result = parse(parse_pattern, line.strip().rstrip('\\\\').replace('{', '[').replace('}', ']')).named\n", "    if not result:\n", "        raise ValueError(f\"Can't parse line \\\"{line}\\\"\")\n", "    return Activity(ActType[result['acttype']], int(result['parameter']), int(result['time_offset']))\n", "\n", "def parse_lac_activities(filename: str) -> list[Activity]:\n", "    with open(filename, 'r') as f:\n", "        text = f.read()\n", "    return [parse_line(line) for line in text.splitlines()]\n", "\n", "lac_1_activities = parse_lac_activities('lac1.txt')\n", "lac_2_activities = parse_lac_activities('lac2.txt')\n", "lac_3_activities = parse_lac_activities('lac3.txt')\n", "lac_4_activities = parse_lac_activities('lac4.txt')"]}, {"cell_type": "markdown", "id": "5275e636-4e79-4dfc-a7f6-a51ca9f3a6e2", "metadata": {}, "source": ["# Generating the Binary Snippets"]}, {"cell_type": "code", "execution_count": 5, "id": "ad4102b7-c176-4062-bfb4-0b4985a03db2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["52 D1 04\n"]}], "source": ["def generate_acttab_header(acttab_id: int, num_lacs: int) -> bytes:\n", "    \"\"\"Generates the header byte sequence for activity tables in the \"Activity Table Report TC\" format required for\n", "    computing the checksum.\n", "    \n", "    `acttab_id` is the 16-bit ID of the activity table that encodes some of its metadata and `num_lacs` is the number\n", "    of LAC definitions in the activity table.\"\"\"\n", "    \n", "    assert acttab_id > 0 and acttab_id < 2**16, \"ACTTAB ID is an unsigned 16-bit integer\"\n", "    assert num_lacs >= 1 and num_lacs <= 16, \"Num LAC ranges from 1 to 16\"\n", "\n", "    return acttab_id.to_bytes(2, 'big') + num_lacs.to_bytes(1, 'big')  # TODO check endianness\n", "\n", "header = generate_acttab_header(acttab_id=21201, num_lacs=4)\n", "print_bytes(header)"]}, {"cell_type": "code", "execution_count": 6, "id": "08367bb7-ca39-463e-9142-116422489614", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["03 4E 00 02 00 F4\n"]}], "source": ["def generate_lac_definition_header(lac_id: int, num_dwells: int, first_dwell_id: int, num_acts: int):\n", "    assert lac_id >= 3 and lac_id <= 6, \"LAC ID ranges from 3 to 6\"\n", "    assert num_dwells >= 1 and num_dwells <= 85, \"Number of dwells in a LAC ranges from 1 to 85\"\n", "    assert num_acts >= 1 and num_acts <= 320, \"Number of activities in a LAC ranges from 1 to 320\"\n", "    assert first_dwell_id >= 1 and first_dwell_id <= 350, \"Dwell IDs range from 1 to 350\"\n", "\n", "    return (\n", "        lac_id.to_bytes(1, 'big')\n", "        + num_dwells.to_bytes(1, 'big')\n", "        + first_dwell_id.to_bytes(2, 'big')\n", "        + num_acts.to_bytes(2, 'big')\n", "    )\n", "\n", "lac_1_header = generate_lac_definition_header(lac_id=3, num_dwells=78, first_dwell_id=2, num_acts=244)\n", "print_bytes(lac_1_header)"]}, {"cell_type": "code", "execution_count": 7, "id": "e8b19b5b-6a09-4494-8b0c-8ac2667cfb78", "metadata": {}, "outputs": [], "source": ["def generate_activity_list_bytes(activities: list[Activity]) -> bytes:\n", "    out = bytes()\n", "    for activity in activities:\n", "        out += activity.to_bytes()\n", "    return out"]}, {"cell_type": "markdown", "id": "235f70f3-33d8-4e91-a647-ce0b6c176f2c", "metadata": {}, "source": ["# Let's Try the Checksum"]}, {"cell_type": "code", "execution_count": 15, "id": "7afbf811-101d-4e74-a7ca-14d78b2dab8c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["01 52 D1 04 03 4E 00 02 00 F4 02 80 00 00 00 01 50 00 0B B8 04 80 00 0F 3C 01 40 00 3A 34 01 50 00 63 38 00 00 00 6F AC 01 40 00 8C 3C 02 00 00 B3 B0 03 00 00 B6 C4 01 10 00 C0 F8 04 00 00 C2 EC 00 00 00 E8 60 01 00 00 EC 54 03 00 00 F0 F8 00 00 01 13 BC 01 10 01 15 58 03 00 01 19 FC 00 00 01 3C C0 01 00 01 3E 5C 03 00 01 43 00 00 00 01 65 C4 01 10 01 67 60 03 00 01 6C 04 00 00 01 8E C8 01 00 01 90 64 03 00 01 95 08 00 00 01 B7 CC 01 10 01 B9 68 03 00 01 BE 0C 00 00 01 E0 D0 01 00 01 E2 6C 03 00 01 E7 10 00 00 02 09 D4 01 10 02 0B 70 03 00 02 10 14 00 00 02 32 D8 01 60 02 39 24 03 00 02 3D C8 00 00 02 60 8C 01 10 02 63 54 03 00 02 67 F8 00 00 02 8A BC 01 00 02 8C 58 03 00 02 90 FC 00 00 02 B3 C0 01 10 02 B5 5C 03 00 02 BA 00 00 00 02 DC C4 01 00 02 DE 60 03 00 02 E3 04 00 00 03 05 C8 01 10 03 07 64 03 00 03 0C 08 00 00 03 2E CC 01 00 03 30 68 03 00 03 35 0C 00 00 03 57 D0 01 10 03 59 6C 03 00 03 5E 10 00 00 03 80 D4 01 00 03 82 70 03 00 03 87 14 00 00 03 A9 D8 01 10 03 AB 74 03 00 03 B0 18 00 00 03 D2 DC 01 00 03 D4 78 03 00 03 D9 1C 00 00 03 FB E0 01 10 03 FD 7C 03 00 04 02 20 00 00 04 24 E4 01 60 04 2B 30 03 00 04 2F D4 00 00 04 52 98 01 10 04 54 FC 03 00 04 59 A0 00 00 04 7C 64 01 00 04 7E 00 03 00 04 82 A4 00 00 04 A5 68 01 10 04 A7 04 03 00 04 AB A8 00 00 04 CE 6C 01 00 04 D0 08 03 00 04 D4 AC 00 00 04 F7 70 01 10 04 F9 0C 03 00 04 FD B0 00 00 05 20 74 01 00 05 22 10 03 00 05 26 B4 00 00 05 49 78 01 10 05 4B 14 03 00 05 4F B8 00 00 05 72 7C 01 00 05 74 18 03 00 05 78 BC 00 00 05 9B 80 01 10 05 9D 1C 03 00 05 A1 C0 00 00 05 C4 84 01 00 05 C6 20 03 00 05 CA C4 00 00 05 ED 88 01 10 05 EF 24 03 00 05 F3 C8 00 00 06 16 8C 01 00 06 18 28 03 00 06 1C CC 00 00 06 3F 90 01 10 06 41 2C 03 00 06 45 D0 00 00 06 68 94 01 60 06 6E E0 03 00 06 73 84 00 00 06 96 48 01 10 06 98 AC 03 00 06 9D 50 00 00 06 C0 14 01 00 06 C1 B0 03 00 06 C6 54 00 00 06 E9 18 01 10 06 EA B4 03 00 06 EF 58 00 00 07 12 1C 01 00 07 13 B8 03 00 07 18 5C 00 00 07 3B 20 01 10 07 3C BC 03 00 07 41 60 00 00 07 64 24 01 00 07 65 C0 03 00 07 6A 64 00 00 07 8D 28 01 10 07 8E C4 03 00 07 93 68 00 00 07 B6 2C 01 00 07 B7 C8 03 00 07 BC 6C 00 00 07 DF 30 01 10 07 E0 CC 03 00 07 E5 70 00 00 08 08 34 01 00 08 09 D0 03 00 08 0E 74 00 00 08 31 38 01 10 08 32 D4 03 00 08 37 78 00 00 08 5A 3C 01 00 08 5B D8 03 00 08 60 7C 00 00 08 83 40 01 10 08 84 DC 03 00 08 89 80 00 00 08 AC 44 01 00 08 AD E0 03 00 08 B2 84 00 00 08 D5 48 01 10 08 D6 E4 03 00 08 DB 88 00 00 08 FE 4C 01 60 09 04 98 03 00 09 09 3C 00 00 09 2C 00 01 10 09 2E 64 03 00 09 33 08 00 00 09 55 CC 01 00 09 57 68 03 00 09 5C 0C 00 00 09 7E D0 01 10 09 80 6C 03 00 09 85 10 00 00 09 A7 D4 01 00 09 A9 70 03 00 09 AE 14 00 00 09 D0 D8 01 10 09 D2 74 03 00 09 D7 18 00 00 09 F9 DC 01 00 09 FB 78 03 00 0A 00 1C 00 00 0A 22 E0 01 10 0A 24 7C 03 00 0A 29 20 00 00 0A 4B E4 01 00 0A 4D 80 03 00 0A 52 24 00 00 0A 74 E8 01 10 0A 76 84 03 00 0A 7B 28 00 00 0A 9D EC 01 00 0A 9F 88 03 00 0A A4 2C 00 00 0A C6 F0 01 10 0A C8 8C 03 00 0A CD 30 00 00 0A EF F4 01 00 0A F1 90 03 00 0A F6 34 00 00 0B 18 F8 01 10 0B 1A 94 03 00 0B 1F 38 00 00 0B 41 FC 01 00 0B 43 98 03 00 0B 48 3C 00 00 0B 6B 00 01 10 0B 6C 9C 03 00 0B 71 40 00 00 0B 94 04 01 00 0B 95 A0 03 00 0B 9A 44 00 00 0B BD 08 01 70 0B C3 54 03 00 0B C7 F8 00 00 0B EA BC 01 00 0B ED 20 03 00 0B F1 C4 00 00 0C 14 88 01 10 0C 16 24 03 00 0C 1A C8 00 00 0C 3D 8C 01 00 0C 3F 28 03 00 0C 43 CC 00 00 0C 66 90 01 10 0C 68 2C 03 00 0C 6C D0 00 00 0C 8F 94 01 00 0C 91 30 03 00 0C 95 D4 00 00 0C B8 98 01 10 0C BA 34 03 00 0C BE D8 00 00 0C E1 9C 01 00 0C E3 38 03 00 0C E7 DC 00 00 0D 0A A0 02 40 0D 0A AC 01 30 0D 16 64 04 40 0D 19 E8 03 00 0D 39 80 01 20 0D 40 94 01 30 0D 69 98 01 20 0D 92 9C 04 4D 00 4F 00 F2 02 80 00 00 00 01 50 00 0B B8 04 80 00 0F 3C 01 40 00 3A 34 01 50 00 63 38 01 40 00 8C 3C 00 00 00 B0 E8 01 50 00 B5 40 02 00 00 DC B4 03 00 00 DF C8 01 00 00 E9 FC 04 00 00 EB F0 00 00 01 11 64 01 10 01 15 58 03 00 01 19 FC 00 00 01 3C C0 01 00 01 3E 5C 03 00 01 43 00 00 00 01 65 C4 01 10 01 67 60 03 00 01 6C 04 00 00 01 8E C8 01 00 01 90 64 03 00 01 95 08 00 00 01 B7 CC 01 10 01 B9 68 03 00 01 BE 0C 00 00 01 E0 D0 01 00 01 E2 6C 03 00 01 E7 10 00 00 02 09 D4 01 10 02 0B 70 03 00 02 10 14 00 00 02 32 D8 01 00 02 34 74 03 00 02 39 18 00 00 02 5B DC 01 10 02 5D 78 03 00 02 62 1C 00 00 02 84 E0 01 00 02 86 7C 03 00 02 8B 20 00 00 02 AD E4 01 70 02 B4 30 03 00 02 B8 D4 00 00 02 DB 98 01 00 02 DD FC 03 00 02 E2 A0 00 00 03 05 64 01 10 03 07 00 03 00 03 0B A4 00 00 03 2E 68 01 00 03 30 04 03 00 03 34 A8 00 00 03 57 6C 01 10 03 59 08 03 00 03 5D AC 00 00 03 80 70 01 00 03 82 0C 03 00 03 86 B0 00 00 03 A9 74 01 10 03 AB 10 03 00 03 AF B4 00 00 03 D2 78 01 00 03 D4 14 03 00 03 D8 B8 00 00 03 FB 7C 01 10 03 FD 18 03 00 04 01 BC 00 00 04 24 80 01 00 04 26 1C 03 00 04 2A C0 00 00 04 4D 84 01 10 04 4F 20 03 00 04 53 C4 00 00 04 76 88 01 00 04 78 24 03 00 04 7C C8 00 00 04 9F 8C 01 10 04 A1 28 03 00 04 A5 CC 00 00 04 C8 90 01 00 04 CA 2C 03 00 04 CE D0 00 00 04 F1 94 01 10 04 F3 30 03 00 04 F7 D4 00 00 05 1A 98 01 00 05 1C 34 03 00 05 20 D8 00 00 05 43 9C 01 10 05 45 38 03 00 05 49 DC 00 00 05 6C A0 01 00 05 6E 3C 03 00 05 72 E0 00 00 05 95 A4 01 10 05 97 40 03 00 05 9B E4 00 00 05 BE A8 01 60 05 C4 F4 03 00 05 C9 98 00 00 05 EC 5C 01 10 05 EE C0 03 00 05 F3 64 00 00 06 16 28 01 00 06 17 C4 03 00 06 1C 68 00 00 06 3F 2C 01 10 06 40 C8 03 00 06 45 6C 00 00 06 68 30 01 00 06 69 CC 03 00 06 6E 70 00 00 06 91 34 01 10 06 92 D0 03 00 06 97 74 00 00 06 BA 38 01 00 06 BB D4 03 00 06 C0 78 00 00 06 E3 3C 01 10 06 E4 D8 03 00 06 E9 7C 00 00 07 0C 40 01 00 07 0D DC 03 00 07 12 80 00 00 07 35 44 01 10 07 36 E0 03 00 07 3B 84 00 00 07 5E 48 01 00 07 5F E4 03 00 07 64 88 00 00 07 87 4C 01 10 07 88 E8 03 00 07 8D 8C 00 00 07 B0 50 01 00 07 B1 EC 03 00 07 B6 90 00 00 07 D9 54 01 10 07 DA F0 03 00 07 DF 94 00 00 08 02 58 01 00 08 03 F4 03 00 08 08 98 00 00 08 2B 5C 01 10 08 2C F8 03 00 08 31 9C 00 00 08 54 60 01 00 08 55 FC 03 00 08 5A A0 00 00 08 7D 64 01 10 08 7F 00 03 00 08 83 A4 00 00 08 A6 68 01 00 08 A8 04 03 00 08 AC A8 00 00 08 CF 6C 01 70 08 D5 B8 03 00 08 DA 5C 00 00 08 FD 20 01 00 09 00 4C 03 00 09 04 F0 00 00 09 27 B4 01 10 09 29 50 03 00 09 2D F4 00 00 09 50 B8 01 00 09 52 54 03 00 09 56 F8 00 00 09 79 BC 01 10 09 7B 58 03 00 09 7F FC 00 00 09 A2 C0 01 00 09 A4 5C 03 00 09 A9 00 00 00 09 CB C4 01 10 09 CD 60 03 00 09 D2 04 00 00 09 F4 C8 01 00 09 F6 64 03 00 09 FB 08 00 00 0A 1D CC 01 10 0A 1F 68 03 00 0A 24 0C 00 00 0A 46 D0 01 00 0A 48 6C 03 00 0A 4D 10 00 00 0A 6F D4 01 10 0A 71 70 03 00 0A 76 14 00 00 0A 98 D8 01 00 0A 9A 74 03 00 0A 9F 18 00 00 0A C1 DC 01 10 0A C3 78 03 00 0A C8 1C 00 00 0A EA E0 01 00 0A EC 7C 03 00 0A F1 20 00 00 0B 13 E4 01 10 0B 15 80 03 00 0B 1A 24 00 00 0B 3C E8 01 00 0B 3E 84 03 00 0B 43 28 00 00 0B 65 EC 01 10 0B 67 88 03 00 0B 6C 2C 00 00 0B 8E F0 01 00 0B 90 8C 03 00 0B 95 30 00 00 0B B7 F4 01 10 0B B9 90 03 00 0B BE 34 00 00 0B E0 F8 01 60 0B E7 44 03 00 0B EB E8 00 00 0C 0E AC 01 10 0C 11 D8 03 00 0C 16 7C 00 00 0C 39 40 01 00 0C 3A DC 03 00 0C 3F 80 00 00 0C 62 44 01 10 0C 63 E0 03 00 0C 68 84 00 00 0C 8B 48 01 00 0C 8C E4 03 00 0C 91 88 00 00 0C B4 4C 01 10 0C B5 E8 03 00 0C BA 8C 00 00 0C DD 50 01 00 0C DE EC 03 00 0C E3 90 00 00 0D 06 54 02 40 0D 06 60 01 30 0D 12 18 03 00 0D 15 2C 04 40 0D 15 9C 01 20 0D 3C AC 01 30 0D 65 B0 01 20 0D 8E B4 05 4D 00 9B 00 F2 02 80 00 00 00 01 50 00 0B B8 04 80 00 0F 3C 01 40 00 3A 34 01 50 00 63 38 01 40 00 8C 3C 00 00 00 A9 7C 02 00 00 B3 B0 03 00 00 B6 C4 01 10 00 C0 F8 04 00 00 C2 EC 00 00 00 E8 60 01 00 00 EC 54 03 00 00 F0 F8 00 00 01 13 BC 01 10 01 15 58 03 00 01 19 FC 00 00 01 3C C0 01 00 01 3E 5C 03 00 01 43 00 00 00 01 65 C4 01 10 01 67 60 03 00 01 6C 04 00 00 01 8E C8 01 00 01 90 64 03 00 01 95 08 00 00 01 B7 CC 01 10 01 B9 68 03 00 01 BE 0C 00 00 01 E0 D0 01 00 01 E2 6C 03 00 01 E7 10 00 00 02 09 D4 01 10 02 0B 70 03 00 02 10 14 00 00 02 32 D8 01 00 02 34 74 03 00 02 39 18 00 00 02 5B DC 01 10 02 5D 78 03 00 02 62 1C 00 00 02 84 E0 01 00 02 86 7C 03 00 02 8B 20 00 00 02 AD E4 01 70 02 B4 30 03 00 02 B8 D4 00 00 02 DB 98 01 00 02 DD FC 03 00 02 E2 A0 00 00 03 05 64 01 10 03 07 00 03 00 03 0B A4 00 00 03 2E 68 01 00 03 30 04 03 00 03 34 A8 00 00 03 57 6C 01 10 03 59 08 03 00 03 5D AC 00 00 03 80 70 01 00 03 82 0C 03 00 03 86 B0 00 00 03 A9 74 01 10 03 AB 10 03 00 03 AF B4 00 00 03 D2 78 01 00 03 D4 14 03 00 03 D8 B8 00 00 03 FB 7C 01 10 03 FD 18 03 00 04 01 BC 00 00 04 24 80 01 00 04 26 1C 03 00 04 2A C0 00 00 04 4D 84 01 10 04 4F 20 03 00 04 53 C4 00 00 04 76 88 01 00 04 78 24 03 00 04 7C C8 00 00 04 9F 8C 01 10 04 A1 28 03 00 04 A5 CC 00 00 04 C8 90 01 00 04 CA 2C 03 00 04 CE D0 00 00 04 F1 94 01 10 04 F3 30 03 00 04 F7 D4 00 00 05 1A 98 01 00 05 1C 34 03 00 05 20 D8 00 00 05 43 9C 01 10 05 45 38 03 00 05 49 DC 00 00 05 6C A0 01 00 05 6E 3C 03 00 05 72 E0 00 00 05 95 A4 01 10 05 97 40 03 00 05 9B E4 00 00 05 BE A8 01 60 05 C4 F4 03 00 05 C9 98 00 00 05 EC 5C 01 10 05 EE C0 03 00 05 F3 64 00 00 06 16 28 01 00 06 17 C4 03 00 06 1C 68 00 00 06 3F 2C 01 10 06 40 C8 03 00 06 45 6C 00 00 06 68 30 01 00 06 69 CC 03 00 06 6E 70 00 00 06 91 34 01 10 06 92 D0 03 00 06 97 74 00 00 06 BA 38 01 00 06 BB D4 03 00 06 C0 78 00 00 06 E3 3C 01 10 06 E4 D8 03 00 06 E9 7C 00 00 07 0C 40 01 00 07 0D DC 03 00 07 12 80 00 00 07 35 44 01 10 07 36 E0 03 00 07 3B 84 00 00 07 5E 48 01 00 07 5F E4 03 00 07 64 88 00 00 07 87 4C 01 10 07 88 E8 03 00 07 8D 8C 00 00 07 B0 50 01 00 07 B1 EC 03 00 07 B6 90 00 00 07 D9 54 01 10 07 DA F0 03 00 07 DF 94 00 00 08 02 58 01 00 08 03 F4 03 00 08 08 98 00 00 08 2B 5C 01 10 08 2C F8 03 00 08 31 9C 00 00 08 54 60 01 00 08 55 FC 03 00 08 5A A0 00 00 08 7D 64 01 10 08 7F 00 03 00 08 83 A4 00 00 08 A6 68 01 00 08 A8 04 03 00 08 AC A8 00 00 08 CF 6C 01 70 08 D5 B8 03 00 08 DA 5C 00 00 08 FD 20 01 00 09 00 4C 03 00 09 04 F0 00 00 09 27 B4 01 10 09 29 50 03 00 09 2D F4 00 00 09 50 B8 01 00 09 52 54 03 00 09 56 F8 00 00 09 79 BC 01 10 09 7B 58 03 00 09 7F FC 00 00 09 A2 C0 01 00 09 A4 5C 03 00 09 A9 00 00 00 09 CB C4 01 10 09 CD 60 03 00 09 D2 04 00 00 09 F4 C8 01 00 09 F6 64 03 00 09 FB 08 00 00 0A 1D CC 01 10 0A 1F 68 03 00 0A 24 0C 00 00 0A 46 D0 01 00 0A 48 6C 03 00 0A 4D 10 00 00 0A 6F D4 01 10 0A 71 70 03 00 0A 76 14 00 00 0A 98 D8 01 00 0A 9A 74 03 00 0A 9F 18 00 00 0A C1 DC 01 10 0A C3 78 03 00 0A C8 1C 00 00 0A EA E0 01 00 0A EC 7C 03 00 0A F1 20 00 00 0B 13 E4 01 10 0B 15 80 03 00 0B 1A 24 00 00 0B 3C E8 01 00 0B 3E 84 03 00 0B 43 28 00 00 0B 65 EC 01 10 0B 67 88 03 00 0B 6C 2C 00 00 0B 8E F0 01 00 0B 90 8C 03 00 0B 95 30 00 00 0B B7 F4 01 10 0B B9 90 03 00 0B BE 34 00 00 0B E0 F8 01 60 0B E7 44 03 00 0B EB E8 00 00 0C 0E AC 01 10 0C 11 D8 03 00 0C 16 7C 00 00 0C 39 40 01 00 0C 3A DC 03 00 0C 3F 80 00 00 0C 62 44 01 10 0C 63 E0 03 00 0C 68 84 00 00 0C 8B 48 01 00 0C 8C E4 03 00 0C 91 88 00 00 0C B4 4C 01 10 0C B5 E8 03 00 0C BA 8C 00 00 0C DD 50 02 40 0C DD 5C 01 20 0C E9 14 04 40 0C EC 98 03 00 0D 0C 30 01 30 0D 13 A8 01 20 0D 3C AC 01 30 0D 65 B0 01 20 0D 8E B4 06 50 00 E7 00 F8 02 80 00 00 00 01 50 00 0B B8 04 80 00 0F 3C 01 40 00 3A 34 00 00 00 5E 18 01 50 00 63 38 02 00 00 8A AC 03 00 00 8D C0 01 00 00 97 F4 04 00 00 99 E8 00 00 00 BF 5C 01 10 00 C3 50 03 00 00 C7 F4 00 00 00 EA B8 01 00 00 EC 54 03 00 00 F0 F8 00 00 01 13 BC 01 10 01 15 58 03 00 01 19 FC 00 00 01 3C C0 01 00 01 3E 5C 03 00 01 43 00 00 00 01 65 C4 01 10 01 67 60 03 00 01 6C 04 00 00 01 8E C8 01 00 01 90 64 03 00 01 95 08 00 00 01 B7 CC 01 10 01 B9 68 03 00 01 BE 0C 00 00 01 E0 D0 01 00 01 E2 6C 03 00 01 E7 10 00 00 02 09 D4 01 10 02 0B 70 03 00 02 10 14 00 00 02 32 D8 01 00 02 34 74 03 00 02 39 18 00 00 02 5B DC 01 70 02 62 28 03 00 02 66 CC 00 00 02 89 90 01 00 02 8B F4 03 00 02 90 98 00 00 02 B3 5C 01 10 02 B4 F8 03 00 02 B9 9C 00 00 02 DC 60 01 00 02 DD FC 03 00 02 E2 A0 00 00 03 05 64 01 10 03 07 00 03 00 03 0B A4 00 00 03 2E 68 01 00 03 30 04 03 00 03 34 A8 00 00 03 57 6C 01 10 03 59 08 03 00 03 5D AC 00 00 03 80 70 01 00 03 82 0C 03 00 03 86 B0 00 00 03 A9 74 01 10 03 AB 10 03 00 03 AF B4 00 00 03 D2 78 01 00 03 D4 14 03 00 03 D8 B8 00 00 03 FB 7C 01 10 03 FD 18 03 00 04 01 BC 00 00 04 24 80 01 00 04 26 1C 03 00 04 2A C0 00 00 04 4D 84 01 10 04 4F 20 03 00 04 53 C4 00 00 04 76 88 01 00 04 78 24 03 00 04 7C C8 00 00 04 9F 8C 01 10 04 A1 28 03 00 04 A5 CC 00 00 04 C8 90 01 00 04 CA 2C 03 00 04 CE D0 00 00 04 F1 94 01 10 04 F3 30 03 00 04 F7 D4 00 00 05 1A 98 01 60 05 20 E4 03 00 05 25 88 00 00 05 48 4C 01 10 05 4A B0 03 00 05 4F 54 00 00 05 72 18 01 00 05 73 B4 03 00 05 78 58 00 00 05 9B 1C 01 10 05 9C B8 03 00 05 A1 5C 00 00 05 C4 20 01 00 05 C5 BC 03 00 05 CA 60 00 00 05 ED 24 01 10 05 EE C0 03 00 05 F3 64 00 00 06 16 28 01 00 06 17 C4 03 00 06 1C 68 00 00 06 3F 2C 01 10 06 40 C8 03 00 06 45 6C 00 00 06 68 30 01 00 06 69 CC 03 00 06 6E 70 00 00 06 91 34 01 10 06 92 D0 03 00 06 97 74 00 00 06 BA 38 01 00 06 BB D4 03 00 06 C0 78 00 00 06 E3 3C 01 10 06 E4 D8 03 00 06 E9 7C 00 00 07 0C 40 01 00 07 0D DC 03 00 07 12 80 00 00 07 35 44 01 10 07 36 E0 03 00 07 3B 84 00 00 07 5E 48 01 00 07 5F E4 03 00 07 64 88 00 00 07 87 4C 01 10 07 88 E8 03 00 07 8D 8C 00 00 07 B0 50 01 60 07 B6 9C 03 00 07 BB 40 00 00 07 DE 04 01 10 07 E0 68 03 00 07 E5 0C 00 00 08 07 D0 01 00 08 09 6C 03 00 08 0E 10 00 00 08 30 D4 01 10 08 32 70 03 00 08 37 14 00 00 08 59 D8 01 00 08 5B 74 03 00 08 60 18 00 00 08 82 DC 01 10 08 84 78 03 00 08 89 1C 00 00 08 AB E0 01 00 08 AD 7C 03 00 08 B2 20 00 00 08 D4 E4 01 10 08 D6 80 03 00 08 DB 24 00 00 08 FD E8 01 00 08 FF 84 03 00 09 04 28 00 00 09 26 EC 01 10 09 28 88 03 00 09 2D 2C 00 00 09 4F F0 01 00 09 51 8C 03 00 09 56 30 00 00 09 78 F4 01 10 09 7A 90 03 00 09 7F 34 00 00 09 A1 F8 01 00 09 A3 94 03 00 09 A8 38 00 00 09 CA FC 01 10 09 CC 98 03 00 09 D1 3C 00 00 09 F4 00 01 60 09 FA 4C 03 00 09 FE F0 00 00 0A 21 B4 01 10 0A 24 18 03 00 0A 28 BC 00 00 0A 4B 80 01 00 0A 4D 1C 03 00 0A 51 C0 00 00 0A 74 84 01 10 0A 76 20 03 00 0A 7A C4 00 00 0A 9D 88 01 00 0A 9F 24 03 00 0A A3 C8 00 00 0A C6 8C 01 10 0A C8 28 03 00 0A CC CC 00 00 0A EF 90 01 00 0A F1 2C 03 00 0A F5 D0 00 00 0B 18 94 01 10 0B 1A 30 03 00 0B 1E D4 00 00 0B 41 98 01 00 0B 43 34 03 00 0B 47 D8 00 00 0B 6A 9C 01 10 0B 6C 38 03 00 0B 70 DC 00 00 0B 93 A0 01 00 0B 95 3C 03 00 0B 99 E0 00 00 0B BC A4 01 10 0B BE 40 03 00 0B C2 E4 00 00 0B E5 A8 01 60 0B EB F4 03 00 0B F0 98 00 00 0C 13 5C 01 10 0C 15 C0 03 00 0C 1A 64 00 00 0C 3D 28 01 00 0C 3E C4 03 00 0C 43 68 00 00 0C 66 2C 01 10 0C 67 C8 03 00 0C 6C 6C 00 00 0C 8F 30 01 00 0C 90 CC 03 00 0C 95 70 00 00 0C B8 34 01 10 0C B9 D0 03 00 0C BE 74 00 00 0C E1 38 01 00 0C E2 D4 03 00 0C E7 78 00 00 0D 0A 3C 01 10 0D 0B D8 03 00 0D 10 7C 00 00 0D 33 40 02 40 0D 33 4C 01 20 0D 3F 04 04 40 0D 42 88 01 30 0D 69 98 03 00 0D 76 70 01 20 0D 92 9C\n"]}], "source": ["acttab_bin = bytes()\n", "\n", "acttab_bin += bytes.fromhex('01')  # activity table slot\n", "\n", "acttab_bin += generate_acttab_header(acttab_id=21201, num_lacs=4)\n", "\n", "acttab_bin += generate_lac_definition_header(lac_id=3, num_dwells=78, first_dwell_id=2, num_acts=244)\n", "acttab_bin += generate_activity_list_bytes(lac_1_activities)\n", "\n", "acttab_bin += generate_lac_definition_header(lac_id=4, num_dwells=77, first_dwell_id=79, num_acts=242)\n", "acttab_bin += generate_activity_list_bytes(lac_2_activities)\n", "\n", "acttab_bin += generate_lac_definition_header(lac_id=5, num_dwells=77, first_dwell_id=155, num_acts=242)\n", "acttab_bin += generate_activity_list_bytes(lac_3_activities)\n", "\n", "acttab_bin += generate_lac_definition_header(lac_id=6, num_dwells=80, first_dwell_id=231, num_acts=248)\n", "acttab_bin += generate_activity_list_bytes(lac_4_activities)\n", "\n", "print_bytes(acttab_bin)"]}, {"cell_type": "code", "execution_count": 16, "id": "8afe005a-668f-4b47-b771-effc28b44920", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ic| int.from_bytes(isochecksum(acttab_bin), 'big'): 39208\n"]}, {"data": {"text/plain": ["39208"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["def isochecksum(msg: bytes) -> bytes:\n", "    \"\"\"Calculates a 16-bit checksum as per Annex A.2 of [ECSS-E-70-41A].\n", "    \n", "    The returned bytes object contains the byte CK1 at index 0 and the byte CK2 at index 1.\"\"\"\n", "    \n", "    c0 = 0\n", "    c1 = 0\n", "    for byte in msg:\n", "        c0 = (c0 + byte) % 255\n", "        c1 = (c1 + c0) % 255\n", "    ck1 = -(c0 + c1) % 255 or 255\n", "    ck2 = c1 or 255\n", "    return bytes([ck1, ck2])\n", "\n", "ic(int.from_bytes(isochecksum(acttab_bin), 'big'))"]}, {"cell_type": "code", "execution_count": null, "id": "0e85ae8a-47ac-4889-befa-ebd3d8151e49", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ae1d42f8-6acc-4ff5-b575-ba5da0228216", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}