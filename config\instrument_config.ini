[FCI]
count = 40
count_max = 250
file_class = OPER
slicer_dict = {'DETCONF': '', 'INSTCONF': '[icid;icid_ver]', 'REPSEQ': 'repeat_sequence_id', 'SCANENC': 'scan_encode_correction_lut_id', 'SCANLAW': 'scan_law_id', 'SSCANLAW': 'scan_law_id', 'APCTAB': 'scan_law_id'}
common_dict = {'DETCONF': 'detector_configuration_id', 'INSTCONF': 'detector_configuration_id', 'REPSEQ': '', 'SCANENC': '', 'SCANLAW': '', 'SSCANLAW': '', 'APCTAB': ''}
input_folder = FCI
vsm_file = MTG FCI IPSSCCC VSM [FCI-IPSSCCC-VSM].xlsx
satellites = ["MTG-I 1", "MTG-I 2", "MTG-I 3", "MTG-I 4"]
cdc_options = ["VCU Update","Mission Scenario (SL+APC+SSL)","Repeat Sequence","Scan Encoder LUT"]
channel_names = ["FD-VIS0.4","FD-VIS0.5","HR-VIS0.6","FD-VIS0.8","FD-VIS0.9","FD-NIR1.3","FD-NIR1.6","HR-NIR2.2","HR-IR3.8","FA-IR3.8","FD-IR6.3","FD-IR7.3","FD-IR8.7","FD-IR9.7","HR-IR10.5","FD-IR12.3","FD-IR13.3"]

[LI]
count = 40
count_max = 255
file_class = OPRE
slicer_dict = {'DETCONF': '', 'INSTCONF': '[icid;icid_ver]'}
common_dict = {'DETCONF': 'detector_configuration_id', 'INSTCONF': 'detector_configuration_id'}
input_folder = LI
vsm_file = MTG LI IPSSCCC VSM [LI-IPSSCCC-VSM].xlsx
satellites = ["MTG-I 1", "MTG-I 2", "MTG-I 3", "MTG-I 4"]
cdc_options = ["LOH BASIC Conf","LOH OPER Conf","LME Conf (Patch)","LME Conf (PAF)","LI Calibration"]
par_name = ["Activate/Deactivate LOH Regulation","Activate/Deactivate LME Regulation","Activate/Deactivate MV Filtering","Activate/Deactivate Clustering","Activate/Deactivate Isolated DT Filtering","Configure Background/MV Windows","Configure Background & Activate/Deactivate MV Windows","Enable/Disable RTS Unlock","Activate/Deactivate dt neighbour","Seasonal Configuration Update","Update Season Conf Enable","Update SDTF Enable Vector","Update KAVG Sel Vector","Update DR Delta Thresholds Enable Vector","Update DR Det LUT Sel Vector","Update DR SDTF LUT Sel Vector","Update RTS Unlock Repetition","Update RTS Unlock Period"]
tc_name =["Basic_Conf_Pix","Conf_Det_Thr","Delta_Thr","Clamp","Asic","Filter_Thr","Noise_Corr","Offset_Lut"]

[IRS]
count = 40
count_max = 0
file_class = OPER
slicer_dict = {'DETCONF': '', 'INSTCONF': '[icid;icid_ver]', 'REPSEQ': 'repeat_sequence_id', 'SCANENC': 'scan_encode_correction_lut_id', 'SCANLAW': 'scan_law_id', 'SSCANLAW': 'scan_law_id', 'ACTTAB': 'activity_table_id', 'INSTOBP':  'instrument_observation_id'}
common_dict = {'DETCONF': 'detector_configuration_id', 'INSTCONF': 'detector_configuration_id', 'REPSEQ': '', 'SCANENC': '', 'SCANLAW': '', 'SSCANLAW': '', 'ACTTAB': 'activity_table_id'}
input_folder = IRS
vsm_file = MTG IRS IPSSCCC VSM (IRS-IPSSCCC-VSM).xlsx
satellites = ["MTG-S 1", "MTG-S 2"]
cdc_options = ["Mission Scenario (SL)","Activity Table","Repeat Sequence","Scan Encoder LUT","VCU Update","DPU","SAV","Scan Scenario"]

[UVN]
count = 40
count_max=0
file_class = OPER
slicer_dict = {}
common_dict = {}
input_folder =  UVN
vsm_file = MTG UVN IPSSCCC VSM [UVN-IPSSCCC-VSM].xlsx
satellites = ["MTG-S 1", "MTG-S 2"]
cdc_options = ["Generate PTD Memory image File"]
ptd_config_file = ptd_config.ini