# Standard library imports
import os
from difflib import SequenceMatcher
import lxml
import re
from pathlib import Path
import pandas as pd
import netCDF4 as nc


# Local imports
from src.utils.import_utils import basics, config, mamut
from src.logger_wrapper import logger
from src.algorithms.uvn_functions.utils.bit_dec_converter import (
    convert_bit_to_dec, convert_dec_to_bit)


def load_input_files(str_instrument: str):
    """Loads instruments input files into the correct folder"""

    # Create path input
    str_path_input = os.path.join(config.CONFIG_VALUES["input_folder"], str_instrument)

    # Create test files path
    str_path_test = os.path.join(
        config.CONFIG_VALUES["test_files"], str_instrument, "Input"
    )

    # Get all files in both path if the same dont do the rest
    lst_input = basics.files_in_dir(str_path_input)
    lst_test = basics.files_in_dir(str_path_test)


def assert_img_files(lst_path_expected: list, lst_path_run: list):
    """Compare images files ignoring date and version as they will change"""

    # Check all img files
    for int_step in range(len(lst_path_expected)):
        # Find the correct file, the most similar one
        str_path_run = find_file_name(lst_path_expected[int_step], lst_path_run)

        logger.warning(f"Expected: {lst_path_expected[int_step]}")
        logger.warning(f"Run: {str_path_run}")

        # Get info from expected bin file
        obj_expected_image = mamut.memory_image(lst_path_expected[int_step])

        # Get info of the run test
        obj_test_image = mamut.memory_image(str_path_run)

        # Check if the body are the same
        if obj_expected_image.hex_image != obj_test_image.hex_image:
            logger.warning("Not equal body")
            return False

        # Check Header
        if len(obj_expected_image.dict_header) != len(obj_test_image.dict_header):
            logger.warning("Not equal header")
            return False

        # Check all header attributes
        for str_key in obj_expected_image.dict_header:
            # Ignore the ones that wont be equal by design
            if str_key not in ["CREATIONDATE", "DESCRIPTION"]:  # Added "DESCRIPTION" to ignored keys
                if (
                    obj_expected_image.dict_header[str_key]
                    != obj_test_image.dict_header[str_key]
                ):
                    logger.warning(
                        f"Header field '{str_key}' differs: "
                        f"Expected '{obj_expected_image.dict_header[str_key]}', "
                        f"Got '{obj_test_image.dict_header[str_key]}'"
                    )
                    logger.warning("Not equal header") # Keep original warning
                    return False

        return True


def assert_TC_stacks(lst_path_expected: list, lst_path_run: list):
    """Compare TC stacks files ignoring date and version as they will change"""

    # Check all img files
    for int_step in range(len(lst_path_expected)):
        # Find the correct file, the most similar one
        str_path_run = find_file_name(lst_path_expected[int_step], lst_path_run)

        logger.warning(f"Expected: {lst_path_expected[int_step]}")
        logger.warning(f"Run: {str_path_run}")

        # Get info from expected bin file
        with open(lst_path_expected[int_step], mode="r") as obj_file:
            lst_expected = obj_file.read().splitlines()

        # Get info of the run test
        with open(str_path_run, mode="r") as obj_file:
            lst_run = obj_file.read().splitlines()

        # Check if the body are the same
        for int_step in range(len(lst_run)):
            if lst_run[int_step] != lst_expected[int_step]:
                logger.warning("Not equal body")
                return False

        return True


def find_file_name(str_string: str, lst_list: list):
    """Finds the most similar file name on a list"""

    # Initialize Variables
    int_ratio = 0
    # Regex to identify and remove timestamps (e.g., YYYYMMDDHHMMSS)
    # This regex looks for 14 digits, common for timestamps.
    str_re_pattern_timestamp = r"_\d{14}"

    # Get only filename and remove timestamp from the target string
    str_string_base = Path(str_string).stem
    str_string_no_ts = re.sub(str_re_pattern_timestamp, "", str_string_base)
    # Further normalize by removing common prefixes like OPIT_NA_ or PAF_
    str_string_normalized = re.sub(r"^(OPIT_NA_PAF_|PAF_)", "", str_string_no_ts)

    str_found = ""

    # Find the correct file, the most similar one
    for str_step in lst_list:
        # Get only filename, remove timestamp, and normalize prefixes from the current list item
        str_step_filename_base = Path(str_step).stem
        str_step_filename_no_ts = re.sub(str_re_pattern_timestamp, "", str_step_filename_base)
        str_step_filename_normalized = re.sub(r"^(OPIT_NA_PAF_|PAF_)", "", str_step_filename_no_ts)

        logger.warning(f"Comparing normalized: '{str_string_normalized}' with '{str_step_filename_normalized}'")

        # Similarity percentage
        int_ratio_step = SequenceMatcher(None, str_string_normalized, str_step_filename_normalized).ratio()

        # Check the step ratio is bigger than the one saved
        if int_ratio_step > int_ratio:
            # Save the info of the most similar file and the ratio of similarity
            int_ratio = int_ratio_step
            str_found = str_step
        elif int_ratio_step == int_ratio and str_found: # Handle cases with same ratio, prefer shorter or specific match if logic allows
            # This part can be enhanced if there's a tie-breaking rule, e.g. prefer shorter, or one with fewer differences.
            # For now, it keeps the first best match.
            pass

    if not str_found and lst_list:
        logger.warning(f"No suitable match found for {str_string} in list. Returning first element as fallback.")
        return lst_list[0] # Fallback, consider if this is appropriate or should raise error/return None
    elif not lst_list:
        logger.error(f"Cannot find match for {str_string} in an empty list.")
        return None # Or raise an error

    return str_found


def assert_paf_files(lst_path_expected: list, lst_path_run: list):
    """Provided 2 list of paf files location will return if they are equal, supposing same order in both lists"""

    # Sort string to avoid time issue
    lst_path_expected.sort(key=lambda str_step: str_step.split("-")[-1])
    lst_path_run.sort(key=lambda str_step: str_step.split("-")[-1])

    # Loop through the list of PAF
    for int_step in range(len(lst_path_expected)):
        # Initialize Variables
        lst_expected = []
        lst_test = []

        # Find the correct file, the most similar one
        str_path_expected = lst_path_expected[int_step]
        str_path_run = find_file_name(str_path_expected, lst_path_run)

        logger.warning(f"Expected: {str_path_expected}")
        logger.warning(f"Run: {str_path_run}")

        # Get root of expected and test files
        root_expected = lxml.etree.parse(str_path_expected).getroot()
        root_run = lxml.etree.parse(str_path_run).getroot()

        obj_step_expected = root_expected.findall(".//parameters")
        obj_step_run = root_run.findall(".//parameters")

        # Initial check , the number of  parameters are the same
        if len(obj_step_expected) != len(obj_step_run):
            logger.warning("Not equal parameter lengths")
            return False

        # Loop through the expected steps
        for obj_step in list(obj_step_expected[0]):
            # Reset variables
            lst_step = []
            lst_tags = []
            bln_skip = False

            # Check for expected changes to skip
            for obj_element in obj_step.findall(".//name"):
                if obj_element.text == "IMG_FileName":
                    bln_skip = True

            # Skip the expected changes
            if bln_skip:
                continue

            # Get all tags inside the given list of tags
            for obj_element in obj_step:
                lst_tags.append(obj_element.tag)

            # Create a list of list with all the expected tags values
            for str_step in lst_tags:
                lst_step.append(obj_step.find(str_step).text)

            lst_expected.append(lst_step)

        # Loop through the test steps
        for obj_step in list(obj_step_run[0]):
            # Reset variables
            lst_step = []
            lst_tags = []
            bln_skip = False

            # Check for expected changes to skip
            for obj_element in obj_step.findall(".//name"):
                if obj_element.text == "IMG_FileName":
                    bln_skip = True

            # Skip the expected changes
            if bln_skip:
                continue

            # Get all tags inside the given list of tags
            for obj_element in obj_step:
                lst_tags.append(obj_element.tag)

            # Create a list of list with all the expected tags values
            for str_step in lst_tags:
                lst_step.append(obj_step.find(str_step).text)

            lst_test.append(lst_step)

        # Check if files are the same or not
        if lst_expected != lst_test:
            logger.warning("Not equal files")
            return False

    return True


def assert_bin_files(lst_path_expected: list, lst_path_run: list):
    """Compare two binary files"""

    # Check all bin files
    for int_step in range(len(lst_path_expected)):
        # Find the correct file, the most similar one
        str_path_run = find_file_name(lst_path_expected[int_step], lst_path_run)

        logger.warning(f"Expected: {lst_path_expected[int_step]}")
        logger.warning(f"Run: {str_path_run}")

        # Get info from expected bin file
        with open(lst_path_expected[int_step], mode="rb") as obj_file:
            str_expected = obj_file.read()

        # Get info of the run test
        with open(str_path_run, mode="rb") as obj_file:
            str_run = obj_file.read()

        # Check if the files are the same
        if str_expected != str_run:
            logger.warning("Not equal files")
            return False

    # If not
    return True

def assert_excel_files(expected_file_path: str, actual_file_path: str):
    """ Compare two excel files

    Args:
        expected_file_path (str): expected excel file
        actual_file_path (str): actual excel file
    """
    # Load both Excel files as dict of DataFrames
    file1 = pd.read_excel(expected_file_path, sheet_name=None, engine="openpyxl")
    file2 = pd.read_excel(actual_file_path, sheet_name=None, engine="openpyxl")

# Get all unique sheet names from both files
    all_sheets = set(file1.keys()).union(set(file2.keys()))

# Loop through each sheet and compare
    for sheet in all_sheets:
        df1 = file1.get(sheet)
        df2 = file2.get(sheet)

        #Checks for missing sheets in either file
        if df1 is None:
            return False
        elif df2 is None:
             return False
        #Uses .equals() for fast equality check
        elif df1.equals(df2):
            continue

    return True


def assert_ncd_files(lst_path_expected: list, lst_path_run: list):
    """Provided 2 list of NetCDF files location will return if they are equal, supposing same order in both lists
    """

    if(len(lst_path_expected)!=len(lst_path_run)):
        return False

    #Check all ncd files
    for i_path in range(len(lst_path_expected)):
        # Find the correct file, the most similar one
        #str_path_run = find_file_name(lst_path_expected[i_path], lst_path_run)
        str_path_run=lst_path_run[i_path]

        logger.warning(f"Expected: {lst_path_expected[i_path]}")
        logger.warning(f"Run: {str_path_run}")

        nc_file1 = nc.Dataset(lst_path_expected[i_path], 'r', format='NETCDF4')
        nc_file2 = nc.Dataset(str_path_run, 'r', format='NETCDF4')

        lst_group1=[k_group for k_group in nc_file1.groups]
        lst_group2=[k_group for k_group in nc_file2.groups]

        dict_ptd1={}
        dict_ptd2={}
        if(len(lst_group1)!=len(lst_group2)):
            return False
        for i_group in range(len(lst_group1)):
            if(lst_group1[i_group] not in lst_group2):
                return False

            k_group=lst_group1[i_group]

            dict_ptd1[k_group]={}
            dict_ptd2[k_group]={}

            for k_col in nc_file1[k_group].variables.keys():
                dict_ptd1[k_group][k_col]=nc_file1[k_group].variables[k_col][:].data.tolist()

            for k_col in nc_file2[k_group].variables.keys():
                dict_ptd2[k_group][k_col]=nc_file2[k_group].variables[k_col][:].data.tolist()

        if(assert_obj(dict_ptd1,dict_ptd2)==False):
            return False

    return True


def assert_obj(obj1,obj2):
    """Check whether 2 objects are equal, with special handling of floats to ignore rounding errors
    """

    var_format="{:.8e}"#min:8, max:15

    if(type(obj1)!=type(obj2)):
        if(obj1!=obj2):#in case the 2 obj are actually equal (e.g. 0.0==0)
            logger.debug('Type mismatch: %s vs %s'%(type(obj1),type(obj2)))
            return False
    elif(type(obj1) is dict):
        lst_k1=[k for k in obj1]
        lst_k2=[k for k in obj2]
        for k_var in lst_k1:
            if(k_var not in lst_k2):
                logger.debug('Dict1 key=%s not found in dict2'%(k_var))
                return False
        for k_var in lst_k2:
            if(k_var not in lst_k1):
                logger.debug('Dict2 key=%s not found in dict2'%(k_var))
                return False
        for k_var in lst_k1:
            if(assert_obj(obj1[k_var],obj2[k_var])==False):
                logger.debug('Dict item mismatch on key=%s'%(k_var))
                return False
    elif(type(obj1) is list):
        if(len(obj1)!=len(obj2)):
            logger.debug('List length mismatch: %s vs %s'%(len(obj1),len(obj2)))
            return False
        for i_lst in range(len(obj1)):
            if(assert_obj(obj1[i_lst],obj2[i_lst])==False):
                logger.debug('List item mismatch on index=%s'%(i_lst))
                return False
    elif(type(obj1) is float):
        #ignore 32-bit rounding error
        hex_val1=convert_dec_to_bit(val_dec=obj1,len_bit=32,type='float',flag_hex=True)
        hex_val2=convert_dec_to_bit(val_dec=obj2,len_bit=32,type='float',flag_hex=True)
        if(hex_val1!=hex_val2):
            logger.debug('Value mismatch (%s): %s vs %s'%(type(obj1),obj1,obj2))
            return False
        if(float(var_format.format(obj1))!=float(var_format.format(obj2))):
        #if(float("{:.8e}".format(obj1))!=float("{:.8e}".format(obj2))):
        #if(obj1!=obj2):
            logger.debug('Value mismatch (%s): %s vs %s'%(type(obj1),obj1,obj2))
            return False
    else:
        if(obj1!=obj2):
            logger.debug('Value mismatch (%s): %s vs %s'%(type(obj1),obj1,obj2))
            return False

    return True
