"""Constants used across GUI components"""

# Define base font sizes
BASE_FONT_SIZE = 12
TITLE_FONT_SIZE = int(BASE_FONT_SIZE * 1.5)  # Increased for more prominent titles
HEADER_FONT_SIZE = int(BASE_FONT_SIZE * 1.25)  # Slightly increased
BUTTON_FONT_SIZE = int(BASE_FONT_SIZE)

# Default font family - System fonts for modern look
DEFAULT_FONT = ("Segoe UI", BASE_FONT_SIZE)
TITLE_FONT = ("Segoe UI", TITLE_FONT_SIZE, "bold")
HEADER_FONT = ("Segoe UI", HEADER_FONT_SIZE, "bold")

# Modern UI Color scheme - Contemporary Professional Design (2025)
# Based on modern design systems with excellent contrast and readability

# Primary colors - Professional blue palette
PRIMARY_COLOR = "#2563EB"       # Blue 600 - Primary actions (Generate button)
PRIMARY_HOVER = "#1D4ED8"       # Blue 700 - Hover state for primary
PRIMARY_LIGHT = "#3B82F6"       # Blue 500 - Focus rings, accents

# Secondary colors - Neutral gray palette  
SECONDARY_COLOR = "#E5E7EB"     # Gray 200 - Secondary buttons (Back, Close)
SECONDARY_HOVER = "#D1D5DB"     # Gray 300 - Secondary button hover
SECONDARY_TEXT = "#1F2937"      # Gray 800 - Text on secondary buttons

# Modern UI backgrounds
BACKGROUND_COLOR = "#F9FAFB"    # Gray 50 - Overall window background
CARD_BACKGROUND = "#FFFFFF"     # White - Main content panels
CARD_SHADOW = "rgba(0, 0, 0, 0.1)" # Subtle shadow for depth
BORDER_COLOR = "#D1D5DB"        # Gray 300 - Input borders

# Header colors for different sections
HEADER_PRIMARY = "#2563EB"      # Blue 600 - Primary section headers
HEADER_SECONDARY = "#4F46E5"    # Indigo 600 - Sub-section headers  
HEADER_SUCCESS = "#059669"      # Emerald 600 - Success/config sections
HEADER_ACCENT = "#7C3AED"       # Violet 600 - Special sections

# Card-specific color combinations with subtle backgrounds
CARD_PRIMARY_HEADER = "#2563EB"    # Blue 600 - VCU Memory Side, Channel Selection
CARD_PRIMARY_BG = "#EFF6FF"        # Blue 50 - Very subtle blue background
CARD_PRIMARY_BORDER = "#BFDBFE"    # Blue 200 - Subtle border

CARD_SECONDARY_HEADER = "#4F46E5"  # Indigo 600 - Memory Image Type, Activity Selection
CARD_SECONDARY_BG = "#EEF2FF"      # Indigo 50 - Very subtle indigo background  
CARD_SECONDARY_BORDER = "#C7D2FE"  # Indigo 200 - Subtle border

CARD_SUCCESS_HEADER = "#059669"    # Emerald 600 - ICID Configuration
CARD_SUCCESS_BG = "#ECFDF5"        # Emerald 50 - Very subtle green background
CARD_SUCCESS_BORDER = "#A7F3D0"    # Emerald 200 - Subtle border

CARD_ACCENT_HEADER = "#7C3AED"     # Violet 600 - Special sections
CARD_ACCENT_BG = "#F5F3FF"         # Violet 50 - Very subtle violet background
CARD_ACCENT_BORDER = "#DDD6FE"     # Violet 200 - Subtle border

CARD_ERROR_HEADER = "#DC2626"      # Red 600 - Error sections
CARD_ERROR_BG = "#FEF2F2"          # Red 50 - Very subtle red background
CARD_ERROR_BORDER = "#FECACA"      # Red 200 - Subtle border

CARD_WARNING_HEADER = "#D97706"    # Amber 600 - Warning sections
CARD_WARNING_BG = "#FFFBEB"        # Amber 50 - Very subtle amber background
CARD_WARNING_BORDER = "#FDE68A"    # Amber 200 - Subtle border

# Legacy color mappings for backward compatibility
ACCENT_COLOR = HEADER_ACCENT
SUCCESS_COLOR = HEADER_SUCCESS
ERROR_COLOR = CARD_ERROR_HEADER
WARNING_COLOR = CARD_WARNING_HEADER

# Enhanced text colors with better contrast
TEXT_PRIMARY = "#1F2937"        # Gray 800 - Main text, excellent readability
TEXT_SECONDARY = "#6B7280"      # Gray 500 - Secondary text
TEXT_DISABLED = "#9CA3AF"       # Gray 400 - Disabled elements
TEXT_ON_PRIMARY = "#FFFFFF"     # White - Text on colored backgrounds

# Padding constants
STANDARD_PADDING = {"padx": 10, "pady": 10}
SMALL_PADDING = {"padx": 5, "pady": 5}
LARGE_PADDING = {"padx": 15, "pady": 15}
