#!/usr/bin/env python3
"""
Compare data from NetCDF file and XLDT file side by side.
"""

import os
import sys
import numpy as np
from xldt_reader_standalone import XLDTReader

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    import netCDF4 as nc
except ImportError:
    print("❌ netCDF4 not available")
    sys.exit(1)

def compare_data():
    """Compare NetCDF and XLDT data side by side."""
    
    # File paths
    netcdf_file = "../assets/Input_Files/IRS/MTS1_IRS_SCANLAW_0000_v2.nc"
    xldt_file = "inputs/ScanLaw_Central_Summer_16384.xldt"
    config_path = "../config/IRS/IRS_SL_Conf.csv"
    
    if not os.path.exists(netcdf_file):
        print(f"❌ NetCDF file not found: {netcdf_file}")
        return
    
    if not os.path.exists(xldt_file):
        print(f"❌ XLDT file not found: {xldt_file}")
        return
    
    print("🔍 Comparing NetCDF vs XLDT Data")
    print("=" * 80)
    
    # Extract scan law ID from filename
    import re
    match = re.search(r'(\d+)\.xldt$', xldt_file)
    scan_law_id = int(match.group(1)) if match else None
    print(f"Scan Law ID: {scan_law_id}")
    print("=" * 80)
    
    try:
        # Read NetCDF data
        print("\n📊 READING NETCDF DATA")
        print("-" * 40)
        
        with nc.Dataset(netcdf_file, 'r') as dataset:
            # Get scan law IDs
            scan_law_ids = dataset.variables['scan_law_id'][:]
            print(f"Available scan law IDs: {scan_law_ids}")
            
            # Find index for scan law ID 16384
            target_indices = np.where(scan_law_ids == scan_law_id)[0]
            if len(target_indices) == 0:
                print(f"❌ Scan law ID {scan_law_id} not found in NetCDF")
                return
            
            target_index = target_indices[0]
            print(f"Found scan law ID {scan_law_id} at NetCDF index {target_index}")
            
            # Get FDA data from NetCDF
            netcdf_alpha = dataset.variables['fda_mp_pointer_alpha'][target_index, :]
            netcdf_epsilon = dataset.variables['fda_mp_pointer_epsilon'][target_index, :]
            
            # Convert to regular arrays if masked
            if hasattr(netcdf_alpha, 'data'):
                netcdf_alpha = netcdf_alpha.data
            if hasattr(netcdf_epsilon, 'data'):
                netcdf_epsilon = netcdf_epsilon.data
            
            # Remove filler values (255)
            filler_pos_alpha = np.where(netcdf_alpha == 255)[0]
            if len(filler_pos_alpha) > 0:
                netcdf_alpha_valid = netcdf_alpha[:filler_pos_alpha[0]]
            else:
                netcdf_alpha_valid = netcdf_alpha
            
            filler_pos_epsilon = np.where(netcdf_epsilon == 255)[0]
            if len(filler_pos_epsilon) > 0:
                netcdf_epsilon_valid = netcdf_epsilon[:filler_pos_epsilon[0]]
            else:
                netcdf_epsilon_valid = netcdf_epsilon
            
            print(f"NetCDF fda_mp_pointer_alpha: {len(netcdf_alpha_valid)} valid values")
            print(f"NetCDF fda_mp_pointer_epsilon: {len(netcdf_epsilon_valid)} valid values")
        
        # Read XLDT data
        print("\n📊 READING XLDT DATA")
        print("-" * 40)
        
        reader = XLDTReader(config_path=config_path)
        xldt_data = reader.read_xldt_file(xldt_file)
        
        # Get FDA section from XLDT
        if 'FDA' in xldt_data['sections']:
            fda_section = xldt_data['sections']['FDA']
            xldt_bytes = fda_section.data
            
            print(f"XLDT FDA section: {len(xldt_bytes)} bytes")
            
            # Parse as pairs of bytes (current mapping)
            xldt_alpha = []
            xldt_epsilon = []
            for i in range(0, len(xldt_bytes), 2):
                if i + 1 < len(xldt_bytes):
                    xldt_alpha.append(xldt_bytes[i])
                    xldt_epsilon.append(xldt_bytes[i + 1])
            
            print(f"XLDT parsed alpha: {len(xldt_alpha)} values")
            print(f"XLDT parsed epsilon: {len(xldt_epsilon)} values")
        else:
            print("❌ FDA section not found in XLDT")
            return
        
        # Compare the data
        print("\n🔍 SIDE-BY-SIDE COMPARISON")
        print("=" * 80)
        
        print(f"{'Index':<5} {'NetCDF Alpha':<12} {'XLDT Alpha':<12} {'NetCDF Epsilon':<14} {'XLDT Epsilon':<12} {'Match':<8}")
        print("-" * 80)
        
        max_len = max(len(netcdf_alpha_valid), len(xldt_alpha))
        matches_alpha = 0
        matches_epsilon = 0
        
        for i in range(min(50, max_len)):  # Show first 50 values
            nc_a = netcdf_alpha_valid[i] if i < len(netcdf_alpha_valid) else "N/A"
            nc_e = netcdf_epsilon_valid[i] if i < len(netcdf_epsilon_valid) else "N/A"
            xl_a = xldt_alpha[i] if i < len(xldt_alpha) else "N/A"
            xl_e = xldt_epsilon[i] if i < len(xldt_epsilon) else "N/A"
            
            match_a = "✓" if nc_a == xl_a else "✗"
            match_e = "✓" if nc_e == xl_e else "✗"
            match_overall = "✓" if match_a == "✓" and match_e == "✓" else "✗"
            
            if nc_a == xl_a and isinstance(nc_a, (int, np.integer)):
                matches_alpha += 1
            if nc_e == xl_e and isinstance(nc_e, (int, np.integer)):
                matches_epsilon += 1
            
            print(f"{i:<5} {nc_a:<12} {xl_a:<12} {nc_e:<14} {xl_e:<12} {match_overall:<8}")
        
        if max_len > 50:
            print(f"... (showing first 50 of {max_len} values)")
        
        print("\n📊 SUMMARY")
        print("-" * 40)
        print(f"NetCDF valid alpha values: {len(netcdf_alpha_valid)}")
        print(f"NetCDF valid epsilon values: {len(netcdf_epsilon_valid)}")
        print(f"XLDT alpha values: {len(xldt_alpha)}")
        print(f"XLDT epsilon values: {len(xldt_epsilon)}")
        print(f"Alpha matches: {matches_alpha}/{min(len(netcdf_alpha_valid), len(xldt_alpha))}")
        print(f"Epsilon matches: {matches_epsilon}/{min(len(netcdf_epsilon_valid), len(xldt_epsilon))}")
        
        # Show first 20 values of each for easy comparison
        print(f"\n🔍 FIRST 20 VALUES COMPARISON")
        print("-" * 40)
        print(f"NetCDF Alpha:  {list(netcdf_alpha_valid[:20])}")
        print(f"XLDT Alpha:    {list(xldt_alpha[:20])}")
        print(f"NetCDF Epsilon: {list(netcdf_epsilon_valid[:20])}")
        print(f"XLDT Epsilon:   {list(xldt_epsilon[:20])}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    compare_data()
