# Standard library imports
import os
from typing import List

# Local imports
from src.utils.import_utils import config, basics
from src.logger_wrapper import logger
from src.algorithms.fci_functions.FCI_SELUT import FCI_SELUT
from src.algorithms.fci_functions.FCI_APC import FCI_APC
from src.algorithms.fci_functions.FCI_SL import FCI_SL
from src.algorithms.fci_functions.FCI_PAF import FCI_PAF
from src.algorithms.fci_functions.FCI_VCU_MEM_NUMOFF import FCI_VCU_MEM_NUMOFF
from src.algorithms.fci_functions.FCI_VCU_MEM_PIXMAP import FCI_VCU_MEM_PIXMAP
from src.utils.excel_utils import Read_Sheet
from src.utils.activity_params import ActivityParams

# Constants
INSTRUMENT="FCI"
dict_config = config.CONFIG_INSTRUMENT[INSTRUMENT]

def fci_functions_launcher(act_params: ActivityParams, lst_part: List[str],
                           lst_channel: List[str], str_VCU_image_format: str) -> None:
    """Launch appropriate FCI functions based on activity parameters
    
    Args:
        act_params: Activity parameters containing configuration and operation details
        lst_part: List of parts to process for VCU updates
        lst_channel: List of channels to process
        str_VCU_image_format: Format specification for VCU image generation
    """
    if act_params.activity == "Mission Scenario (SL+APC+SSL)":
        for satellite in act_params.satellites:
            FCI_SL(act_params, satellite)
            FCI_APC(act_params, satellite)
            FCI_PAF(act_params, satellite)

    elif act_params.activity == "VCU Update":
        # Use icid and icid_ver for VCU operations
        if "Numerical Offset" in lst_part:
            FCI_VCU_MEM_Launcher(
                act_params,
                lst_channel,
                str_VCU_image_format,
                "VCU"
            )
        if "Pixel Mapping" in lst_part:
            FCI_VCU_MEM_Launcher(
                act_params,
                lst_channel,
                str_VCU_image_format,
                "PIX"
            )
        if "Numerical Offset" in lst_part or "Instconf" in lst_part:
            if ("Numerical Offset" in lst_part and str_VCU_image_format == "Image per Channel"):
                for channel in lst_channel:
                    for satellite in act_params.satellites:
                        FCI_PAF(act_params, satellite, Which_VCU_Update=lst_part, channel=channel)
            else:
                for satellite in act_params.satellites:
                    FCI_PAF(act_params, satellite,
                            Which_VCU_Update=lst_part,
                            channel=lst_channel[0] if "Numerical Offset" in lst_part else "")

    elif act_params.activity == "Repeat Sequence":
        for satellite in act_params.satellites:
            FCI_PAF(act_params, satellite)

    elif act_params.activity == "Scan Encoder LUT":
        # Process each satellite for SELUT updates
        for satellite in act_params.satellites:
            FCI_SELUT(act_params, satellite)
            
    # Handle output commit
    if not act_params.test_mode:
        _handle_output_commit(act_params.satellites)
            

def FCI_VCU_MEM_Launcher(
    act_params: ActivityParams,
    lst_channels: List[str],
    str_memory: str,
    WhichUpdate: str
) -> None:
    """Handles FCI VCU memory updates
    
    Args:
        act_params: Activity parameters containing configuration and operation details
        lst_channels: List of channels to process
        str_memory: Memory type specification ('Combined Image' or 'Image per Channel')
        WhichUpdate: Type of update to perform ('VCU' or 'PIX')
    """
    logger.info("FCI_VCU_MEM_Launcher")
    global glob_NOF_StartAdd
    global glob_NOF_BlkLen

    # Specify here the files used by the specific section of code
    lst_used_files = ["INSTCONF", "DETCONF"]

    Mem_Map = Read_Sheet(
        os.path.join(
            config.CONFIG_VALUES["config_folder"],
            INSTRUMENT,
            "FCI_VCU_CONF_Config.xlsx",
        ),
        "Map",
    )

    for satellite in act_params.satellites:
        glob_NOF_StartAdd = {}
        glob_NOF_BlkLen = {}

        for File in lst_used_files:
            df_VSM = Read_Sheet(dict_config["vsm_path"], File)

        logger.info(f"Started FCI VCU Memory Image File creation for {satellite}")

        if str_memory == "Combined Image":
            if WhichUpdate == "VCU":
                    FCI_VCU_MEM_NUMOFF(
                        act_params,
                        satellite,
                        lst_used_files,
                        Mem_Map,
                        df_VSM,
                        lst_channels,
                        100  # n_channel
                    )
            else:
                    FCI_VCU_MEM_PIXMAP(
                        act_params,
                        satellite,
                        lst_used_files,
                        Mem_Map,
                        df_VSM,
                        lst_channels,
                        200  # n_channel
                    )

        elif str_memory == "Image per Channel":
            n_channel = 1

            for channel in lst_channels:
                if WhichUpdate == "VCU":
                    FCI_VCU_MEM_NUMOFF(
                        act_params,
                        satellite,
                        lst_used_files,
                        Mem_Map,
                        df_VSM,
                        [channel],
                        n_channel + 100  # n_channel
                    )
                else:
                    FCI_VCU_MEM_PIXMAP(
                        act_params,
                        satellite,
                        lst_used_files,
                        Mem_Map,
                        df_VSM,
                        [channel],
                        n_channel + 200  # n_channel
                    )
                n_channel += 1

    logger.info("FCI VCU Memory Image File Creation Completed")


def _handle_output_commit(satellites: List[str]) -> None:
    """Handle committing output files to gitlab."""
    bln_commit = basics.pop_up_message(
        "Output Outcome",
        "Execution Completed\nDo you wish to commit the generated output?",
        "askyesno",
    )
    
    if bln_commit:
        for satellite in satellites:
            # Create gitlab for the specific satellite
            str_gitlab_project = (
                config.CONFIG_VALUES[f"gitlab_fci"]
                + satellite[-1]
            )

            # Get files to commit
            lst_files_to_commit = basics.files_in_dir(
                os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
            )

            # Add full paths
            lst_files_to_commit = [
                os.path.join(config.CONFIG_VALUES["output_folder"], satellite, f)
                for f in lst_files_to_commit
            ]

            # Commit files
            basics.commit_to_gitlab(str_gitlab_project, lst_files_to_commit)

        basics.pop_up_message(
            "The_End", 
            "Commit Performed Successfully", 
            "info"
        )
