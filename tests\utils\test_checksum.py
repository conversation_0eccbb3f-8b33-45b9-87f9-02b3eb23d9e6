# Standard library Imports
import pytest

# Local imports
from src.utils.import_utils import basics

class TestChecksum:
    def test_CRC16(self):
        hex_crc16 = basics.checksum("0A00010103010001000101500D000C", "CRC16")
        assert hex_crc16 == "2B32"

    def test_fletcher16(self):
        hex_fletcher = basics.checksum("0A00010103010001000101500D000C", "fletcher16")
        assert hex_fletcher == "2162"
