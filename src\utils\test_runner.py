"""
Test runner utility for the CDC-S application.
This module provides functions to execute pytest for different instrument tests within a virtual environment.
"""
import subprocess
import os
import sys
from typing import Tuple, Callable
import tkinter as tk  # Added import
from tkinter import messagebox  # Added import

from src.logger_wrapper import logger

# Determine the project root directory dynamically
# __file__ is .../CDC-S/src/utils/test_runner.py
# os.path.dirname(__file__) is .../CDC-S/src/utils
# First .. gets to .../CDC-S/src
# Second .. gets to .../CDC-S (project root)
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))

def _run_pytest_for_directory(
    test_dir_relative_path: str,
    instrument_name: str,
    output_callback: Callable[[str], None]  # Callback for streaming output
) -> bool:  # Return only bool, output is streamed
    """
    Helper function to run pytest for a specific directory within a virtual environment
    and stream output.

    Args:
        test_dir_relative_path: Relative path to the test directory from project root.
        instrument_name: Name of the instrument for logging.
        output_callback: Function to call with each line of output.

    Returns:
        True if tests passed (or no tests found), False otherwise.
    """
    test_dir_abs_path = os.path.join(PROJECT_ROOT, test_dir_relative_path)
    
    venv_scripts_path = os.path.join(PROJECT_ROOT, "venv", "Scripts")
    activate_script_path = os.path.join(venv_scripts_path, "activate.bat") 
    venv_python_executable = os.path.join(venv_scripts_path, "python.exe")

    if not os.path.isfile(activate_script_path):
        error_msg = (
            f"Error: Virtual environment activation script (activate.bat) not found at '{activate_script_path}'. "
            f"Please ensure the virtual environment is set up correctly at '{os.path.join(PROJECT_ROOT, 'venv')}'.\n"
        )
        logger.error(error_msg)
        output_callback(error_msg)
        return False

    if not os.path.isfile(venv_python_executable):
        error_msg = (
            f"Error: Virtual environment Python executable not found at '{venv_python_executable}'. "
            f"Please ensure the virtual environment is set up correctly at '{os.path.join(PROJECT_ROOT, 'venv')}'.\n"
        )
        logger.error(error_msg)
        output_callback(error_msg)
        return False

    # Quote paths for command robustness, especially for cmd.exe
    quoted_activate_script = f'"{activate_script_path}"'
    quoted_python_executable = f'"{venv_python_executable}"'
    quoted_test_dir = f'"{test_dir_abs_path}"'

    # Construct the command to activate the venv and run pytest
    # Using '&&' to chain commands: activate and then run pytest
    command_string = f'cmd /c "{quoted_activate_script} && {quoted_python_executable} -m pytest {quoted_test_dir}"'

    # Log the command string that will be executed by the shell
    logger.info(f"Running tests for {instrument_name} using command string: {command_string}")
    output_callback(f"Shell command: {command_string}\n") # Clarify this is a shell command string
    logger.info(f"Project root for test execution: {PROJECT_ROOT}")
    logger.info(f"Absolute test directory: {test_dir_abs_path}")

    if not os.path.isdir(test_dir_abs_path):
        error_msg = f"Test directory not found: {test_dir_abs_path}\n"
        logger.error(error_msg)
        output_callback(error_msg)
        return False

    process = None  # Initialize process to None
    try:
        # Use Popen to execute the command string
        process = subprocess.Popen(
            command_string,
            cwd=PROJECT_ROOT,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # Merge stderr with stdout
            text=True,
            bufsize=1,  # Line-buffered
            universal_newlines=True,
            shell=True # We need shell=True to execute compound cmd commands
        )

        if process.stdout:
            for line in iter(process.stdout.readline, ''):
                output_callback(line)
        
        process.wait()  # Wait for the process to complete

        # Pytest exit codes:
        # 0: All tests passed
        # 1: Tests failed
        # 5: No tests collected
        if process.returncode == 0 or process.returncode == 5:
            success_msg = f"\nSuccessfully ran {instrument_name} tests (or no tests found). Return code: {process.returncode}\n"
            logger.info(success_msg.strip())
            output_callback(success_msg)
            return True
        else:
            error_msg = f"\nError running {instrument_name} tests. Return code: {process.returncode}\n"
            logger.error(error_msg.strip())
            output_callback(error_msg)
            return False

    except FileNotFoundError:
        # With shell=True, this error typically means the shell executable itself (e.g., cmd.exe) was not found.
        # Errors from commands within the shell (like activate.bat not found) should come via stderr.
        error_msg = (
            f"Error: The shell (cmd.exe) could not be started. Please ensure it is in your system's PATH.\n"
            f"Attempted command: {command_string}"
        )
        logger.error(error_msg)
        output_callback(error_msg)
        return False
    except Exception as e:
        error_msg = f"An unexpected error occurred while running {instrument_name} tests: {e}\n"
        logger.exception(error_msg.strip()) # Log with stack trace
        output_callback(error_msg)
        return False
    finally:
        if process and process.stdout:
            process.stdout.close()
        # stderr is merged, so no separate close needed if process.stderr was process.stdout

def run_fci_tests(output_callback: Callable[[str], None]) -> bool:
    """Runs tests for the FCI instrument."""
    logger.info("Triggered FCI tests.")
    return _run_pytest_for_directory("tests/algorithms/fci_functions", "FCI", output_callback)

def run_li_tests(output_callback: Callable[[str], None]) -> bool:
    """Shows a pop-up indicating LI tests are not available."""
    logger.info("Triggered LI tests - showing 'Not available' pop-up.")
    
    # Ensure a Tk root window exists if messagebox needs one.
    # This is a simplified way; in a complex app, root window management is more central.
    # If this script can be run outside a Tkinter mainloop, a temporary root might be needed.
    # However, messagebox.showinfo often creates its own toplevel window if no root is apparent.
    try:
        # Check if a default root window exists
        if tk._default_root is None:
            # Create a temporary, hidden root window if none exists
            temp_root = tk.Tk()
            temp_root.withdraw() # Hide the window
            messagebox.showinfo(
                parent=temp_root, # Explicitly parent to temporary root
                title="LI Tests",
                message="Not available at this moment"
            )
            temp_root.destroy() # Clean up the temporary root
        else:
            messagebox.showinfo(
                title="LI Tests",
                message="Not available at this moment"
            )
    except tk.TclError as e:
        # Fallback if GUI cannot be initialized (e.g., no display server)
        logger.error(f"Could not display Tkinter messagebox: {e}")
        output_callback("LI tests: Not available at this moment (GUI pop-up failed).\\n")
        logger.info("Informed callback about LI tests unavailability (GUI pop-up failed).")
        return True # Still "succeeded" in conveying the message via callback

    output_callback("LI tests are currently not available. A pop-up was displayed.\\n")
    logger.info("Displayed 'Not available' pop-up for LI tests and informed callback.")
    return True # Indicate that the action was handled (by showing the message)

def run_irs_tests(output_callback: Callable[[str], None]) -> bool:
    """Runs tests for the IRS instrument."""
    logger.info("Triggered IRS tests.")
    return _run_pytest_for_directory("tests/algorithms/irs_functions", "IRS", output_callback)

def run_uvn_tests(output_callback: Callable[[str], None]) -> bool:
    """Runs tests for the UVN instrument."""
    logger.info("Triggered UVN tests.")
    return _run_pytest_for_directory("tests/algorithms/uvn_functions", "UVN", output_callback)