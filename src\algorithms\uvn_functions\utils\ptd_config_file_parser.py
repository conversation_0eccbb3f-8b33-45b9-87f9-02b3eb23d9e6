"""
This class contains  is used to read the uvn configuration files
— typically ptd_config_ini file.

"""
import ast
import os

import config

"""
Singleton class to ensures that only one instance of the class
can be created — useful for managing config

"""


class PTDConfigFileParser:

    _instance = None
    parser = None

    @classmethod
    def __new__(cls, *args, **kwargs):
        """Method to create a new instance"""
        if not cls._instance:
            cls._instance = super(PTDConfigFileParser, cls).__new__(cls)
            cls.__parse_config()
        return cls._instance

    def get_ptd_property(self, property):

        """Method to get the value of a property in the uvn config file

        Parameters
        ----------

        """
        map_type_value = self.parser["ptd"][property]
        return ast.literal_eval(map_type_value)

    @classmethod
    def __parse_config(self):

        """parse a config file"""
        str_full_path = os.path.join(
            config.CONFIG_VALUES["config_folder"], "UVN", "ptd_config_ini"
        )
        self.parser = config.ConfigParser()
        self.parser.read(str_full_path)
