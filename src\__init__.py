"""
CDC (Configuration Data Converter) Project Source Package

This package contains the core functionality for different instruments in the CDC project:
- FCI (Flexible Combined Imager)
- IRS (Infrared Sounder)
- LI (Lightning Imager)
- UVN (Ultraviolet Visible Near-infrared)

The package provides:
- Instrument-specific functions and utilities
- XML generation and validation
- Binary file operations
- Common utility functions
- Standardized logging system
"""

__version__ = '1.0.0'

# Import basics setup first
from .utils import import_utils

# Import logger after basics is set up
from .logger_wrapper import logger, CDCLogger

# Import XML functions and create instrument-specific aliases
from .utils.xml_utils import (
    PAF_Update as generate_xml,
    PAF_Update_SSL as generate_ssl_xml,
    PAF_Update_FCI_REPSEQ as generate_repseq_xml,
    check_xml as validate_xml
)

# Create instrument-specific aliases for XML functions
fci_generate_xml = generate_xml
fci_validate_xml = validate_xml
irs_generate_xml = generate_xml
irs_validate_xml = validate_xml
li_generate_xml = generate_xml
li_validate_xml = validate_xml
uvn_generate_xml = generate_xml
uvn_validate_xml = validate_xml

# Initialize logger with default settings
logger.info("CDC Package initialized")

# Define public API
__all__ = [
    # Logging
    'logger',
    'CDCLogger',

    # Basics module
    'import_utils',

    # Generic XML functions
    'generate_xml',
    'generate_ssl_xml',
    'generate_repseq_xml',
    'validate_xml',

    # Instrument-specific XML aliases
    'fci_generate_xml',
    'fci_validate_xml',
    'irs_generate_xml',
    'irs_validate_xml',
    'li_generate_xml',
    'li_validate_xml',
    'uvn_generate_xml',
    'uvn_validate_xml'
]
