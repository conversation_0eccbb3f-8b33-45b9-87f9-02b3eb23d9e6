"""
XLDT Command Line Interface
==========================

Command-line tool for reading and analyzing XLDT files.
"""

import argparse
import json
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

try:
    from xldt_reader import XLDTReader
except ImportError:
    from utils.xldt_reader import XLDTReader

import logging
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="XLDT File Reader - Parse and analyze XLDT binary files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Read XLDT file with IRS configuration
  python xldt_cli.py input.bin --config config/IRS/IRS_SL_Conf.csv
  
  # Read XLDT file with FCI configuration
  python xldt_cli.py input.bin --config config/FCI/FCI_SL_Conf.csv
  
  # Read XLDT file without configuration (generic parsing)
  python xldt_cli.py input.bin
  
  # Export to JSON
  python xldt_cli.py input.bin --output output.json --format json
  
  # Validate CRC
  python xldt_cli.py input.bin --validate-crc
        """
    )
    
    parser.add_argument(
        "input_file",
        help="Path to the XLDT binary file to read"
    )
    
    parser.add_argument(
        "--config", "-c",
        help="Path to configuration CSV file (e.g., IRS_SL_Conf.csv, FCI_SL_Conf.csv)"
    )
    
    parser.add_argument(
        "--output", "-o",
        help="Output file path (default: print to stdout)"
    )
    
    parser.add_argument(
        "--format", "-f",
        choices=["json", "summary", "detailed"],
        default="summary",
        help="Output format (default: summary)"
    )
    
    parser.add_argument(
        "--validate-crc",
        action="store_true",
        help="Validate CRC checksum"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Configure logging
    if args.verbose:
        logger.setLevel("DEBUG")
    
    try:
        # Initialize reader
        reader = XLDTReader(config_path=args.config)
        
        # Read XLDT file
        parsed_data = reader.read_xldt_file(args.input_file)
        
        # Validate CRC if requested
        if args.validate_crc and 'CRC' in parsed_data['sections']:
            crc_valid = reader.validate_crc(
                parsed_data['raw_hex'],
                parsed_data['sections']['CRC']
            )
            parsed_data['crc_valid'] = crc_valid
        
        # Format output
        if args.format == "json":
            output = json.dumps(reader.export_to_dict(parsed_data), indent=2)
        elif args.format == "detailed":
            output = format_detailed_output(parsed_data)
        else:  # summary
            output = format_summary_output(parsed_data)
        
        # Write output
        if args.output:
            with open(args.output, 'w') as f:
                f.write(output)
            print(f"Output written to: {args.output}")
        else:
            print(output)
            
    except Exception as e:
        logger.error(f"Error processing XLDT file: {e}")
        sys.exit(1)


def format_summary_output(parsed_data):
    """Format summary output."""
    lines = []
    lines.append("XLDT File Summary")
    lines.append("=" * 50)
    
    # File info
    file_info = parsed_data.get('file_info', {})
    lines.append(f"File: {file_info.get('file_path', 'Unknown')}")
    lines.append(f"Size: {file_info.get('file_size', 0)} bytes")
    
    # Header info
    header = parsed_data.get('header')
    if header:
        lines.append(f"Format ID: {header.format_id}")
        lines.append(f"MM Slot: {header.mm_slot}")
    
    # CRC validation
    if 'crc_valid' in parsed_data:
        status = "VALID" if parsed_data['crc_valid'] else "INVALID"
        lines.append(f"CRC Status: {status}")
    
    # Sections summary
    sections = parsed_data.get('sections', {})
    lines.append(f"\nSections ({len(sections)}):")
    lines.append("-" * 30)
    
    for name, section in sections.items():
        if hasattr(section, 'length'):
            lines.append(f"  {name}: {section.length} bytes")
        else:
            lines.append(f"  {name}: {type(section).__name__}")
    
    return "\n".join(lines)


def format_detailed_output(parsed_data):
    """Format detailed output."""
    lines = []
    lines.append("XLDT File Detailed Analysis")
    lines.append("=" * 50)
    
    # File info
    file_info = parsed_data.get('file_info', {})
    lines.append("File Information:")
    for key, value in file_info.items():
        lines.append(f"  {key}: {value}")
    
    # Header
    header = parsed_data.get('header')
    if header:
        lines.append("\nXLDT Header:")
        lines.append(f"  Format ID: {header.format_id} (0x{header.format_id:04X})")
        lines.append(f"  MM Slot: {header.mm_slot} (0x{header.mm_slot:04X})")
        if header.body_length:
            lines.append(f"  Body Length: {header.body_length}")
    
    # CRC validation
    if 'crc_valid' in parsed_data:
        status = "VALID" if parsed_data['crc_valid'] else "INVALID"
        lines.append(f"\nCRC Validation: {status}")
    
    # Sections
    sections = parsed_data.get('sections', {})
    lines.append(f"\nSections ({len(sections)}):")
    lines.append("-" * 40)
    
    for name, section in sections.items():
        lines.append(f"\n{name}:")
        if hasattr(section, 'length'):
            lines.append(f"  Length: {section.length} bytes")
            lines.append(f"  Order: {section.order}")
            if section.msdf_id is not None:
                lines.append(f"  MSDF ID: {section.msdf_id}")
            
            # Show first few bytes of data
            hex_data = section.data.hex().upper()
            if len(hex_data) > 32:
                lines.append(f"  Data: {hex_data[:32]}... ({len(hex_data)} hex chars)")
            else:
                lines.append(f"  Data: {hex_data}")
        else:
            lines.append(f"  Type: {type(section).__name__}")
            if isinstance(section, dict):
                for key, value in section.items():
                    lines.append(f"  {key}: {value}")
    
    return "\n".join(lines)


if __name__ == "__main__":
    main()
