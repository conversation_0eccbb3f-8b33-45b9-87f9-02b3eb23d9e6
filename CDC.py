import warnings
from src.utils import import_utils

import platform
import shutil
import signal
import sys
import os
from typing import Optional
from src.logger_wrapper import logger
from src.gui import create_app
from src.utils.import_utils import config

# Global app reference for cleanup
_app: Optional[object] = None

def signal_handler(signum: int, frame) -> None:
    """Handle system signals for graceful shutdown."""
    logger.info(f"Received signal {signum}")
    if _app is not None:
        logger.info("Shutting down application...")
        _app.quit()
    sys.exit(0)

def clear_bytecode_cache(directory: str = ".") -> None:
    """Recursively clears the Python bytecode cache in the given directory and all its subdirectories."""
    for root, dirs, files in os.walk(directory, topdown=False): # walk bottom-up, to avoid issues with deleting directories before their children
        for dir_name in dirs:
            if dir_name == "__pycache__":
                cache_path = os.path.join(root, dir_name)
                try:
                    shutil.rmtree(cache_path)
                    logger.debug(f"Removed cache: {cache_path}")
                except Exception as e:
                    logger.debug(f"Error removing cache {cache_path}: {e}")
        for filename in files:
            if filename.endswith(".pyc"):
                pyc_file_path = os.path.join(root, filename)
                try:
                    os.remove(pyc_file_path)
                    logger.debug(f"Removed .pyc file: {pyc_file_path}")
                except Exception as e:
                    logger.debug(f"Error removing .pyc file {pyc_file_path}: {e}")
    logger.info("Cache cleared.")


def main() -> None:
    """Main application entry point."""
    global _app
    
    logger.info(f'{import_utils.config.CONFIG_VALUES["tool_name"]} {4.0}')
    logger.info("Script start")
    logger.info(f"PC Info {str(platform.uname())}")

    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        _app = create_app()
        _app.run()
    except Exception as e:
        logger.error(f"Error running application: {e}")
        raise
    finally:
        clear_bytecode_cache()
        logger.info("Program End")
        logger.info("That's all folks\n")
    

if __name__ == "__main__":
    main()
