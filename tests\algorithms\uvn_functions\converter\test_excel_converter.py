# Standard library imports
import os

import pytest
# Local imports
from src.algorithms.uvn_functions.converter.excel_converter import \
    ExcelConverter
from src.utils.import_utils import config
from tests.helpers import assert_excel_files, load_input_files


@pytest.fixture(scope="class")
def suspend_capture(pytestconfig):
    """Use to make user interactions in the test or order stops for modify configurations file, HW..."""

    class suspend_guard:
        def __init__(self):
            self.capmanager = pytestconfig.pluginmanager.getplugin("capturemanager")

        def __enter__(self):
            self.capmanager.suspend_global_capture(in_=True)

        def __exit__(self, _1, _2, _3):
            self.capmanager.resume_global_capture()

    yield suspend_guard()


@pytest.fixture()
def load_UVN_test_files(request):
    """load test configurations

    Args:
        request (_type_): use to attach instance variable
    """

    # Set global  variables for testing
    global satellite
    global instrument
    satellite = "MTG-S 1"
    instrument = "UVN"
    # Load input files
    load_input_files(instrument)

    # Read config file to get the location of the input and output directory
    input_directory = os.path.join(
        config.CONFIG_VALUES["test_files"], instrument, "Input"
    )

    output_directory = os.path.join(
        config.CONFIG_VALUES["test_files"], instrument, "Output"
    )

    # Set the paths of the input and output file
    test_file_name = "MTS1_UVN_PTD_ref.xlsx"
    output_excel_file = os.path.join(output_directory, test_file_name)
    input_excel_file = os.path.join(input_directory, test_file_name)

    # attach the instance variable to the request
    request.cls.input_excel_file = input_excel_file
    request.cls.output_excel_file = output_excel_file

    # Delete output file if exists
    os.remove(output_excel_file) if os.path.isfile(output_excel_file) else None

    yield

    # Delete output file if exists
    os.remove(output_excel_file) if os.path.isfile(output_excel_file) else None


@pytest.mark.usefixtures("load_UVN_test_files")
class TestExcelConverter:
    def test_excel_file_conversion(self):
        """Test aims to verify the  following methods of the class excel_file_conversion:

        ptd_dict_to_file_format(self,dict_ptd,output_file_path):

        This test verifies that:
        1. An excel file is correctly converted to a dictionary
        2. The dictionary generated is correctly converted to an excel ptd format
        3. A ptd excel format is correctly converted to a dictionary
        """

        # Execute the method  to generate a ptd dictionary format
        excel_conv = ExcelConverter()
        input_file_ptd_dict = excel_conv.convert_excel_to_dict(self.input_excel_file)

        # Generate the excel output test file from the dictionary
        excel_conv.convert_dict_to_excel(input_file_ptd_dict, self.output_excel_file)

        output_file_ptd_dict = excel_conv.convert_excel_to_dict(self.output_excel_file)

        # compare content of both dictionaries
        assert input_file_ptd_dict == output_file_ptd_dict
        assert assert_excel_files(
            expected_file_path=self.output_excel_file,
            actual_file_path=self.input_excel_file,
        )
