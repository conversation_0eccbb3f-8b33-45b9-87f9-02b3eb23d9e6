"""
XLDT Reader Tests
================

Unit tests for the XLDT reader functionality.
"""

import unittest
import tempfile
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

try:
    from utils.xldt_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>, XLDTHeader, XLDTSection
except ImportError:
    # Try alternative import path
    sys.path.append(str(Path(__file__).parent.parent / "src" / "utils"))
    from xldt_reader import XLDTReader, XLDTHeader, XLDTSection


class TestXLDTReader(unittest.TestCase):
    """Test cases for XLDT Reader."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.reader = XLDTReader()
        
        # Create temporary config file
        self.config_content = """Section,Length,MSDF_ID_Hex,Order
XLDT_Header,,,1
CRC,,,2
<PERSON><PERSON>,4,0,3
LAC_Pointer,20,3,4
Test_Section,8,5,5"""
        
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        self.temp_config.write(self.config_content)
        self.temp_config.close()
    
    def tearDown(self):
        """Clean up test fixtures."""
        if os.path.exists(self.temp_config.name):
            os.unlink(self.temp_config.name)
    
    def test_xldt_header_parsing(self):
        """Test XLDT header parsing."""
        # Test header: 0001 (format) + 0005 (MM slot 5)
        header_hex = "00010005"
        header = self.reader._parse_xldt_header(header_hex)
        
        self.assertIsInstance(header, XLDTHeader)
        self.assertEqual(header.format_id, 1)
        self.assertEqual(header.mm_slot, 5)
    
    def test_config_loading(self):
        """Test configuration file loading."""
        reader_with_config = XLDTReader(config_path=self.temp_config.name)
        
        self.assertIsNotNone(reader_with_config.config_df)
        self.assertIn('XLDT_Header', reader_with_config.sections_config)
        self.assertIn('Header', reader_with_config.sections_config)
        
        # Check specific config values
        header_config = reader_with_config.sections_config['Header']
        self.assertEqual(header_config['length'], 4)
        self.assertEqual(header_config['msdf_id_hex'], '0')
        self.assertEqual(header_config['order'], 3)
    
    def test_create_and_read_xldt(self):
        """Test creating and reading an XLDT file."""
        # Create XLDT file manually instead of using create_XLDT function
        def create_xldt_manual(hex_data: str, file_path: str):
            """Manually create XLDT file from hex data."""
            binary_data = bytes.fromhex(hex_data)
            with open(file_path, 'wb') as f:
                f.write(binary_data)
        
        # Create sample XLDT data
        # XLDT Header: 0001 (format) + 0003 (MM slot 3)
        # CRC: ABCD
        # Header section: 4 bytes of data
        # Test data: 8 bytes
        sample_hex = "00010003ABCD12345678DEADBEEFCAFEBABE"
        
        # Create temporary XLDT file
        with tempfile.NamedTemporaryFile(suffix='.bin', delete=False) as temp_file:
            temp_xldt_path = temp_file.name
        
        try:
            create_xldt_manual(sample_hex, temp_xldt_path)
            
            # Read it back
            reader_with_config = XLDTReader(config_path=self.temp_config.name)
            parsed_data = reader_with_config.read_xldt_file(temp_xldt_path)
            
            # Verify header
            self.assertIsNotNone(parsed_data['header'])
            self.assertEqual(parsed_data['header'].format_id, 1)
            self.assertEqual(parsed_data['header'].mm_slot, 3)
            
            # Verify file info
            self.assertIn('file_info', parsed_data)
            self.assertEqual(parsed_data['file_info']['file_path'], temp_xldt_path)
            
            # Verify sections
            self.assertIn('sections', parsed_data)
            
        finally:
            if os.path.exists(temp_xldt_path):
                os.unlink(temp_xldt_path)
    
    def test_generic_parsing(self):
        """Test generic parsing without configuration."""
        # Create a simple binary file
        sample_data = bytes.fromhex("00010007DEADBEEF")
        
        with tempfile.NamedTemporaryFile(suffix='.bin', delete=False) as temp_file:
            temp_file.write(sample_data)
            temp_xldt_path = temp_file.name
        
        try:
            # Read without configuration
            parsed_data = self.reader.read_xldt_file(temp_xldt_path)
            
            # Verify header parsing still works
            self.assertIsNotNone(parsed_data['header'])
            self.assertEqual(parsed_data['header'].format_id, 1)
            self.assertEqual(parsed_data['header'].mm_slot, 7)
            
            # Verify generic sections
            self.assertIn('sections', parsed_data)
            sections = parsed_data['sections']
            self.assertIn('remaining_hex', sections)
            
        finally:
            if os.path.exists(temp_xldt_path):
                os.unlink(temp_xldt_path)
    
    def test_export_to_dict(self):
        """Test exporting parsed data to dictionary."""
        # Create mock parsed data
        header = XLDTHeader(format_id=1, mm_slot=5)
        section = XLDTSection(
            section_name="Test",
            msdf_id=1,
            length=4,
            order=1,
            data=bytes.fromhex("DEADBEEF")
        )
        
        parsed_data = {
            'header': header,
            'sections': {'Test': section},
            'file_info': {'file_path': 'test.bin', 'file_size': 100}
        }
        
        # Export to dict
        result = self.reader.export_to_dict(parsed_data)
        
        # Verify structure
        self.assertIn('header', result)
        self.assertIn('sections', result)
        self.assertIn('file_info', result)
        
        # Verify header conversion
        self.assertEqual(result['header']['format_id'], 1)
        self.assertEqual(result['header']['mm_slot'], 5)
        
        # Verify section conversion
        self.assertIn('Test', result['sections'])
        test_section = result['sections']['Test']
        self.assertEqual(test_section['length'], 4)
        self.assertEqual(test_section['data_hex'], 'DEADBEEF')
    
    def test_invalid_file(self):
        """Test handling of invalid files."""
        # Test non-existent file
        with self.assertRaises(FileNotFoundError):
            self.reader.read_xldt_file('non_existent_file.bin')
    
    def test_empty_file(self):
        """Test handling of empty files."""
        # Create empty file
        with tempfile.NamedTemporaryFile(suffix='.bin', delete=False) as temp_file:
            temp_xldt_path = temp_file.name
        
        try:
            # Should handle gracefully
            parsed_data = self.reader.read_xldt_file(temp_xldt_path)
            self.assertIsNotNone(parsed_data)
            self.assertEqual(parsed_data['file_info']['file_size'], 0)
            
        finally:
            if os.path.exists(temp_xldt_path):
                os.unlink(temp_xldt_path)
    
    def test_malformed_config(self):
        """Test handling of malformed configuration files."""
        # Create malformed config
        malformed_config = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        malformed_config.write("Invalid,CSV,Content\n")
        malformed_config.close()
        
        try:
            # Should raise an exception
            with self.assertRaises(Exception):
                XLDTReader(config_path=malformed_config.name)
                
        finally:
            if os.path.exists(malformed_config.name):
                os.unlink(malformed_config.name)


class TestXLDTDataStructures(unittest.TestCase):
    """Test XLDT data structures."""
    
    def test_xldt_header(self):
        """Test XLDTHeader dataclass."""
        header = XLDTHeader(format_id=1, mm_slot=5)
        
        self.assertEqual(header.format_id, 1)
        self.assertEqual(header.mm_slot, 5)
        self.assertIsNone(header.body_length)
        
        # Test with body length
        header_with_length = XLDTHeader(format_id=1, mm_slot=5, body_length=100)
        self.assertEqual(header_with_length.body_length, 100)
    
    def test_xldt_section(self):
        """Test XLDTSection dataclass."""
        section = XLDTSection(
            section_name="Test",
            msdf_id=1,
            length=4,
            order=1,
            data=bytes.fromhex("DEADBEEF")
        )
        
        self.assertEqual(section.section_name, "Test")
        self.assertEqual(section.msdf_id, 1)
        self.assertEqual(section.length, 4)
        self.assertEqual(section.order, 1)
        self.assertEqual(section.data, bytes.fromhex("DEADBEEF"))


def run_tests():
    """Run all tests."""
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestXLDTReader))
    suite.addTests(loader.loadTestsFromTestCase(TestXLDTDataStructures))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
