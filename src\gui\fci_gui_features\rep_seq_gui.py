import tkinter as tk
from tkinter import ttk
from src.utils.import_utils import basics
from src import functions
from ..frames import BaseFrame
from ..custom_widgets import PrimaryCard
from src.logger_wrapper import logger
from ..theme_manager import ThemeManager


class REP_SEQ_GUI(BaseFrame):
    """Frame for FCI Repeat Sequence configuration"""
    def __init__(self, parent, act_params, *args, **kwargs):
        self.act_params = act_params
        self.input_repeat_seq = None # Initialize widget variable
        super().__init__(parent, *args, **kwargs)
        # Get application reference from parent
        # self.app = self.winfo_toplevel()


    def create_widgets(self):
        """Create and layout GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Remove bottom padding: padding=5 -> padding=(5, 5, 5, 0)
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew") # Use grid

        # Configure main layout rows/columns
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)


    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"FCI Repeat Sequence Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew")

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5)


    def _create_body(self, parent):
        """Creates the body section with the input card, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)

        # Configure grid to center the card
        body_frame.columnconfigure(0, weight=1) # Left spacer
        body_frame.columnconfigure(1, weight=0) # Content column
        body_frame.columnconfigure(2, weight=1) # Right spacer
        body_frame.rowconfigure(0, weight=1) # Top spacer
        body_frame.rowconfigure(1, weight=0) # Card row
        body_frame.rowconfigure(2, weight=1) # Bottom spacer

        # Repeat Sequence ID Card - Removed fixed sizes
        repeat_seq_card = PrimaryCard(body_frame, title="Repeat Sequence Configuration", padding=5)
        repeat_seq_card.grid(row=1, column=1, pady=10, sticky="ew") # Center column, expand horizontally        
        repeat_seq_content = repeat_seq_card.get_content_frame()
        frame_repeat = ttk.Frame(repeat_seq_content, style="PrimaryCard.TFrame")
        frame_repeat.pack(anchor="center", pady=5, padx=5) # Pack inside card

        ttk.Label(frame_repeat, text="Repeat Sequence ID:", style="PrimaryCard.TLabel").pack(side=tk.LEFT, padx=5)

        self.input_repeat_seq = ttk.Entry(frame_repeat, width=10)
        self.input_repeat_seq.pack(side=tk.LEFT, padx=5)
        self.input_repeat_seq.insert(0, "16384")


    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=2)
        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5) 
        controls_frame.columnconfigure(0, weight=1)  # Ensure the frame expands properly

        # Use ThemeManager instead of GUIBuilder
        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )


    def execute(self):
        """Handle execution of repeat sequence configuration with validation."""
        if self.input_repeat_seq is None:
             logger.error("Execute called before widgets created in REP_SEQ_GUI")
             basics.pop_up_message("Error", "GUI not fully initialized.", "error")
             return

        repeat_seq_id_val = self.input_repeat_seq.get()

        # Validation
        if not repeat_seq_id_val:
            basics.pop_up_message("Error", "Repeat Sequence ID cannot be empty.", "error")
            return
        int(repeat_seq_id_val) # Check if it's an integer. This will raise ValueError if not.

        # Update parameters
        self.act_params.repeat_sequence_id = repeat_seq_id_val

        functions.generate_outputs(self.act_params)
        basics.pop_up_message("Success", "Repeat Sequence configuration generated successfully.")

    def back(self):
        """Handle back navigation"""
        if self.app is not None and hasattr(self.app, "back"): # MODIFIED: Added check
            self.app.back()
        else: # MODIFIED: Added else block
            logger.error("No app instance with 'back' method available in REP_SEQ_GUI")
            basics.pop_up_message("Navigation Error", "No previous screen to return to.", "warning")
