# Standard library imports

import pytest
from src.algorithms.uvn_functions.converter.excel_converter import \
    ExcelConverter
# Local imports
from src.algorithms.uvn_functions.converter.memory_image_converter import \
    MemoryImageConverter
from src.algorithms.uvn_functions.converter.netcdf_Converter import \
    NetcdfConverter
from tests.helpers import (assert_img_files, assert_ncd_files, assert_obj,
                           load_input_files)
from tests.utils.test_uvn_utils import Helper


@pytest.fixture(scope="class")
def suspend_capture(pytestconfig):
    """Use to make user interactions in the test or order stops for modify configurations file, HW..."""

    class suspend_guard:
        def __init__(self):
            self.capmanager = pytestconfig.pluginmanager.getplugin("capturemanager")

        def __enter__(self):
            self.capmanager.suspend_global_capture(in_=True)

        def __exit__(self, _1, _2, _3):
            self.capmanager.resume_global_capture()

    yield suspend_guard()


@pytest.fixture()
def load_UVN_test_files(request):

    # Set global  variables for testing
    global satellite
    global instrument
    satellite = "MTG-S 1"
    instrument = "UVN"

    # Load input files
    load_input_files(instrument)

    # Setup test files directory
    input_file_xls, output_file_xls = Helper.get_xml_test_files()

    # Get excel test file ptd dictionary used for testing
    excel_conv = ExcelConverter()
    input_excel_ptd_dict = excel_conv.convert_excel_to_dict(input_file_xls)

    # Attach the variables to the request, thus they can be used as instance variables
    request.cls.input_excel_ptd_dict = input_excel_ptd_dict

    # Delete netcdf files of the previous run
    Helper.delete_test_files()

    yield

    # Cleanup
    Helper.delete_test_files()


@pytest.mark.usefixtures("load_UVN_test_files")
class TestExcelIntegration:
    def test_netcdf(self):
        """
        This test verifies that:
        1. The excel file ptd dictionary is correctly converted to a netcdf ptd file.
        2. The generated ptd netcdf file is equal  to the input netcdf file.

        """

        # Create a netcdf converter instance
        netcdf_conv = NetcdfConverter()
        test_input_ncd, test_output_ncd = Helper.get_netcdf_test_files()

        # Convert excel ptd dictionary to a netcdf format
        netcdf_conv.convert_dict_to_netcdf(self.input_excel_ptd_dict, test_output_ncd)

        # Convert the netcdf file format back to  a ptd dictionary
        dict_ptd_ncd = netcdf_conv.convert_netcdf_to_dict(test_output_ncd)

        # Compare resulting dictionaries against excel ptd dictionary:
        assert assert_obj(dict_ptd_ncd, self.input_excel_ptd_dict)

        # Compare resulting netcdf output object against netcdf input object:
        assert assert_ncd_files([test_output_ncd], [test_input_ncd])

    def test_img_memory(self):
        """Test excel file conversion to CDC UVN memory_image_file

        This test verifies that:
        1. The image memory file ptd dictionary is correctly converted to a image memory ptd file.
        2. The generated ptd image memory file is equal  to the input image memory file.

        """

        # Get test files
        input_file_img, output_file_img = Helper.get_image_memory_test_files()

        # Generate sdram memory image
        mem_img = MemoryImageConverter("SDRAM")

        # Generate image memories
        mem_img.convert_dict_to_image(
            dict_ptd=self.input_excel_ptd_dict,
            filename_img=output_file_img,
            icu_id="ICU_A",
            software_id="ASW",
        )
        # Convert the memory image file format back to  a ptd dictionary
        dict_ptd_img = mem_img.convert_image_to_dict(filename_img=output_file_img)

        # Compare resulting dictionaries against excel ptd dictionary:
        assert assert_obj(self.input_excel_ptd_dict, dict_ptd_img)

        # Compare resulting image memory output object against memory input object:
        assert assert_img_files([input_file_img], [output_file_img])
