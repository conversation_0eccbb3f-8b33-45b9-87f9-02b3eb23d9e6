"""Utility functions for handling imports across the codebase."""
import os
import sys
import config  # Import config at the top to avoid circular imports
from src.logger_wrapper import logger

def setup_basics_import():
    """Setup the path for importing basics module."""
    str_basic_path = os.path.join(os.path.dirname(__file__), "..", config.CONFIG_VALUES["basics"])
    if str_basic_path not in sys.path:
        sys.path.append(str_basic_path)
        logger.info(f"Added basics path: {str_basic_path}")

# Pre-configure basics import
setup_basics_import()
import basics  # type: ignore # noqa: E402
import mamut  # type: ignore # noqa: E402
import floppy  # type: ignore # noqa: E402

__all__ = ['basics', 'mamut', 'floppy', 'config']
