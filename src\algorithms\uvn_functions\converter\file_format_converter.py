"""Base class for all converter objects.

    This class is extended to create converters, and users may extend this
    class to write their own custom converters.

    """

from abc import ABC, abstractmethod

from ..utils.ptd_config_file_parser import PTDConfigFileParser


class FileFormatConverterInterface(ABC):
    def __init__(self):
        """Initialize the instance variables used in the subclasses

        Parameters
        ----------

        """
        self.ptd_parser = PTDConfigFileParser()
        self.map_type_xls2net = self.ptd_parser.get_ptd_property("map_type_xls2net")
        self.ptd_layout = self.ptd_parser.get_ptd_property("ptd_layout")
        self.map_type_xls2img = self.ptd_parser.get_ptd_property("map_type_xls2")
        self.lst_head_print = self.ptd_parser.get_ptd_property("lst_head_print")
        self.ptd_start = self.ptd_parser.get_ptd_property("ptd_start")
        self.pattern = self.ptd_parser.get_ptd_property("uvn_input_file_pattern")
        self.var_format = self.ptd_parser.get_ptd_property("var_format")
        self.map_type_xls2img = self.ptd_parser.get_ptd_property("map_type_xls2img")
