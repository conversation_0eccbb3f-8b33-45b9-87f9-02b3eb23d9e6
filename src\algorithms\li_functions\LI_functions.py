# Based on 'MTG LI FOM' MTG-GA-LI-MA-002
# Related Attachment MTG_LI_ASW_DataBank_05.00.02.xls
# Related Attachment 'MTG LI LOH-LME CICD' MTG-GA-LI-ICD-008 Iss 6

import math
import re
import os
from datetime import datetime

import pandas as pd

from src.logger_wrapper import logger
from src.utils.import_utils import basics, config
from src.utils.activity_params import ActivityParams
from src.functions import memory_image
from src.utils.netcdf_utils import read_netCDF
from src.utils.xml_utils import PAF_Update
from src.utils.conversion_utils import dec2hex, hex2dec, dec2bin, float_to_hex, bin2dec
from src.utils.excel_utils import Read_Sheet


str_instrument = "LI"
dict_config = config.CONFIG_INSTRUMENT[str_instrument]


def Packet_Creator(packet, Packet_DF, spare_value, TC_type):
    """Functions to create the packets in binary format
    Creating the specific byte sequence in binary string of each packet for any type of memory/TC
    """

    if TC_type == "Basic_Conf_Pix":
        for byte in sorted(set(Packet_DF["byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["byte_in_packet"] == byte]
            packet.append(
                dec2bin(Byte_DF.iloc[0]["pixel_mask_summer"]).zfill(1)
                + dec2bin(Byte_DF.iloc[0]["pixel_mask_winter"]).zfill(1)
                + dec2bin(Byte_DF.iloc[0]["pixel_offset"]).zfill(4)
                + dec2bin(Byte_DF.iloc[0]["pixel_noise_corr_en"]).zfill(1)
                + dec2bin(Byte_DF.iloc[0]["pixel_defect"]).zfill(1)
            )
        packet.append(dec2bin(hex2dec(spare_value)).zfill(8))

    elif TC_type == "Conf_Det_Thr":
        for byte in sorted(set(Packet_DF["byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["byte_in_packet"] == byte]
            packet.append(dec2bin(Byte_DF.iloc[0]["det_thr"]).zfill(8))
        packet.append(dec2bin(hex2dec(spare_value)).zfill(8))
        packet.append(dec2bin(hex2dec(spare_value)).zfill(8))

    elif TC_type == "Delta_Thr":
        for byte in sorted(set(Packet_DF["byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["byte_in_packet"] == byte]
            packet.append(
                (dec2bin(Byte_DF.iloc[1]["delta_thr"]).zfill(4))
                + (dec2bin(Byte_DF.iloc[0]["delta_thr"]).zfill(4))
            )

    elif TC_type == "Clamp":
        packet.append(
            (dec2bin(hex2dec(spare_value)).zfill(11))[0:8]
        )
        packet.append(
            (dec2bin(hex2dec(spare_value)).zfill(11))[8:]
            + (dec2bin(Packet_DF.iloc[0]["max_clamp"]).zfill(13))[0:5]
        )
        packet.append((dec2bin(Packet_DF.iloc[0]["max_clamp"]).zfill(13))[5:])
        packet.append(
            (dec2bin(hex2dec(spare_value)).zfill(3))
            + (dec2bin(Packet_DF.iloc[0]["min_clamp"]).zfill(13))[0:5]
        )
        packet.append((dec2bin(Packet_DF.iloc[0]["min_clamp"]).zfill(13))[5:])
        packet.append(dec2bin(hex2dec(spare_value)).zfill(8))

    elif TC_type == "Asic_Oper":
        packet.append(
            (dec2bin(hex2dec(spare_value)).zfill(5))
            + (dec2bin(Packet_DF.iloc[0]["edac"]).zfill(1))
            + (dec2bin(Packet_DF.iloc[0]["offset"]).zfill(1))
            + (dec2bin(Packet_DF.iloc[0]["clamping"]).zfill(1))
        )
        for byte in sorted(set(Packet_DF["byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["byte_in_packet"] == byte]
            packet.append((dec2bin(Byte_DF.iloc[0]["cloud_thr"]).zfill(8)))

    elif TC_type == "Filter_Thr":
        for byte in sorted(set(Packet_DF["byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["byte_in_packet"] == byte]
            packet.append(dec2bin(Byte_DF.iloc[0]["filter_thr"]).zfill(8))

        packet.append(
            (dec2bin(Packet_DF.iloc[0]["fpga"]).zfill(1))
            + (dec2bin(hex2dec(spare_value)).zfill(15)[0:7])
        )
        packet.append(dec2bin(hex2dec(spare_value)).zfill(15)[-8:])

    elif TC_type == "Noise_Corr":
        packet.append(dec2bin(hex2dec(spare_value)).zfill(8))

        for byte in sorted(set(Packet_DF["byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["byte_in_packet"] == byte]

            if byte < 10:
                packet.append(
                    (dec2bin(Byte_DF.iloc[3]["noise_corr"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[2]["noise_corr"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[1]["noise_corr"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[0]["noise_corr"]).zfill(2))
                )
            elif byte == 10:
                packet.append(
                    (dec2bin(hex2dec(spare_value)).zfill(2))
                    + (dec2bin(Byte_DF.iloc[2]["noise_corr"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[1]["noise_corr"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[0]["noise_corr"]).zfill(2))
                )
        packet.append(dec2bin(hex2dec(spare_value)).zfill(8))

    elif TC_type == "Offset_Lut":
        for byte in sorted(set(Packet_DF["byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["byte_in_packet"] == byte]
            packet.append(dec2bin(Byte_DF.iloc[0]["offset_lut"]).zfill(8))

        packet.append(dec2bin(hex2dec(spare_value)).zfill(16)[0:8])
        packet.append(dec2bin(hex2dec(spare_value)).zfill(16)[-8:])

    elif TC_type == "Oper_Conf":
        for byte in sorted(set(Packet_DF["rtpp_conf_byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["rtpp_conf_byte_in_packet"] == byte]
            packet.append(
                (dec2bin(Byte_DF.iloc[0]["cloud_thr_sel"]).zfill(2))
                + (dec2bin(Byte_DF.iloc[0]["dt_exclusion"]).zfill(1))
                + (dec2bin(Byte_DF.iloc[0]["global_det_en"]).zfill(1))
                + (dec2bin(Byte_DF.iloc[0]["delta_thr_en"]).zfill(1))
                + (dec2bin(Byte_DF.iloc[0]["kavg_sel"]).zfill(3))
            )
        for byte in sorted(set(Packet_DF["detlut_byte_in_packet"].to_list())):
            # DF for specific byte
            Byte_DF = Packet_DF[Packet_DF["detlut_byte_in_packet"] == byte]
            if byte < 23:
                packet.append(
                    (dec2bin(Byte_DF.iloc[3]["detlut_sel"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[2]["detlut_sel"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[1]["detlut_sel"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[0]["detlut_sel"]).zfill(2))
                )
            elif byte == 23:
                packet.append(
                    (dec2bin(hex2dec(spare_value)).zfill(2))
                    + (dec2bin(Byte_DF.iloc[2]["detlut_sel"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[1]["detlut_sel"]).zfill(2))
                    + (dec2bin(Byte_DF.iloc[0]["detlut_sel"]).zfill(2))
                )

        packet.append(dec2bin(hex2dec(spare_value)).zfill(24)[0:8])
        packet.append(dec2bin(hex2dec(spare_value)).zfill(24)[8:16])
        packet.append(dec2bin(hex2dec(spare_value)).zfill(24)[-8:])
        packet.append(
            (dec2bin(hex2dec(spare_value)).zfill(5)[-5:])
            + (dec2bin(Packet_DF.iloc[0]["SDTF_lut_sel"]).zfill(2))
            + (dec2bin(Packet_DF.iloc[0]["SDTF_enable"]).zfill(1))
        )

    return packet


def pixel_locator(int_row: int, int_col: int):
    """Identify the pixel-asic-rtpp relation
    Copied from Manual LI TC Specific Functions MTG-GA-LI-MA-002"""

    # Initialize Variables
    pixinfo = {}

    if int_row < 0 or int_row > 999 or int_col < 0 or int_col > 1169:
        logger.error("Pixel outside expected dimensions")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        if int_row < 500 and int_col < 585:
            asic_id = 0
            asic_row = int_row
            asic_col = int_col
        elif int_row < 500 and int_col >= 585:
            asic_id = 1
            asic_row = int_row
            asic_col = int_col - 585
        elif int_row >= 500 and int_col < 585:
            asic_id = 2
            asic_row = 999 - int_row
            asic_col = int_col
        elif int_row >= 500 and int_col >= 585:
            asic_id = 3
            asic_row = 999 - int_row
            asic_col = int_col - 585

        # Absolute
        pixinfo["row"] = int_row
        pixinfo["col"] = int_col
        pixinfo["asic_id"] = asic_id

        # Relative to ASIC
        pixinfo["asic_row"] = asic_row
        pixinfo["asic_col"] = asic_col
        pixinfo["asic_win_row"] = math.floor(asic_row / 10)
        pixinfo["asic_win_col"] = math.floor(asic_col / 13)
        pixinfo["win_counter"] = 45 * pixinfo["asic_win_row"] + pixinfo["asic_win_col"]

        # Absolute
        if asic_id < 2:
            pixinfo["rtpp_id"] = math.floor(int_col / 39)
        else:
            pixinfo["rtpp_id"] = math.floor(int_col / 39) + 30

    return pixinfo


def rtpp_locator(rtpp_id: int):
    rtppinfo = {}

    if rtpp_id < 0 or rtpp_id > 59:
        logger.error("RTPP outside detector (rtpp_id must be in [0,59])")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # Absolute
        rtppinfo["rtpp_id"] = rtpp_id
        rtppinfo["asic_id"] = math.floor(rtpp_id / 15)

        # Relative to ASIC
        rtppinfo["asic_rtpp_id"] = rtpp_id % 15

    return rtppinfo


# Functions to determine addresses (Flash, RAM, TC target)
def basic_conf_pixel_map(
    fee_id: int,
    row,
    col,
    TC_Config_Input,
):
    """BASIC CONF PIXEL
    Finding the absolute address for each packet for this type of TC, both for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    # Initialize Variables
    basic_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    # Get target address for TC for this pixel
    basic_conf_map["packet_tcpkt_addr"] = basic_conf_pixel_tcpkt_address(row, col)

    if basic_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # FLASH address calculation
        if 0 <= fee_id <= 3:
            flash_base_abs = hex2dec(
                TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
            )
        else:
            logger.error("Fee_id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        pixinfo = pixel_locator(row, col)

        # My Code modification # pixinfo['asic_id']*500 instead of bitshift --> The result was not making sense
        # Packet num
        pkt_num = 45 * (pixinfo["asic_id"] * 500 + pixinfo["asic_row"]) + math.floor(
            pixinfo["asic_col"] / 13
        )
        sector_num_rel = math.floor(
            pkt_num / TC_Config_Input.loc["Flash_Sector_Packets"]
        )

        # Relative Packet num to memory sector
        sector_pkt_num = pkt_num - (
            sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )

        # Absolute Address
        basic_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])

        # Absolute Packet num
        ram_pkt_num = fee_id * TC_Config_Input.loc["Number_of_Packets"] + pkt_num

        # Absolute Address
        basic_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )

        basic_conf_map["packet_number"] = pkt_num

    return basic_conf_map


def basic_conf_pixel_tcpkt_address(row, col):
    """Finding the TC target address for each packet for this type of TC, same for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    if row < 0 or row > 999 or col < 0 or col > 1169:
        logger.error("row/column outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        pixinfo = pixel_locator(row, col)
        tcpkt_addr = (
            (int("101", 2) << 21)
            + (pixinfo["asic_id"] << 19)
            + (pixinfo["asic_row"] << 10)
            + (13 * math.floor(pixinfo["asic_col"] / 13))
        )
        tcpkt_addr_bin = dec2bin(tcpkt_addr)

    return tcpkt_addr_bin


def basic_conf_det_thr_map(
    fee_id,
    asic_id,
    asic_rtpp_id,
    lut_portion,
    TC_Config_Input,
):
    """BASIC DET THR CONF
    Finding the absolute address for each packet for this type of TC, both for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    # For each RTPP, 1024 values are defined in the Look-Up Table (LUT)
    # 64 Packets per RTPP are needed to load the LUT values
    # lut_portion must be a parameter that increases at every cycle, for the same RTPP, from 0 to 63
    # Each packet will be then filled with 16 values of DET_THR taken from the input files. There must be 1024 values per RTPP

    # Initialize Variables
    basic_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    basic_conf_map["packet_tcpkt_addr"] = basic_conf_det_thr_tcpkt_address(
        asic_id, asic_rtpp_id, lut_portion
    )

    if basic_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # FLASH address calculation
        if 0 <= fee_id <= 3:
            flash_base_abs = hex2dec(
                TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
            )
        else:
            logger.error("Fee_Id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        # My Code modification # asic_id*15 instead of bitshift --> The result was not making sense
        pkt_num = 64 * ((asic_id * 15) + asic_rtpp_id) + lut_portion
        sector_num_rel = math.floor(
            pkt_num / TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        sector_pkt_num = (
            pkt_num - sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        basic_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])
        ram_pkt_num = fee_id * TC_Config_Input.loc["Number_of_Packets"] + pkt_num
        basic_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )
        basic_conf_map["packet_number"] = pkt_num

    return basic_conf_map


def basic_conf_det_thr_tcpkt_address(asic_id, asic_rtpp_id, lut_portion):
    """Finding the TC target address for each packet for this type of TC, same for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    if asic_id < 0 or asic_id > 3:
        logger.error("Asic Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    elif asic_rtpp_id < 0 or asic_rtpp_id > 14:
        logger.error("Asic RTPP Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    elif lut_portion < 0 or lut_portion > 63:
        logger.error("Lut portion outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        tcpkt_addr = (
            (int("100", 2) << 21)
            + (asic_id << 19)
            + (asic_rtpp_id << 15)
            + (int("11000", 2) << 10)
            + (lut_portion << 4)
            + int("0000", 2)
        )

        tcpkt_addr_bin = dec2bin(tcpkt_addr)

    return tcpkt_addr_bin


def basic_conf_delta_thr_map(
    fee_id,
    asic_id,
    TC_Config_Input,
):
    """BASIC CONF DELTA THR
    Finding the absolute address for each packet for this type of TC, both for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    # For each RTPP, 4 values are defined
    # Each Packet contains the 4 values for 15 RTPPs
    basic_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    basic_conf_map["packet_tcpkt_addr"] = basic_conf_delta_thr_tcpkt_address(asic_id)

    if basic_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        if fee_id not in [0, 1, 2, 3]:
            logger.error("Fee Id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        # FLASH address calculation
        flash_base_abs = hex2dec(
            TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
        )

        # Actually not needed, always zero....
        sector_num_rel = math.floor(
            asic_id / TC_Config_Input.loc["Flash_Sector_Packets"]
        )

        # Actually not needed, always zero....
        sector_pkt_num = (
            asic_id - sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )

        basic_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])
        ram_pkt_num = fee_id * TC_Config_Input.loc["Number_of_Packets"] + asic_id
        basic_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )

        basic_conf_map["packet_number"] = ram_pkt_num

    return basic_conf_map


def basic_conf_delta_thr_tcpkt_address(asic_id):
    """Finding the TC target address for each packet for this type of TC, same for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    if asic_id < 0 or asic_id > 3:
        logger.error("Asic ID outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        tcpkt_addr = (
            (int("100", 2) << 21) + (asic_id << 19) + int("1111000010000000000", 2)
        )

        tcpkt_addr_bin = dec2bin(tcpkt_addr)

    return tcpkt_addr_bin


def basic_conf_clamp_map(
    fee_id,
    rtpp_id,
    TC_Config_Input,
):
    """BASIC CONF CLAMP
    Finding the absolute address for each packet for this type of TC, both for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    # For each RTPP, 2 values are defined
    # Each Packet contains the 2 values for 1 RTPP

    # Initialize Variables
    basic_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    basic_conf_map["packet_tcpkt_addr"] = basic_conf_clamp_tcpkt_address(rtpp_id)

    if basic_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # FLASH address calculation
        if 0 <= fee_id <= 3:
            flash_base_abs = hex2dec(
                TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
            )
        else:
            logger.error("Fee_Id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        rtppinfo = rtpp_locator(rtpp_id)

        # My Code modification # asic_id*15 instead of bitshift --> The result was not making sense
        pkt_num = (rtppinfo["asic_id"] * 15) + rtppinfo["asic_rtpp_id"]

        # Actually not needed, always zero....
        sector_num_rel = math.floor(
            pkt_num / TC_Config_Input.loc["Flash_Sector_Packets"]
        )

        # Actually not needed, always zero....
        sector_pkt_num = (
            pkt_num - sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        basic_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])
        ram_pkt_num = fee_id * TC_Config_Input.loc["Number_of_Packets"] + pkt_num
        basic_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )

        basic_conf_map["packet_number"] = pkt_num

    return basic_conf_map


def basic_conf_clamp_tcpkt_address(rtpp_id):
    """Finding the TC target address for each packet for this type of TC, same for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    if rtpp_id < 0 or rtpp_id > 59:
        logger.error("RTPP Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        rtppinfo = rtpp_locator(rtpp_id)
        tcpkt_addr = (
            (int("100", 2) << 21)
            + (rtppinfo["asic_id"] << 19)
            + (rtppinfo["asic_rtpp_id"] << 15)
            + int("100000000000000", 2)
        )
        tcpkt_addr_bin = dec2bin(tcpkt_addr)

    return tcpkt_addr_bin


def basic_conf_asic_map(
    fee_id: int,
    asic_id,
    TC_Config_Input,
):
    """BASIC CONF ASIC
    Finding the absolute address for each packet for this type of TC, both for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    # For each ASIC, 1 packet
    # Each Packet contains the 2 enable flags and all the values for the RTPPs

    # Initialize Variables
    basic_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    basic_conf_map["packet_tcpkt_addr"] = basic_conf_asic_tcpkt_address(asic_id)
    if basic_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # FLASH address calculation
        if 0 <= fee_id <= 3:
            flash_base_abs = hex2dec(
                TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
            )

        else:
            logger.error("Fee_Id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        # Not needed, just for consistency
        sector_num_rel = math.floor(
            asic_id / TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        # Not needed, just for consistency
        sector_pkt_num = (
            asic_id - sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        basic_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])
        ram_pkt_num = 2 * fee_id * TC_Config_Input.loc["Number_of_Packets"] + asic_id
        basic_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )

        basic_conf_map["packet_number"] = asic_id

    return basic_conf_map


def basic_conf_asic_tcpkt_address(asic_id):
    """Finding the TC target address for each packet for this type of TC, same for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    if asic_id < 0 or asic_id > 3:
        logger.error("Asic Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        tcpkt_addr = (
            (int("100", 2) << 21) + (asic_id << 19) + int("1111000100000000000", 2)
        )
        tcpkt_addr_bin = dec2bin(tcpkt_addr)

    return tcpkt_addr_bin


def basic_conf_filter_thr_map(
    fee_id: int,
    asic_id,
    filter_id,
    lut_portion,
    TC_Config_Input,
):
    """BASIC CONF FILTER THR
    Finding the absolute address for each packet for this type of TC, both for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    # Initialize Variables
    basic_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    basic_conf_map["packet_tcpkt_addr"] = basic_conf_filter_thr_tcpkt_address(
        asic_id, filter_id, lut_portion
    )
    if basic_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Error: Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # FLASH address calculation
        if 0 <= fee_id <= 3:
            flash_base_abs = hex2dec(
                TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
            )
        else:
            logger.error("Fee Id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        # My Code modification # asic_id*4 instead of bitshift --> The result was not making sense
        pkt_num = 4 * ((asic_id * 4) + filter_id) + lut_portion

        # Not needed, for consistency
        sector_num_rel = math.floor(
            pkt_num / TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        sector_pkt_num = (
            pkt_num - sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        basic_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])
        ram_pkt_num = fee_id * TC_Config_Input.loc["Number_of_Packets"] + pkt_num
        basic_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )

        basic_conf_map["packet_number"] = pkt_num

    return basic_conf_map


def basic_conf_filter_thr_tcpkt_address(asic_id, filter_id, lut_portion):
    """Finding the TC target address for each packet for this type of TC, same for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    if asic_id < 0 or asic_id > 3:
        logger.error("Asic Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    elif filter_id < 0 or filter_id > 3:
        logger.error("Filter Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    elif lut_portion < 0 or lut_portion > 3:
        logger.error("Lut Portion outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        tcpkt_addr = (
            (int("0000000000001", 2) << 11)
            + (asic_id << 9)
            + (filter_id << 7)
            + (lut_portion << 5)
            + int("00000", 2)
        )
        tcpkt_addr_bin = dec2bin(tcpkt_addr).zfill(24)

    return tcpkt_addr_bin


def basic_conf_noise_corr_map(
    fee_id: int,
    asic_id,
    TC_Config_Input,
):
    """BASIC CONF NOISE CORR
    Finding the absolute address for each packet for this type of TC, both for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    # Initialize Variables
    basic_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    basic_conf_map["packet_tcpkt_addr"] = basic_conf_noise_corr_tcpkt_address(asic_id)

    if basic_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # FLASH address calculation
        if 0 <= fee_id <= 3:
            flash_base_abs = hex2dec(
                TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
            )

        else:
            logger.error("Fee Id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        pkt_num = asic_id
        # Not needed, for consistency
        sector_num_rel = math.floor(
            pkt_num / TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        sector_pkt_num = (
            pkt_num - sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        basic_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])
        ram_pkt_num = fee_id * TC_Config_Input.loc["Number_of_Packets"] + pkt_num
        basic_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )

        basic_conf_map["packet_number"] = pkt_num

    return basic_conf_map


def basic_conf_noise_corr_tcpkt_address(asic_id):
    """Finding the TC target address for each packet for this type of TC, same for Flash and RAM #
    Copied from Manual, but reviewed, understood and modified as needed"""

    if asic_id < 0 or asic_id > 3:
        logger.error("Asic Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        tcpkt_addr = (
            (int("100", 2) << 21) + (asic_id << 19) + int("1111000001000000000", 2)
        )
        tcpkt_addr_bin = dec2bin(tcpkt_addr)

    return tcpkt_addr_bin


def basic_conf_offset_lut_map(
    fee_id: int,
    rtpp_id,
    TC_Config_Input,
):
    """BASIC CONF OFFSET LUT Finding the absolute address for each packet for this type of TC, both for Flash and RAM #
    Copied from MTG-GA-LI-MA-002, but reviewed, understood and modified as needed"""

    # Initialize Variables
    basic_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    basic_conf_map["packet_tcpkt_addr"] = basic_conf_offset_lut_tcpkt_address(rtpp_id)

    if basic_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # FLASH address calculation
        if 0 <= fee_id <= 3:
            flash_base_abs = hex2dec(
                TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
            )
        else:
            logger.error("Fee Id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        rtppinfo = rtpp_locator(rtpp_id)

        # My Code modification # asic_id*15 instead of bitshift
        pkt_num = rtppinfo["asic_id"] * 15 + rtppinfo["asic_rtpp_id"]

        # Not needed, for consistency
        sector_num_rel = math.floor(
            pkt_num / TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        sector_pkt_num = (
            pkt_num - sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        basic_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])
        ram_pkt_num = fee_id * TC_Config_Input.loc["Number_of_Packets"] + pkt_num
        basic_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )

        basic_conf_map["packet_number"] = pkt_num

    return basic_conf_map


def basic_conf_offset_lut_tcpkt_address(rtpp_id):
    """BASIC CONF OFFSET LUT
    Finding the TC target address for each packet for this type of TC, same for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    if rtpp_id < 0 or rtpp_id > 59:
        logger.error("RTPP Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        rtppinfo = rtpp_locator(rtpp_id)
        tcpkt_addr = (
            (int("100", 2) << 21)
            + (rtppinfo["asic_id"] << 19)
            + (rtppinfo["asic_rtpp_id"] << 15)
            + int("111000000000000", 2)
        )
        tcpkt_addr_bin = dec2bin(tcpkt_addr)

    return tcpkt_addr_bin


def oper_conf_map(
    fee_id,
    asic_id,
    TC_Config_Input,
):
    """Finding the absolute address for each packet for this type of TC, both for Flash and RAM"""

    # Initialize Variables
    oper_conf_map = {}

    # To align with manufacturer Algorithm
    fee_id = fee_id - 1

    oper_conf_map["packet_tcpkt_addr"] = oper_conf_tcpkt_address(asic_id)

    if oper_conf_map["packet_tcpkt_addr"] == "NaN":
        logger.error("Packet target address could not be found")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        # FLASH address calculation
        if 0 <= fee_id <= 3:
            flash_base_abs = hex2dec(
                TC_Config_Input.loc[f"Flash_Start_FEE{fee_id}"]
            )
        else:
            logger.error("Fee Id outside expected range")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        pkt_num = asic_id

        # Not needed, for consistency
        sector_num_rel = math.floor(
            pkt_num / TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        sector_pkt_num = (
            pkt_num - sector_num_rel * TC_Config_Input.loc["Flash_Sector_Packets"]
        )
        oper_conf_map["packet_flash_addr_abs"] = (
            flash_base_abs
            + sector_num_rel * TC_Config_Input.loc["Flash_Sector_Size"]
            + sector_pkt_num * TC_Config_Input.loc["Packet_Bytes"]
        )

        # RAM address calculation
        ram_base = hex2dec(TC_Config_Input.loc["RAM_Start"])
        ram_pkt_num = (
            int(2) * fee_id * TC_Config_Input.loc["Number_of_Packets"] + pkt_num
        )
        oper_conf_map["packet_ram_addr"] = (
            ram_base + TC_Config_Input.loc["RAM_Packet_Size"] * ram_pkt_num
        )

        oper_conf_map["packet_number"] = pkt_num

    return oper_conf_map


def oper_conf_tcpkt_address(asic_id):
    """Finding the TC target address for each packet for this type of TC, same for Flash and RAM
    Copied from Manual, but reviewed, understood and modified as needed"""

    if asic_id < 0 or asic_id > 3:
        logger.error("Asic Id outside expected range")
        basics.pop_up_message(
            "Ouch!", "An error Occurred please check the logs.", "error"
        )
        raise RuntimeError()

    else:
        tcpkt_addr = (
            (int("100", 2) << 21) + (asic_id << 19) + int("1111000000000000000", 2)
        )
        tcpkt_addr_bin = dec2bin(tcpkt_addr)

    return tcpkt_addr_bin


def Conf_Pixel_TC_info(
    fee_id: int,
    dict_netCDF,
    TC_Config_Input,
):
    """Functions to extract the value for each byte for each packet and create dataframe with all the information
    BASIC CONF PIXEL
    Creating the dataframe with all the info needed to create the conf pixel TC"""

    logger.info("Basic_Conf_Pixel_TC Content Extraction")

    Conf_Pixel = []
    pixnum = 0

    params = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")

    pixels_defect = dict_netCDF[params[0]]
    pixels_noise_corr_en = dict_netCDF[params[1]]
    pixels_offset = dict_netCDF[params[2]]
    pixels_mask_winter = dict_netCDF[params[3]]
    pixels_mask_summer = dict_netCDF[params[4]]

    for row in range(1000):
        for col in range(1170):
            # Get target address
            conf_pixel_pkt_add = basic_conf_pixel_map(
                fee_id,
                row,
                col,
                TC_Config_Input,
            )

            # Get byte number in packet
            pix_per_packet = TC_Config_Input.loc["Item_Per_Packet"]

            byte_num = TC_Config_Input.loc["Byte_Start"] + math.floor(
                (pixnum % (pix_per_packet))
                / (pix_per_packet / TC_Config_Input.loc["Item_Bytes_Per_Packet"])
            )
            pixel_noise_corr_en = pixels_noise_corr_en[row][col]
            pixel_offset = pixels_offset[row][col]
            pixel_mask_winter = pixels_mask_winter[row][col]
            pixel_mask_summer = pixels_mask_summer[row][col]
            pixel_defect = pixels_defect[row][col]

            # Put all the information together
            Conf_Pixel.append(
                [
                    conf_pixel_pkt_add["packet_number"],
                    byte_num,
                    pixel_defect,
                    pixel_noise_corr_en,
                    pixel_offset,
                    pixel_mask_winter,
                    pixel_mask_summer,
                    conf_pixel_pkt_add["packet_flash_addr_abs"],
                    conf_pixel_pkt_add["packet_ram_addr"],
                    conf_pixel_pkt_add["packet_tcpkt_addr"],
                ]
            )

            pixnum += 1

    Conf_Pixel_DF = pd.DataFrame(
        Conf_Pixel,
        columns=[
            "packet_number",
            "byte_in_packet",
            "pixel_defect",
            "pixel_noise_corr_en",
            "pixel_offset",
            "pixel_mask_winter",
            "pixel_mask_summer",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Conf_Pixel_DF


def Conf_Det_Thr_TC_info(
    fee_id,
    dict_netCDF,
    TC_Config_Input,
):
    """BASIC CONF DET THR
    Creating the dataframe with all the info needed to create the conf det thr TC"""

    logger.info("Basic_Conf_Det_Threshold_TC Content Extraction")

    Det_Thr = []

    param = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")

    det_thrs = dict_netCDF[param[0]]

    for asic_id in range(4):
        for asic_rtpp_id in range(15):
            for lut_id in range(4):
                for lut_value in range(256):
                    rtpp_id = asic_rtpp_id + asic_id * 15

                    # Extracting the value for the byte
                    det_thr = det_thrs[lut_value][rtpp_id][lut_id]
                    lut_portion = math.floor(lut_value / 16) + lut_id * 16

                    # Packet target Add
                    conf_det_thr_pkt_add = basic_conf_det_thr_map(
                        fee_id,
                        asic_id,
                        asic_rtpp_id,
                        lut_portion,
                        TC_Config_Input,
                    )

                    # Byte number
                    byte_num = TC_Config_Input.loc["Byte_Start"] + math.floor(
                        (lut_value % TC_Config_Input.loc["Item_Per_Packet"])
                        / (
                            TC_Config_Input.loc["Item_Per_Packet"]
                            / TC_Config_Input.loc["Item_Bytes_Per_Packet"]
                        )
                    )

                    # Putting all information together
                    Det_Thr.append(
                        [
                            conf_det_thr_pkt_add["packet_number"],
                            byte_num,
                            det_thr,
                            conf_det_thr_pkt_add["packet_flash_addr_abs"],
                            conf_det_thr_pkt_add["packet_ram_addr"],
                            conf_det_thr_pkt_add["packet_tcpkt_addr"],
                        ]
                    )

    Conf_Det_Thr_DF = pd.DataFrame(
        Det_Thr,
        columns=[
            "packet_number",
            "byte_in_packet",
            "det_thr",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Conf_Det_Thr_DF


def Conf_Delta_Thr_TC_info(
    fee_id,
    dict_netCDF,
    TC_Config_Input,
):
    """BASIC CONF DELTA THR
    Creating the dataframe with all the info needed to create the conf delta thr TC"""

    logger.info("Basic_Conf_Delta_Threshold_TC Content Extraction")
    Delta_Thr = []
    param = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")
    delta_thrs = dict_netCDF[param[0]]

    for asic_id in range(4):
        # Packet target Add
        conf_delta_thr_pkt_add = basic_conf_delta_thr_map(
            fee_id,
            asic_id,
            TC_Config_Input,
        )

        for rtpp_id in range(15):
            rtpp_num = rtpp_id + asic_id * 15

            for value in range(4):
                # Extracting the value for the byte
                delta_thr = delta_thrs[rtpp_num][value]

                # Byte number
                byte_num = (
                    TC_Config_Input.loc["Byte_Start"]
                    + rtpp_id
                    * (
                        4
                        / (
                            TC_Config_Input.loc["Item_Per_Packet"]
                            / TC_Config_Input.loc["Item_Bytes_Per_Packet"]
                        )
                    )
                    + math.floor(
                        value
                        / (
                            TC_Config_Input.loc["Item_Per_Packet"]
                            / TC_Config_Input.loc["Item_Bytes_Per_Packet"]
                        )
                    )
                )

                # Putting all information together
                Delta_Thr.append(
                    [
                        conf_delta_thr_pkt_add["packet_number"],
                        byte_num,
                        delta_thr,
                        conf_delta_thr_pkt_add["packet_flash_addr_abs"],
                        conf_delta_thr_pkt_add["packet_ram_addr"],
                        conf_delta_thr_pkt_add["packet_tcpkt_addr"],
                    ]
                )

    Conf_Delta_Thr_DF = pd.DataFrame(
        Delta_Thr,
        columns=[
            "packet_number",
            "byte_in_packet",
            "delta_thr",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Conf_Delta_Thr_DF


def Conf_Clamp_TC_info(
    fee_id,
    dict_netCDF,
    TC_Config_Input,
):
    """BASIC CONF CLAMP
    Creating the dataframe with all the info needed to create the conf clamp TC"""

    logger.info("Basic_Conf_Clamp_TC Content Extraction")
    Clamp = []

    params = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")

    max_clamps = dict_netCDF[params[0]]
    min_clamps = dict_netCDF[params[1]]

    for asic_id in range(4):
        for artpp_id in range(15):
            rtpp_id = asic_id * 15 + artpp_id

            # Extracting the value for the byte
            max_clamp = max_clamps[asic_id][artpp_id]

            # Extracting the value for the byte
            min_clamp = min_clamps[asic_id][artpp_id]

            # Packet target Add
            conf_clamp_pkt_add = basic_conf_clamp_map(
                fee_id,
                rtpp_id,
                TC_Config_Input,
            )

            # Putting all information together
            Clamp.append(
                [
                    conf_clamp_pkt_add["packet_number"],
                    max_clamp,
                    min_clamp,
                    conf_clamp_pkt_add["packet_flash_addr_abs"],
                    conf_clamp_pkt_add["packet_ram_addr"],
                    conf_clamp_pkt_add["packet_tcpkt_addr"],
                ]
            )

    Conf_Clamp_DF = pd.DataFrame(
        Clamp,
        columns=[
            "packet_number",
            "max_clamp",
            "min_clamp",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Conf_Clamp_DF


def Conf_Asic_TC_info(
    fee_id,
    dict_netCDF,
    TC_Config_Input,
):
    """BASIC CONF ASIC
    Creating the dataframe with all the info needed to create the conf asic TC"""

    logger.info("Basic_Conf_Asic_TC Content Extraction")
    # Initialize Variables
    Asic = []

    params = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")

    edacs = dict_netCDF[params[0]]
    offsets = dict_netCDF[params[1]]
    clampings = dict_netCDF[params[2]]
    clouds = dict_netCDF[params[3]]

    for asic_id in range(4):
        edac = edacs[asic_id]
        clamping = clampings[asic_id]
        offset = offsets[asic_id]

        # Packet target Add
        conf_asic_pkt_add = basic_conf_asic_map(
            fee_id,
            asic_id,
            TC_Config_Input,
        )

        for rtpp_id in range(15):
            rtpp_num = rtpp_id + asic_id * 15
            for value in range(3):
                # Extracting the value for the byte
                cloud_thr = clouds[rtpp_num][value]

                # Byte number
                byte_num = (
                    TC_Config_Input.loc["Byte_Start"]
                    + rtpp_id
                    * (
                        3
                        / (
                            TC_Config_Input.loc["Item_Per_Packet"]
                            / TC_Config_Input.loc["Item_Bytes_Per_Packet"]
                        )
                    )
                    + math.floor(
                        value
                        / (
                            TC_Config_Input.loc["Item_Per_Packet"]
                            / TC_Config_Input.loc["Item_Bytes_Per_Packet"]
                        )
                    )
                )

                # Putting all information together
                conf_asic = [
                    conf_asic_pkt_add["packet_number"],
                    byte_num,
                    edac,
                    clamping,
                    offset,
                    cloud_thr,
                    conf_asic_pkt_add["packet_flash_addr_abs"],
                    conf_asic_pkt_add["packet_ram_addr"],
                    conf_asic_pkt_add["packet_tcpkt_addr"],
                ]
                Asic.append(conf_asic)

    Conf_Asic_DF = pd.DataFrame(
        Asic,
        columns=[
            "packet_number",
            "byte_in_packet",
            "edac",
            "clamping",
            "offset",
            "cloud_thr",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Conf_Asic_DF


def Conf_Filter_Thr_TC_info(
    fee_id,
    dict_netCDF,
    TC_Config_Input,
):
    """BASIC CONF FILTER THR
    Creating the dataframe with all the info needed to create the conf filter thr TC"""

    logger.info("Basic_Conf_Filter_Threshold_TC Content Extraction")
    Filter_Thr = []

    param = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")
    filter_thrs = dict_netCDF[param[0]]
    fpga = int(dict_netCDF[param[1]])

    for asic_id in range(4):
        for filter_id in range(4):
            for lut_value in range(128):
                # Extracting the value for the byte
                filter_thr = filter_thrs[lut_value][asic_id][filter_id]
                lut_portion = math.floor(lut_value / 32)
                # Packet target Add
                conf_filter_thr_pkt_add = basic_conf_filter_thr_map(
                    fee_id,
                    asic_id,
                    filter_id,
                    lut_portion,
                    TC_Config_Input,
                )

                # Byte number
                byte_num = TC_Config_Input.loc["Byte_Start"] + math.floor(
                    (lut_value % TC_Config_Input.loc["Item_Per_Packet"])
                    / (
                        TC_Config_Input.loc["Item_Per_Packet"]
                        / TC_Config_Input.loc["Item_Bytes_Per_Packet"]
                    )
                )

                # Putting all information together
                conf_filter_thr = [
                    conf_filter_thr_pkt_add["packet_number"],
                    byte_num,
                    fpga,
                    filter_thr,
                    conf_filter_thr_pkt_add["packet_flash_addr_abs"],
                    conf_filter_thr_pkt_add["packet_ram_addr"],
                    conf_filter_thr_pkt_add["packet_tcpkt_addr"],
                ]
                Filter_Thr.append(conf_filter_thr)

    Conf_Filter_Thr_DF = pd.DataFrame(
        Filter_Thr,
        columns=[
            "packet_number",
            "byte_in_packet",
            "fpga",
            "filter_thr",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Conf_Filter_Thr_DF


def Conf_Noise_Corr_TC_info(
    fee_id,
    dict_netCDF,
    TC_Config_Input,
):
    """BASIC CONF NOISE CORR
    Creating the dataframe with all the info needed to create the conf noise correction TC
    """

    logger.info("Basic_Conf_Noise_Correction_TC Content Extraction")
    Noise_Corr = []
    param = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")
    noise_corrs = dict_netCDF[param[0]]

    for rtpp_id in range(60):
        # Extracting the value for the byte
        noise_corr = noise_corrs[rtpp_id]
        rtppinfo = rtpp_locator(rtpp_id)
        asic_id = rtppinfo["asic_id"]
        asic_rtpp_id = rtppinfo["asic_rtpp_id"]

        # Packet target Add
        conf_noise_corr_pkt_add = basic_conf_noise_corr_map(
            fee_id,
            asic_id,
            TC_Config_Input,
        )

        # Byte number
        byte_num = TC_Config_Input.loc["Byte_Start"] + math.floor(
            asic_rtpp_id
            / (
                math.ceil(
                    TC_Config_Input.loc["Item_Per_Packet"]
                    / TC_Config_Input.loc["Item_Bytes_Per_Packet"]
                )
            )
        )

        # Putting all information together
        Noise_Corr.append(
            [
                conf_noise_corr_pkt_add["packet_number"],
                byte_num,
                noise_corr,
                conf_noise_corr_pkt_add["packet_flash_addr_abs"],
                conf_noise_corr_pkt_add["packet_ram_addr"],
                conf_noise_corr_pkt_add["packet_tcpkt_addr"],
            ]
        )

    Conf_Noise_Corr_DF = pd.DataFrame(
        Noise_Corr,
        columns=[
            "packet_number",
            "byte_in_packet",
            "noise_corr",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Conf_Noise_Corr_DF


def Conf_Offset_Lut_TC_info(
    fee_id,
    dict_netCDF,
    TC_Config_Input,
):
    """BASIC CONF OFFSET LUT
    Creating the dataframe with all the info needed to create the conf offset lut TC"""

    logger.info("Basic_Conf_Offset_Lut_TC Content Extraction")
    Offset_Lut = []
    param = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")
    offset_luts = dict_netCDF[param[0]]

    for rtpp_id in range(60):
        for lut_value in range(16):
            # Extracting the value for the byte
            offset_lut = offset_luts[lut_value][rtpp_id]

            # Packet target Add
            conf_offset_lut_pkt_add = basic_conf_offset_lut_map(
                fee_id,
                rtpp_id,
                TC_Config_Input,
            )

            # Byte number
            byte_num = TC_Config_Input.loc["Byte_Start"] + math.floor(
                lut_value
                / (
                    TC_Config_Input.loc["Item_Per_Packet"]
                    / TC_Config_Input.loc["Item_Bytes_Per_Packet"]
                )
            )

            # Putting all information together
            Offset_Lut.append(
                [
                    conf_offset_lut_pkt_add["packet_number"],
                    byte_num,
                    offset_lut,
                    conf_offset_lut_pkt_add["packet_flash_addr_abs"],
                    conf_offset_lut_pkt_add["packet_ram_addr"],
                    conf_offset_lut_pkt_add["packet_tcpkt_addr"],
                ]
            )

    Conf_Offset_Lut_DF = pd.DataFrame(
        Offset_Lut,
        columns=[
            "packet_number",
            "byte_in_packet",
            "offset_lut",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Conf_Offset_Lut_DF


def Oper_Conf_Oper_TC_info(
    fee_id,
    dict_netCDF,
    TC_Config_Input,
):
    logger.info("Oper_Conf_Oper_TC Content Extraction")

    Oper_Conf = []

    param = TC_Config_Input.loc["Variable_Name"].strip("][").split(";")

    kavg_sels = dict_netCDF[param[0]]
    delta_thr_ens = dict_netCDF[param[1]]
    global_det_ens = dict_netCDF[param[2]]
    dt_exclusions = dict_netCDF[param[3]]
    cloud_thr_sels = dict_netCDF[param[4]]
    detlut_sels = dict_netCDF[param[5]]
    SDTF_enables = dict_netCDF[param[6]]
    SDTF_lut_sels = dict_netCDF[param[7]]

    for rtpp_id in range(60):
        # Extracting the value for the byte
        kavg_sel = kavg_sels[rtpp_id]
        delta_thr_en = delta_thr_ens[rtpp_id]
        global_det_en = global_det_ens[rtpp_id]
        dt_exclusion = dt_exclusions[rtpp_id]
        cloud_thr_sel = cloud_thr_sels[rtpp_id]
        detlut_sel = detlut_sels[rtpp_id]
        rtppinfo = rtpp_locator(rtpp_id)
        asic_id = rtppinfo["asic_id"]
        SDTF_enable = SDTF_enables[asic_id]
        SDTF_lut_sel = SDTF_lut_sels[asic_id]
        asic_rtpp_id = rtppinfo["asic_rtpp_id"]

        # Packet target Add
        oper_conf_pkt_add = oper_conf_map(
            fee_id,
            asic_id,
            TC_Config_Input,
        )

        # Byte number
        rtpp_conf_byte_num = TC_Config_Input.loc["Rtpp_Conf_Byte_Start"] + math.floor(
            asic_rtpp_id
            / (
                math.ceil(
                    TC_Config_Input.loc["Rtpp_Conf_Per_Packet"]
                    / TC_Config_Input.loc["Rtpp_Conf_Bytes_Per_Packet"]
                )
            )
        )

        # Byte number
        detlut_byte_num = TC_Config_Input.loc["Detlut_Byte_Start"] + math.floor(
            asic_rtpp_id
            / (
                math.ceil(
                    TC_Config_Input.loc["Rtpp_Conf_Per_Packet"]
                    / TC_Config_Input.loc["Detlut_Bytes_Per_Packet"]
                )
            )
        )

        # Putting all information together
        Oper_Conf.append(
            [
                oper_conf_pkt_add["packet_number"],
                rtpp_conf_byte_num,
                detlut_byte_num,
                kavg_sel,
                delta_thr_en,
                global_det_en,
                dt_exclusion,
                cloud_thr_sel,
                detlut_sel,
                SDTF_enable,
                SDTF_lut_sel,
                oper_conf_pkt_add["packet_flash_addr_abs"],
                oper_conf_pkt_add["packet_ram_addr"],
                oper_conf_pkt_add["packet_tcpkt_addr"],
            ]
        )

    Oper_Conf_DF = pd.DataFrame(
        Oper_Conf,
        columns=[
            "packet_number",
            "rtpp_conf_byte_in_packet",
            "detlut_byte_in_packet",
            "kavg_sel",
            "delta_thr_en",
            "global_det_en",
            "dt_exclusion",
            "cloud_thr_sel",
            "detlut_sel",
            "SDTF_enable",
            "SDTF_lut_sel",
            "packet_flash_addr_abs",
            "packet_ram_addr",
            "packet_tcpkt_addr",
        ],
    )

    return Oper_Conf_DF


def checksum_function(packet, packet_n):
    """VALID ONLY FOR LI BASIC CONF and OPER CONF PACKETS CHECKSUM"""

    checksum = 0

    for i in range(1, len(packet)):
        byte_dim = len(packet[i])
        if byte_dim > 8:
            logger.warning(
                f"Byte_{i+1}_in packet_{packet_n+1}_exceeded byte dimensions. It has been truncated"
            )

            packet[i] = packet[i][-8:]

        if byte_dim < 8:
            logger.error(
                f"Byte_{i+1}_in packet_{packet_n+1}_is shorter than required byte dimensions."
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        checksum = int(packet[i], 2) + checksum

    checksum = dec2bin(checksum).upper().zfill(8)[-8:]

    return checksum


def Memory_Dict(TC_DF, TC_Config_Input, TC_type, activity_params):
    """Memory Dictionary Function
    Creating the generic byte sequence in binary string of each packet for any type of memory/TC
    """

    Memory_dict = {}

    logger.info(f"{TC_type}_TC Packets Creation")

    if TC_type == "Basic_Conf_Pix" and activity_params.test_mode is False :
        logger.info("Note: This will take around 40 minutes")
        basics.pop_up_message(
            "Go Banana!", "Note: This will take around 40 minutes", "info"
        )
    # To be fixed later on now it should just work coz I am tired of this mess
    if TC_type == "Oper_Conf":
        str_row = "Oper_Conf_Oper"
    else:
        str_row = TC_type

    firstbyte = dec2bin(
        hex2dec(TC_Config_Input.loc[str_row, "Generic_ID_Value"])
    ).zfill(8)
    secondbyte = dec2bin(
        hex2dec(TC_Config_Input.loc[str_row, "Specific_ID_Value"])
    ).zfill(8)

    # Create the packets one by one from the previously obtained dataframes
    for packet_n in sorted(set(TC_DF["packet_number"].to_list())):
        packet = [firstbyte]

        if TC_type != "Oper_Conf":
            packet.append(secondbyte)

        # DF for specific packet
        Packet_DF = TC_DF[TC_DF["packet_number"] == packet_n]
        # Packet target address byte part 1
        packet.append(Packet_DF.iloc[0]["packet_tcpkt_addr"][0:8])
        # Packet target address byte part 2
        packet.append(Packet_DF.iloc[0]["packet_tcpkt_addr"][8:16])
        # Packet target address byte part 3
        packet.append(Packet_DF.iloc[0]["packet_tcpkt_addr"][16:24])

        # Function specific for each TC
        packet = Packet_Creator(
            packet, Packet_DF, TC_Config_Input.loc[str_row, "Spare_Value"], TC_type
        )

        # Packet checksum calculation
        packet.append(checksum_function(packet, packet_n).upper())

        # For each packet number, array of all bytes in binary
        Memory_dict[packet_n] = packet

    return Memory_dict


def ImageWriter(
    Memory_File_Flag,
    Which_TCs,
    Image_All,
    str_output_folder: str,
    Memory_Type: str,
    fee_id,
    str_sat,
    TC_Config_Input,
    FileClass,
    part_n,
    activity_params,
    bln_merge: bool = False,
):
    """Main Image Writing function calling subfunction memory_image_file to actually create the image file and merge different images in one file is requested"""

    # Initialize Variables
    lst_decimal = []

    icid = activity_params.icid
    icidver = activity_params.icid_ver

    if Memory_Type == "RAM":
        to_look_at = "RAM_Start"
        device_id = "000".zfill(3)
        device = "LI_SDRAM"
    elif Memory_Type == "Flash":
        to_look_at = "Flash_Start_FEE0"
        device_id = "1".zfill(3)
        device = "LI_FLASH"

    for TCs in Which_TCs:
        lst_decimal.append(hex2dec(TC_Config_Input.loc[TCs, to_look_at]))

    Which_TCs_Sorted = [x for _, x in sorted(zip(lst_decimal, Which_TCs))]

    if Memory_File_Flag == "Single TC":
        if part_n == 0:
            if len(Which_TCs_Sorted) > 1:
                part_n = 1

        for TC in Which_TCs_Sorted:
            if part_n == 0:
                part = ""
            else:
                part = f"PART{part_n}_"            # Preparation of data for the filename details
            str_filename = f"{str_sat[0:4]}{str_sat[6]}_{FileClass}_OBS_MIMG_{device_id}_{device}_{datetime.now().strftime('%Y%m%dT%H%M%S')}_{part}{str(icid).zfill(2)[-2:]}{str(icidver).zfill(2)[-2:]}.IMG"
            str_path_full = os.path.join(str_output_folder, str_filename)
            memory_image_file(
                Image_All[TC],
                TC,
                fee_id,
                str_sat,
                device,
                part,
                activity_params,
                str_path_full,
                bln_merge,
            )
            part_n = part_n + 1

    elif Memory_File_Flag == "Combined TCs":
        if part_n == 0:
            part = ""
        else:
            part = f"PART{part_n}_"

        Image_Combined = [Image_All[Which_TCs_Sorted[0]][0]]

        for TC in Which_TCs_Sorted:
            for address in range(len(Image_All[TC]))[1:]:
                Image_Combined.append(Image_All[TC][address])        # Preparation of data for the filename details
        str_filename = f"{str_sat[0:4]}{str_sat[6]}_{FileClass}_OBS_MIMG_{device_id}_{device}_{datetime.now().strftime('%Y%m%dT%H%M%S')}_{part}{str(icid).zfill(2)[-2:]}{str(icidver).zfill(2)[-2:]}.IMG"
        str_path_full = os.path.join(str_output_folder, str_filename)
        memory_image_file(
            Image_Combined,
            Which_TCs_Sorted,
            fee_id,
            str_sat,
            device,
            part,
            activity_params,
            str_path_full,
            bln_merge,
        )


def memory_image_file(
    image,
    TC_Type,
    fee_id,
    str_sat: str,
    device,
    part,
    activity_params,
    str_filename: str,
    bln_merge: bool = False,
):
    """Writing the memory image"""

    icid = activity_params.icid
    icidver = activity_params.icid_ver

    Patch_Dict = {}
    ICID_Upd_Dict = {}
    ICIDVer_Upd_Dict = {}
    int_length_tot = 0
    content_tot = ""
    lst_data = []
    Dictionary = {"ICID": icid, "ICIDVER": icidver}
    CONF_Dict = {
        "Basic_Conf_Pix": "PIXEL",
        "Conf_Det_Thr": "DET_THR",
        "Delta_Thr": "DELTA_THR",
        "Clamp": "CLAMP",
        "Asic_Oper": "ASIC",
        "Asic_Calib": "ASIC",
        "Noise_Corr": "NOISE_CORR",
        "Offset_Lut": "OFFSET_LUT",
        "Filter_Thr": "FILTER_THR",
        "Oper_Conf_Init": "OPER_CONF",
        "Oper_Conf_Oper": "OPER_CONF",
    }
    ICID_Dict = {
        "FEE_1": "FA00E002",
        "FEE_2": "FA00E004",
        "FEE_3": "FA00E006",
        "FEE_4": "FA00E008",
    }
    ICIDVer_Dict = {
        "FEE_1": "FA00E00A",
        "FEE_2": "FA00E00C",
        "FEE_3": "FA00E00E",
        "FEE_4": "FA00E010",
    }

    if TC_Type != "LME_CONF":
        fee_n = fee_id
    else:
        fee_n = [1, 2, 3, 4]

    # Unit
    if device == "LI_FLASH":
        SAU = 2
    else:
        SAU = 1

    # Preparation of data for the Image File header structure
    for packets in range(len(image[1:])):
        int_length_tot += hex2dec(image[packets + 1][1])

        content_tot += image[packets + 1][2]

    # End Address (of the last memory data unit, in Hex)
    endaddress = dec2hex(
        hex2dec(image[-1][0]) + hex2dec(image[-1][1]) - SAU
    )

    # Checksum
    checksumhead = basics.checksum(content_tot, "fletcher16").upper().zfill(4)

    # Creation of the Header
    lst_data.append(
        "\n".join(
            [
                "ID=1",
                "VERSION=",
                f"DOMAIN={str_sat[0:2]}{str_sat[4]}{str_sat[6]}",
                "TYPE=PATCH",
                f"DESCRIPTION={device},for_LI_FEE_ID_{fee_n},TC_Type(s)_"
                + str(TC_Type).strip("][").replace(" ", "").replace("'", ""),
                "SOURCE=",
                f"CREATIONDATE={datetime.now().strftime('%Y-%m-%dT%H:%M:%S:%f')}",
                "MODEL=",
                "MODELVER=",
                f"DEVICE={device}",
                f"STARTADDR=0x{dec2hex(int(hex2dec(image[1][0]) / SAU))}",
                f"ENDADDR=0x{dec2hex(int(hex2dec(endaddress) / SAU))}",
                f"LENGTH={int(int_length_tot / SAU)}",
                f"CHECKSUM={checksumhead}",
                f"UNIT={SAU}" + "\n",
            ]
        )
    )

    # Creation of the "body" of the image
    for int_packets in range(len(image[1:])):
        # Create address, length and content lines
        str_data_step = "".join(

            [
                f"START={dec2hex(int(hex2dec(image[int_packets + 1][0]) / SAU))}",
                f",COUNT={dec2hex(int(hex2dec(image[int_packets + 1][1]) / SAU))}",
                f",DATA={image[int_packets + 1][2]}",
            ]
        )

        if int_packets == len(image[1:]) - 1:
            lst_data.append(str_data_step)
        else:
            lst_data.append(str_data_step + "\n")

    # Create the memory image string
    str_image = "".join(lst_data)

    # Create memory image file
    with open(str_filename, "wb") as obj_memory_image:
        obj_memory_image.write(bytes(str_image, "utf-8"))

    # Create associated PAFs
    Dictionary["S2K_FileName"] = os.path.basename(str_filename)

    # Generation of data for PAF - Case of BASIC/OPER CONF
    if TC_Type != "LME_CONF":
        # Generation of data for PAF - Case of BASIC CONF Single TC or OPER CONF
        if "[" not in str(TC_Type) or "Oper_Conf" in str(TC_Type):
            Patch_Dict["FEE_ID"] = f"FEE_{int(fee_id)}"
            Patch_Dict["IMG_FileName"] = Dictionary["S2K_FileName"]

            if device == "LI_FLASH":
                Patch_Dict["ADDRESS"] = dec2hex(
                    hex2dec(image[1][0]) + hex2dec("FA000000")
                )

                ICID_Upd_Dict["MEM_ID"] = "FLASH_ID"
                ICIDVer_Upd_Dict["MEM_ID"] = "FLASH_ID"
                ICID_Upd_Dict["ADDRESS"] = ICID_Dict[Patch_Dict["FEE_ID"]]
                ICIDVer_Upd_Dict["ADDRESS"] = ICIDVer_Dict[Patch_Dict["FEE_ID"]]
                ICID_Upd_Dict["DATA"] = dec2hex(Dictionary["ICID"]).zfill(4)
                ICIDVer_Upd_Dict["DATA"] = dec2hex(
                    Dictionary["ICIDVER"]
                ).zfill(4)
                ICID_Upd_Dict["CKS16"] = basics.checksum(
                    ICID_Upd_Dict["DATA"], "fletcher16"
                ).upper()
                ICIDVer_Upd_Dict["CKS16"] = basics.checksum(
                    ICIDVer_Upd_Dict["DATA"], "fletcher16"
                ).upper()
            else:
                Patch_Dict["ADDRESS"] = image[1][0]
                if "Oper_Conf" in str(TC_Type):
                    Patch_Dict["CONF_ID"] = CONF_Dict[TC_Type[0]]
                else:
                    Patch_Dict["CONF_ID"] = CONF_Dict[TC_Type]

                ICID_Upd_Dict["ICID"] = Dictionary["ICID"]
                ICID_Upd_Dict["ICID_VER"] = Dictionary["ICIDVER"]
                ICID_Upd_Dict["FEE_ID"] = f"FEE_{int(fee_id)}"

            Patch_Dict["LENGTH"] = int_length_tot
            Patch_Dict["CKS16"] = checksumhead

        # Generation of data for PAF - Case of BASIC CONF Combined TC Single FEE
        else:
            Patch_Dict["FEE_ID"] = f"FEE_{int(fee_id)}"
            Patch_Dict["IMG_FileName"] = Dictionary["S2K_FileName"]
            if device == "LI_FLASH":
                Patch_Dict["ADDRESS"] = dec2hex(
                    hex2dec(image[1][0]) + hex2dec("FA000000")
                )

                ICID_Upd_Dict["MEM_ID"] = "FLASH_ID"
                ICIDVer_Upd_Dict["MEM_ID"] = "FLASH_ID"
                ICID_Upd_Dict["ADDRESS"] = ICID_Dict[Patch_Dict["FEE_ID"]]
                ICIDVer_Upd_Dict["ADDRESS"] = ICIDVer_Dict[Patch_Dict["FEE_ID"]]
                ICID_Upd_Dict["DATA"] = dec2hex(Dictionary["ICID"]).zfill(4)
                ICIDVer_Upd_Dict["DATA"] = dec2hex(
                    Dictionary["ICIDVER"]
                ).zfill(4)
                ICID_Upd_Dict["CKS16"] = basics.checksum(
                    ICID_Upd_Dict["DATA"], "fletcher16"
                ).upper()
                ICIDVer_Upd_Dict["CKS16"] = basics.checksum(
                    ICIDVer_Upd_Dict["DATA"], "fletcher16"
                ).upper()

            else:
                Patch_Dict["ADDRESS"] = image[1][0]
                Patch_Dict["CONF_ID"] = "ALL_BASIC_CONF"
                ICID_Upd_Dict["ICID"] = Dictionary["ICID"]
                ICID_Upd_Dict["ICID_VER"] = Dictionary["ICIDVER"]
                ICID_Upd_Dict["FEE_ID"] = f"FEE_{int(fee_id)}"

            Patch_Dict["LENGTH"] = (
                hex2dec(endaddress) - hex2dec(image[1][0]) + 1 * SAU
            )
            Patch_Dict["CKS16"] = ""

        # Call of PAF function update to generate the PAFs
        if device == "LI_FLASH":
            PAF_Update(
                os.path.join(
                    config.CONFIG_VALUES["paf_folder"],
                    str_instrument,
                    config.CONFIG_VALUES["li_patch_flash_paf"],
                ),
                Patch_Dict,
                os.path.dirname(str_filename),
                str_sat,
                f"FEE{fee_id}",
                str_instrument,
                f"{icid}-Ver{icidver}{part[:-1]}",
                "",
                "",
                "",
            )

            # We only need one per Part for PAF N_LME_I331
            if (part[-2:-1] == "1" or part == "") and not bln_merge:
                PAF_Update(
                    os.path.join(
                        config.CONFIG_VALUES["paf_folder"],
                        str_instrument,
                        config.CONFIG_VALUES["li_icid_flash_paf"],
                    ),
                    ICID_Upd_Dict,
                    os.path.dirname(str_filename),
                    str_sat,
                    f"FEE{fee_id}",
                    str_instrument,
                    str(icid),
                    "",
                    "",
                    "",
                )

                PAF_Update(
                    os.path.join(
                        config.CONFIG_VALUES["paf_folder"],
                        str_instrument,
                        config.CONFIG_VALUES["li_icid_flash_paf"],
                    ),
                    ICIDVer_Upd_Dict,
                    os.path.dirname(str_filename),
                    str_sat,
                    f"FEE{fee_id}",
                    str_instrument,
                    f"Ver{icidver}",
                    "",
                    "",
                    "",
                )
        else:
            # Prevent the creation of pafs if we will create merge one
            if not bln_merge:
                PAF_Update(
                    os.path.join(
                        config.CONFIG_VALUES["paf_folder"],
                        str_instrument,
                        config.CONFIG_VALUES["li_patch_ram_paf"],
                    ),
                    Patch_Dict,
                    os.path.dirname(str_filename),
                    str_sat,
                    f"FEE{fee_id}",
                    str_instrument,
                    f"{icid}-Ver{icidver}{part[:-1]}",
                    "",
                    "",
                    "",
                )

            # PAF N_LIR_I032
            PAF_Update(
                os.path.join(
                    config.CONFIG_VALUES["paf_folder"],
                    str_instrument,
                    config.CONFIG_VALUES["li_icid_ram_paf"],
                ),
                ICID_Upd_Dict,
                os.path.dirname(str_filename),
                str_sat,
                f"FEE{fee_id}",
                str_instrument,
                f"{icid}-Ver{icidver}",
                "",
                "",
                "",
            )


def LI_Cal(
    activity_params,
    str_sat: str,
    lst_used_files: list,
    proc: str,
    str_procedures: str,
    int_fee: int,
):
    """LI Calibration Main Function"""

    dict_new = {}
    str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], str_sat)

    # Identify the netCDF file in which the "slicer" should be used to identify the configuration, for the different activities
    Main_Files = ["INSTCONF"]

    # Get the dictionary of values from the netCDF files for the desired slicing parameter #
    logger.info(f"Reading netCDF files for {proc}")

    slicer_1 = activity_params.icid
    slicer_2 = activity_params.icid_ver    # Read netCDF file
    dict_netCDF = read_netCDF(
        activity_params,
        str_sat,
        Main_Files,
        lst_used_files,
        dict_config,
        int_fee,
    )

    if activity_params.activity == "LI Calibration All":
        int_fee = "Alls"

    # Get the dictionary of values from the VSM file for the needed netCDF files #
    for File in lst_used_files:
        logger.info(f"Reading VSM file for {proc}")

        File_Tab = Read_Sheet(dict_config["vsm_path"], File)
        # Find only variables needed for the specific procedure
        File_Tab_Proc = File_Tab[File_Tab["Activity"].str.contains(proc)]
        File_Tab_Proc.set_index("Name", inplace=True)

        # Create dictionary with PAF variable name to netCDF value correspondence
        # This new dictionary replaces the original one with netCDF variable name to netCDF value correspondence
        # Based on VSM file containing PAF variable name to netCDF variable name correspondence
        for variable in File_Tab_Proc.index:
            str_PAF_variable = File_Tab_Proc.loc[variable, "Mnemonic in PAF"]

            if variable == "N_EXP_TIMES":
                calibrations = dict_netCDF[variable]

            if "#" in str_PAF_variable:
                # '#' marks the variables with dimensions >1, which in the VSM are a single entry but multiple entries in the PAF
                int_name = 0

                for specific in dict_netCDF[variable]:
                    int_name += 1
                    PAF_var_spec_name = str_PAF_variable.replace("#", str(int_name))
                    dict_new[PAF_var_spec_name] = specific

            else:
                dict_new[str_PAF_variable] = dict_netCDF[variable]

        dict_new["FEE_ID"] = f"FEE_{int_fee}"

    # Create new PAF file with new values for each needed variable
    PAF_Update(
        os.path.join(
            config.CONFIG_VALUES["paf_folder"], str_instrument, f"PAF_{proc}.xml"
        ),
        dict_new,
        str_output_folder,
        str_sat,
        f"FEE{int_fee}",
        str_instrument,
        f"{slicer_1}-Ver{slicer_2}",
        "",
        "",
        calibrations,
    )


def LI_BASIC_CONF(
    activity_params,
    fee_id,
    str_sat: str,
    File_List,
    Which_TCs,
    Memory_Type,
    TC_Config_Input,
    Memory_File_Flag,
    part_n,
    bln_merge: bool = False,
):
    """LI BASIC CONF Main Function"""

    Pixel_Packet_List = []
    Det_Thr_Packet_List = []
    Delta_Thr_Packet_List = []
    Clamp_Packet_List = []
    Asic_Packet_List = []
    Filter_Thr_Packet_List = []
    Noise_Corr_Packet_List = []
    Offset_Lut_Packet_List = []
    Image_All = {}
    Image_RAM_All = {}
    Image_Flash_All = {}

    str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], str_sat)

    # Get the values from the input file for the desired icid and icid_ver #
    logger.info("Reading netCDF files")
    Main_Files = ["INSTCONF"]

    icid = activity_params.icid
    icid_ver = activity_params.icid_ver
    bln_test = activity_params.test_mode

    # Read netCDF file
    dict_netCDF = read_netCDF(
        activity_params,
        str_sat,
        Main_Files,
        File_List,
        dict_config,
        fee_id,
    )

    if "Basic_Conf_Pix" in Which_TCs:
        # ----------- BASIC_CONF_PIXEL_TC Memory Image ----------------- #

        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Pixel_DF = Conf_Pixel_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Basic_Conf_Pix"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Pixel_Packet_List = Memory_Dict(
            Conf_Pixel_DF, TC_Config_Input, "Basic_Conf_Pix", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Basic_Conf_Pix"] = memory_image(
                Pixel_Packet_List,
                Conf_Pixel_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Basic_Conf_Pix"] = memory_image(
                Pixel_Packet_List,
                Conf_Pixel_DF,
                "RAM",
                dict_config["count_max"],
            )
            Image_Flash_All["Basic_Conf_Pix"] = memory_image(
                Pixel_Packet_List,
                Conf_Pixel_DF,
                "Flash",
                dict_config["count_max"],
            )

    if "Conf_Det_Thr" in Which_TCs:
        """BASIC_CONF_DET_THR_TC Memory Image Read variables from configuration file"""

        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Det_Thr_DF = Conf_Det_Thr_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Conf_Det_Thr"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Det_Thr_Packet_List = Memory_Dict(
            Conf_Det_Thr_DF, TC_Config_Input, "Conf_Det_Thr", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Conf_Det_Thr"] = memory_image(
                Det_Thr_Packet_List,
                Conf_Det_Thr_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Conf_Det_Thr"] = memory_image(
                Det_Thr_Packet_List,
                Conf_Det_Thr_DF,
                "RAM",
                dict_config["count_max"],
            )
            Image_Flash_All["Conf_Det_Thr"] = memory_image(
                Det_Thr_Packet_List,
                Conf_Det_Thr_DF,
                "Flash",
                dict_config["count_max"],
            )

    if "Delta_Thr" in Which_TCs:
        # ----------- BASIC_CONF_DELTA_THR_TC Memory Image ----------------- #
        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Delta_Thr_DF = Conf_Delta_Thr_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Delta_Thr"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Delta_Thr_Packet_List = Memory_Dict(
            Conf_Delta_Thr_DF, TC_Config_Input, "Delta_Thr", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Delta_Thr"] = memory_image(
                Delta_Thr_Packet_List,
                Conf_Delta_Thr_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Delta_Thr"] = memory_image(
                Delta_Thr_Packet_List,
                Conf_Delta_Thr_DF,
                "RAM",
                dict_config["count_max"],
            )
            Image_Flash_All["Delta_Thr"] = memory_image(
                Delta_Thr_Packet_List,
                Conf_Delta_Thr_DF,
                "Flash",
                dict_config["count_max"],
            )

    if "Clamp" in Which_TCs:
        # ----------- BASIC_CONF_CLAMP_TC Memory Image ----------------- #
        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Clamp_DF = Conf_Clamp_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Clamp"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Clamp_Packet_List = Memory_Dict(
            Conf_Clamp_DF, TC_Config_Input, "Clamp", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Clamp"] = memory_image(
                Clamp_Packet_List,
                Conf_Clamp_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Clamp"] = memory_image(
                Clamp_Packet_List,
                Conf_Clamp_DF,
                "RAM",
                dict_config["count_max"],
            )
            Image_Flash_All["Clamp"] = memory_image(
                Clamp_Packet_List,
                Conf_Clamp_DF,
                "Flash",
                dict_config["count_max"],
            )

    if "Asic_Calib" in Which_TCs:
        # ----------- BASIC_CONF_ASIC_TC Memory Image ----------------- #
        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Asic_DF = Conf_Asic_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Asic_Calib"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Asic_Packet_List = Memory_Dict(
            Conf_Asic_DF, TC_Config_Input, "Asic_Calib", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Asic_Calib"] = memory_image(
                Asic_Packet_List,
                Conf_Asic_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Asic_Calib"] = memory_image(
                Asic_Packet_List, Conf_Asic_DF, "RAM", dict_config["count_max"]
            )
            Image_Flash_All["Asic_Calib"] = memory_image(
                Asic_Packet_List,
                Conf_Asic_DF,
                "Flash",
                dict_config["count_max"],
            )

    if "Asic_Oper" in Which_TCs:
        # ----------- BASIC_CONF_ASIC_TC Memory Image ----------------- #
        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Asic_DF = Conf_Asic_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Asic_Oper"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Asic_Packet_List = Memory_Dict(
            Conf_Asic_DF, TC_Config_Input, "Asic_Oper", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Asic_Oper"] = memory_image(
                Asic_Packet_List,
                Conf_Asic_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Asic_Oper"] = memory_image(
                Asic_Packet_List, Conf_Asic_DF, "RAM", dict_config["count_max"]
            )
            Image_Flash_All["Asic_Oper"] = memory_image(
                Asic_Packet_List,
                Conf_Asic_DF,
                "Flash",
                dict_config["count_max"],
            )

    if "Filter_Thr" in Which_TCs:
        # ----------- BASIC_CONF_FILTER_THR_TC Memory Image ----------------- #
        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Filter_Thr_DF = Conf_Filter_Thr_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Filter_Thr"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Filter_Thr_Packet_List = Memory_Dict(
            Conf_Filter_Thr_DF, TC_Config_Input, "Filter_Thr", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Filter_Thr"] = memory_image(
                Filter_Thr_Packet_List,
                Conf_Filter_Thr_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Filter_Thr"] = memory_image(
                Filter_Thr_Packet_List,
                Conf_Filter_Thr_DF,
                "RAM",
                dict_config["count_max"],
            )
            Image_Flash_All["Filter_Thr"] = memory_image(
                Filter_Thr_Packet_List,
                Conf_Filter_Thr_DF,
                "Flash",
                dict_config["count_max"],
            )

    if "Noise_Corr" in Which_TCs:
        # ----------- BASIC_CONF_NOISE_CORR_TC Memory Image ----------------- #
        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Noise_Corr_DF = Conf_Noise_Corr_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Noise_Corr"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Noise_Corr_Packet_List = Memory_Dict(
            Conf_Noise_Corr_DF, TC_Config_Input, "Noise_Corr", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Noise_Corr"] = memory_image(
                Noise_Corr_Packet_List,
                Conf_Noise_Corr_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Noise_Corr"] = memory_image(
                Noise_Corr_Packet_List,
                Conf_Noise_Corr_DF,
                "RAM",
                dict_config["count_max"],
            )
            Image_Flash_All["Noise_Corr"] = memory_image(
                Noise_Corr_Packet_List,
                Conf_Noise_Corr_DF,
                "Flash",
                dict_config["count_max"],
            )

    if "Offset_Lut" in Which_TCs:
        # ----------- BASIC_CONF_OFFSET_LUT_TC Memory Image ----------------- #
        # Create Dataframe with all the info (values, address, target address, etc...)
        Conf_Offset_Lut_DF = Conf_Offset_Lut_TC_info(
            fee_id, dict_netCDF, TC_Config_Input.loc["Offset_Lut"]
        )

        # Create the Dictionary with the Binary Memory Packet for each Packet number
        Offset_Lut_Packet_List = Memory_Dict(
            Conf_Offset_Lut_DF, TC_Config_Input, "Offset_Lut", activity_params
        )

        # Create Memory Image file
        if Memory_Type == "Flash" or Memory_Type == "RAM":
            Image_All["Offset_Lut"] = memory_image(
                Offset_Lut_Packet_List,
                Conf_Offset_Lut_DF,
                Memory_Type,
                dict_config["count_max"],
            )
        elif Memory_Type == "RAM and Flash":
            Image_RAM_All["Offset_Lut"] = memory_image(
                Offset_Lut_Packet_List,
                Conf_Offset_Lut_DF,
                "RAM",
                dict_config["count_max"],
            )
            Image_Flash_All["Offset_Lut"] = memory_image(
                Offset_Lut_Packet_List,
                Conf_Offset_Lut_DF,
                "Flash",
                dict_config["count_max"],
            )

    if Memory_Type == "Flash" or Memory_Type == "RAM":
        ImageWriter(
            Memory_File_Flag,
            Which_TCs,
            Image_All,
            str_output_folder,
            Memory_Type,
            fee_id,
            str_sat,
            TC_Config_Input,
            dict_config["file_class"],
            part_n,
            activity_params,
            bln_merge,
        )
    elif Memory_Type == "RAM and Flash":
        ImageWriter(
            Memory_File_Flag,
            Which_TCs,
            Image_RAM_All,
            str_output_folder,
            "RAM",
            fee_id,
            str_sat,
            TC_Config_Input,
            dict_config["file_class"],
            part_n,
            activity_params,
            bln_merge,
        )
        ImageWriter(
            Memory_File_Flag,
            Which_TCs,
            Image_Flash_All,
            str_output_folder,
            "Flash",
            fee_id,
            str_sat,
            TC_Config_Input,
            dict_config["file_class"],
            part_n,
            activity_params,
            bln_merge,
        )


def LI_OPER_CONF(
    activity_params,
    fee_id,
    str_sat: str,
    File_List,
    Which_TCs,
    Memory_Type: str,
    TC_Config_Input,
    part_n,
    bln_merge: bool = False,
):
    """LI OPER CONF Main Function
    OPER_CONF_TC Memory Image"""

    dict_images = {}
    str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], str_sat)

    # Check what we need to do
    if Memory_Type == "RAM and Flash":
        lst_memories = ["RAM", "Flash"]
    else:
        lst_memories = [Memory_Type]

    # Get the values from the input file for the desired icid and icid_ver #
    logger.info("Reading netCDF files")
    Main_Files = ["INSTCONF"]

    icid = activity_params.icid
    icid_ver = activity_params.icid_ver
    bln_test = activity_params.test_mode

    # Read netCDF file
    dict_netCDF = read_netCDF(
        activity_params,
        str_sat,
        Main_Files,
        File_List,
        dict_config,
        fee_id,
    )

    # Create the memory images information
    for str_step in lst_memories:
        Image_All = {}
        if "Oper_Conf" in Which_TCs:
            # Create Dataframe with all the info (values, address, target address, etc...) for Oper Conf Oper
            Oper_Conf_Oper_DF = Oper_Conf_Oper_TC_info(
                fee_id,
                dict_netCDF,
                TC_Config_Input.loc["Oper_Conf_Oper"],
            )

            # Create the Dictionary with the Binary Memory Packet for each Packet number for Oper Conf Oper
            Oper_Conf_Oper_Packet_List = Memory_Dict(
                Oper_Conf_Oper_DF,
                TC_Config_Input,
                "Oper_Conf",
                activity_params,
            )
            Image_All["Oper_Conf_Oper"] = memory_image(
                Oper_Conf_Oper_Packet_List,
                Oper_Conf_Oper_DF,
                str_step,
                dict_config["count_max"],
            )

        dict_images[str_step] = Image_All

    # Create all the images files
    for str_memory in dict_images:
        ImageWriter(
            "Combined TCs",
            ["Oper_Conf_Oper"],
            dict_images[str_memory],
            str_output_folder,
            str_memory,
            fee_id,
            str_sat,
            TC_Config_Input,
            dict_config["file_class"],
            part_n,
            activity_params,
            bln_merge,
        )


def LI_LME_CONF(
    activity_params,
    str_sat: str,
    File_List,
    Memory_Type,
    Mem_Map,
    Mem_Map_Par,
    VSM_Tab,
    to_be_excluded,
    part_n,
    PAForPatch,
    Datapool_parameters,
    FEE_PAF,
    bln_merge: bool = False,
):
    """LI LME CONF Main Function"""
    lst_value = []
    lst_value_par = []
    lst_fees = [1, 2, 3, 4]
    lst_parameters = ["L88E349X", "L88E350X", "L88E351X", "L88E352X"]

    str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], str_sat)

    icid = activity_params.icid
    icid_ver = activity_params.icid_ver
    bln_test = activity_params.test_mode

    # STEP 1 - Create Dataframe with Memory Map and netCDF values for each mnemonic and address
    for int_fee in lst_fees:
        VSM_dict = {}
        VSM_dict_Par = {}
        cleaner = 0

        # Get the values from the input files (one per FEE) for the desired icid and icid_ver #
        logger.info(f"Reading netCDF files for FEE {int_fee}")

        # Read netCDF file
        dict_netCDF = read_netCDF(
            ["INSTCONF"],
            File_List,
            dict_config,
            icid,
            icid_ver,
            str_instrument,
            str_sat,
            int_fee,
        )

        # Dataframe with VSM variables and corresponding memory mnemonic, for all FEE, invovled in LME_CONF
        VSM_Clean = VSM_Tab[VSM_Tab["Activity"].str.contains("LME_CONF")]

        # Assign actual value to mnemonic/address --- Data Pools
        for variable in VSM_Clean["Name"]:
            if variable not in to_be_excluded:
                Mnem_Map = VSM_Clean[VSM_Clean["Name"] == variable]["Mnemonic in Map"]
                if "[" in Mnem_Map.values[0]:
                    # '[' marks the variables with at least a different mnemonic per FEE
                    Mnemonics = Mnem_Map.values[0].strip("][").split(";")
                    Mnemonic = Mnemonics[int_fee - 1]
                else:
                    Mnemonic = Mnem_Map.values[0]
                if Mnemonic != "":
                    if "(" in Mnemonic:
                        # '(' marks the variables with more than one mnemonic per single FEE
                        Mnemonics_ASIC = Mnemonic.strip(")(").split(",")
                        int_asic = 0
                        if (
                            "COMMON"
                            in VSM_Clean[VSM_Clean["Name"] == variable][
                                "Activity"
                            ].values[0]
                        ):
                            common_value = dict_netCDF[variable].tolist()
                            for asic_mnem in Mnemonics_ASIC:
                                VSM_dict[asic_mnem] = common_value
                        else:
                            for asic_value in dict_netCDF[variable].tolist():
                                VSM_dict[Mnemonics_ASIC[int_asic]] = asic_value
                                int_asic += 1
                    else:
                        VSM_dict[Mnemonic] = dict_netCDF[variable].tolist()

        # Assign actual value to mnemonic/address --- Parameters
        for variable in VSM_Clean["Name"]:
            if variable not in to_be_excluded:
                Mnem_Map_Par = VSM_Clean[VSM_Clean["Name"] == variable][
                    "Mnemonic in Parameters"
                ]
                if "[" in Mnem_Map_Par.values[0]:
                    # '[' marks the variables with at least a different mnemonic per FEE
                    Mnemonics = Mnem_Map_Par.values[0].strip("][").split(";")
                    Mnemonic_Par = Mnemonics[int_fee - 1]
                else:
                    Mnemonic_Par = Mnem_Map_Par.values[0]
                if Mnemonic_Par != "":
                    if "(" in Mnemonic_Par:
                        # '(' marks the variables with more than one mnemonic per single FEE
                        Mnemonics_ASIC = Mnemonic_Par.strip(")(").split(",")
                        int_asic = 0
                        if (
                            "COMMON"
                            in VSM_Clean[VSM_Clean["Name"] == variable][
                                "Activity"
                            ].values[0]
                        ):
                            common_value = dict_netCDF[variable].tolist()
                            for asic_mnem in Mnemonics_ASIC:
                                VSM_dict_Par[asic_mnem] = common_value
                        else:
                            for asic_value in dict_netCDF[variable].tolist():
                                VSM_dict_Par[Mnemonics_ASIC[int_asic]] = asic_value
                                int_asic += 1
                    else:
                        VSM_dict_Par[Mnemonic_Par] = dict_netCDF[variable].tolist()

        # Create dataframe with memory mnemonic to netCDF value to Address correspondence --- Data Pools
        # One column per FEE, because of different mnemonics interested
        Map_Clean = Mem_Map[
            Mem_Map["Parameter Logical Identifier"].isin(list(VSM_dict.keys()))
        ]
        Map_Clean[f"FEE{int_fee}"] = Map_Clean["Parameter Logical Identifier"].map(
            VSM_dict
        )

        if "Map_All" in locals():
            Map_All = Map_All.merge(Map_Clean, how="outer")
        else:
            Map_All = Map_Clean

        # Create dataframe with memory mnemonic to netCDF value to Address correspondence --- Parameters
        # One column per FEE, because of different mnemonics interested
        for prmt in Mem_Map_Par["Parameter Mnemonic"]:
            Mem_Map_Par["Parameter Mnemonic"][cleaner] = prmt.strip(" ")
            cleaner += 1

        Map_Clean_Par = Mem_Map_Par[
            Mem_Map_Par["Parameter Mnemonic"].isin(list(VSM_dict_Par.keys()))
        ]

        Map_Clean_Par[f"FEE{int_fee}"] = Map_Clean_Par["Parameter Mnemonic"].map(
            VSM_dict_Par
        )

        if "Map_All_Par" in locals():
            Map_All_Par = Map_All_Par.merge(Map_Clean_Par, how="outer")
        else:
            Map_All_Par = Map_Clean_Par

    # DF Check that the common parts are the same across the files --- Data Pools
    for line in Map_All.index.to_list():
        single_row = Map_All.iloc[line]
        FEE_Only = []

        for fee_n in lst_fees:
            FEE_Only.append(single_row[f"FEE{fee_n}"])
        FEE_Only_clean = [x for x in FEE_Only if str(x) != "nan"]

        if isinstance(FEE_Only_clean[0], list):
            int_dimension = len(FEE_Only_clean[0])
        else:
            int_dimension = 1

        if int_dimension < 2:
            if len(set(FEE_Only_clean)) > 1:
                logger.warning(
                    f"The four files for the four FEEs contain different values for the common variable {single_row['Parameter Logical Identifier']}. Output should not be trusted"
                )
                basics.pop_up_message(
                    "WTH",
                    f"Warning: The four files for the four FEEs contain different values for the common variable {single_row['Parameter Logical Identifier']}. Output should not be trusted"
                    "warning",
                )
        else:
            if len(FEE_Only_clean) > 1:
                for lenght in range(int_dimension):
                    For_All_Fee = []

                    for numbers in lst_fees:
                        For_All_Fee.append(FEE_Only_clean[numbers - 1][lenght])

                    if len(set(For_All_Fee)) > 1:
                        logger.warning(
                            f"The four files for the four FEEs contain different values for the common variable {single_row['Parameter Logical Identifier']}. Output should not be trusted"
                        )
                        basics.pop_up_message(
                            "WTH",
                            f"The four files for the four FEEs contain different values for the common variable {single_row['Parameter Logical Identifier']}. Output should not be trusted",
                            "warning",
                        )

    # DF Check that the common parts are the same across the files --- Parameters
    for line in Map_All_Par.index.to_list():
        single_row = Map_All_Par.iloc[line]
        FEE_Only = []

        for fee_n in lst_fees:
            FEE_Only.append(single_row[f"FEE{fee_n}"])

        FEE_Only_clean = [x for x in FEE_Only if str(x) != "nan"]

        if isinstance(FEE_Only_clean[0], list):
            int_dimension = len(FEE_Only_clean[0])
        else:
            int_dimension = 1

        if int_dimension < 2:
            if len(set(FEE_Only_clean)) > 1:
                logger.warning(
                    f"Warning: The four files for the four FEEs contain different values for the common variable {single_row['Parameter Logical Identifier']}. Output should not be trusted"
                )
                basics.pop_up_message(
                    "WTH",
                    f"The four files for the four FEEs contain different values for the common variable {single_row['Parameter Logical Identifier']}. Output should not be trusted",
                    "warning",
                )
        else:
            if len(FEE_Only_clean) > 1:
                for lenght in range(int_dimension):
                    For_All_Fee = []
                    for numbers in lst_fees:
                        For_All_Fee.append(FEE_Only_clean[numbers - 1][lenght])

                    if len(set(For_All_Fee)) > 1:
                        logger.warning(
                            f"Warning: The four files for the four FEEs contain different values for the common variable {single_row['Parameter Logical Identifier']}. Output should not be trusted"
                        )
                        basics.pop_up_message(
                            "WTH",
                            f"The four files for the four FEEs contain different values for the common variable {single_row['Parameter Logical Identifier']}. Output should not be trusted",
                            "warning",
                        )

    # Create final dataframe with single "Values" column merging all FEEs --- Data Pools
    for line in Map_All.index.to_list():
        FEEx = -len(lst_fees)
        rw = Map_All.iloc[line]
        first = rw[FEEx]
        value_to_take = first

        if not isinstance(first, list):
            while pd.isnull(first):
                first = rw[FEEx + 1]
                value_to_take = first
                if isinstance(first, list):
                    first = first[0]
                FEEx += 1
        lst_value.append(value_to_take)

    Map_All["Value"] = lst_value

    # Create final dataframe with single "Values" column merging all FEEs --- Parameters
    Indexes_Par = Map_All_Par.index.to_list()

    for line in Indexes_Par:
        FEEx = -len(lst_fees)
        rw = Map_All_Par.iloc[line]
        first = rw[FEEx]
        value_to_take = first

        if not isinstance(first, list):
            while pd.isnull(first):
                first = rw[FEEx + 1]
                value_to_take = first
                if isinstance(first, list):
                    first = first[0]
                FEEx += 1
        lst_value_par.append(value_to_take)

    Map_All_Par["Value_Par"] = lst_value_par

    # STEP 2 (Patch ONLY) - Create Memory List with for each address the content in Hex from the values in the dataframe
    if PAForPatch == "Patch":
        Bytes_Dict_Flash = []
        Bytes_Dict_Flash_Par = []
        Bytes_Dict_RAM = [["Start", "Count", "Data"]]

        if Memory_Type == "RAM and Flash":
            lst_memory = ["Flash", "RAM"]
        else:
            lst_memory = [Memory_Type]

        for str_memory in lst_memory:
            # Data Pools
            lst_addresses = sorted(set(Map_All[f"{str_memory} Addr (Hex)"].to_list()))

            for add in lst_addresses:
                Bytes = ""
                AddLen = 0

                Map_Add = Map_All[Map_All[f"{str_memory} Addr (Hex)"] == add]
                lst_starts = sorted(Map_Add["Start Bit"].to_list(), reverse=True)

                for int_sb in lst_starts:
                    Map_Bit = Map_Add[Map_Add["Start Bit"] == int_sb]
                    Val = Map_Bit["Value"].values[0]
                    Typ = Map_Bit["Type"].values[0]
                    Len = int(Map_Bit["Length (bits/ bytes)"].values[0])

                    if Typ == "float":
                        Bytes_Content = float_to_hex(Val).zfill(Len * 2)
                    elif Typ == "bitfield":
                        # At the moment not required, to be addressed later
                        Bytes_Content = "N/A"
                    elif Typ == "struct":
                        # At the moment not required, to be addressed later
                        Bytes_Content = "N/A"
                    elif Typ == "bytes sequence":
                        Bytes_Content = ""
                        for byte in Val:
                            Bytes_Content += dec2hex(byte).zfill(2)
                    elif (
                        Typ == "unsigned char"
                        or Typ == "unsigned short"
                        or Typ == "unsigned int"
                        or Typ == "undefined"
                    ):
                        if (
                            Map_Bit["Parameter Logical Identifier"].values[0]
                            in lst_parameters
                        ):
                            Int_Val = int(round((Val[0] - 3.5) / 10.032))
                            Bytes_Content = dec2hex(Int_Val).zfill(Len * 2)
                        else:
                            # DUE TO MASKED ARRAYS VALUES
                            Bytes_Content = dec2hex(int(Val)).zfill(Len * 2)

                    Bytes += Bytes_Content
                    AddLen += Len

                # Note:Assume LSB 0 Bit Numbering, i.e. 10101010 is bits 7-6-5-4-3-2-1-0, so Start bit 15 is the MSB
                if 15 not in lst_starts:
                    add_new = dec2hex(
                        hex2dec(Map_Add[f"{str_memory} Addr (Hex)"].values[0])
                        + 1
                    ).zfill(len(add))
                    add = add_new

                # Check the data unit is not longer than 255 bytes, if it is, it is split for the memory image file
                if str_memory == "Flash":
                    Count_Max_Check = dict_config["count_max"] * 2
                elif str_memory == "RAM":
                    Count_Max_Check = dict_config["count_max"]

                if AddLen > (Count_Max_Check):
                    logger.info(
                        "Note: The data dimension exceeds the maximum number of memory data unit per line in the memory image. The data has been split in the file"
                    )

                    if str_memory == "Flash":
                        Bytes_Dict_Flash.append(
                            [
                                add,
                                dec2hex(Count_Max_Check),
                                Bytes[0 : Count_Max_Check * 2],
                            ]
                        )
                    elif str_memory == "RAM":
                        Bytes_Dict_RAM.append(
                            [
                                add,
                                dec2hex(Count_Max_Check),
                                Bytes[0 : Count_Max_Check * 2],
                            ]
                        )

                    line_slices = math.floor(AddLen / Count_Max_Check)

                    for slices in range(line_slices):
                        # TBD depending Flash address LME
                        add_2 = dec2hex(
                            hex2dec(add) + Count_Max_Check * (slices + 1)
                        ).zfill(len(add))
                        if slices == line_slices - 1:
                            Number_Bytes = (AddLen) % Count_Max_Check
                            End_index = (AddLen) * 2
                        else:
                            Number_Bytes = Count_Max_Check
                            End_index = (
                                (Count_Max_Check * (slices + 1)) * 2
                            ) + Count_Max_Check * 2
                        Start_index = (Count_Max_Check * (slices + 1)) * 2

                        if Number_Bytes != 0:
                            if str_memory == "Flash":
                                Bytes_Dict_Flash.append(
                                    [
                                        add_2,
                                        dec2hex(Number_Bytes),
                                        Bytes[Start_index:End_index],
                                    ]
                                )
                            elif str_memory == "RAM":
                                Bytes_Dict_RAM.append(
                                    [
                                        add_2,
                                        dec2hex(Number_Bytes),
                                        Bytes[Start_index:End_index],
                                    ]
                                )
                else:
                    if str_memory == "Flash":
                        Bytes_Dict_Flash.append([add, dec2hex(AddLen), Bytes])
                    elif str_memory == "RAM":
                        Bytes_Dict_RAM.append([add, dec2hex(AddLen), Bytes])

            # Parameters
            if str_memory == "Flash":
                Addresses_Par = list(Map_All_Par[f"{str_memory} Addr (Hex)"].values)

                for add in sorted(set(Addresses_Par)):
                    Map_Add_Par = Map_All_Par[
                        Map_All_Par[f"{str_memory} Addr (Hex)"] == add
                    ]
                    Bytes = ""
                    Val = Map_Add_Par["Value_Par"].values[0]
                    Typ = Map_Add_Par["Type"].values[0]

                    if "8-bit" in Typ:
                        Len = 1
                    elif "16-bit" in Typ:
                        Len = 2
                    elif "32-bit" in Typ:
                        Len = 4
                    elif "56-bit" in Typ:
                        Len = 8

                    if "float" in Typ:
                        Bytes_Content = float_to_hex(Val).zfill(Len * 2)
                    elif "bitfield" in Typ:
                        # At the moment not required, to be addressed later
                        Bytes_Content = "N/A"
                    elif "struct" in Typ:
                        # At the moment not required, to be addressed later
                        Bytes_Content = "N/A"
                    elif Typ == "vector byte":
                        lst_bytes = []
                        for byte in Val:
                            lst_bytes.append(dec2hex(byte).zfill(2))

                        Len = len(lst_bytes)
                        Bytes_Content = "".join(lst_bytes)

                    elif (
                        "unsigned char" in Typ
                        or "unsigned short" in Typ
                        or "unsigned integer" in Typ
                        or "undefined" in Typ
                    ):
                        if (
                            Map_Add_Par["Parameter Mnemonic"].values[0]
                            in lst_parameters
                        ):
                            Int_Val = int(round((Val[0] - 3.5) / 10.032))
                            Bytes_Content = dec2hex(Int_Val).zfill(Len * 2)
                        else:
                            # DUE TO MASKED ARRAYS VALUES
                            Bytes_Content = dec2hex(int(Val)).zfill(Len * 2)

                    Bytes += Bytes_Content
                    AddLen = Len

                    # Check the data unit is not longer than 255 bytes, if it is, it is split for the memory image file
                    Count_Max_Check = dict_config["count_max"] * 2

                    if AddLen > (Count_Max_Check):
                        logger.info(
                            "Note: The data dimension exceeds the maximum number of memory data unit per line in the memory image. The data has been split in the file"
                        )

                        Bytes_Dict_Flash_Par.append(
                            [
                                add,
                                dec2hex(Count_Max_Check),
                                Bytes[0 : Count_Max_Check * 2],
                            ]
                        )

                        line_slices = math.floor(AddLen / Count_Max_Check)
                        for slices in range(line_slices):
                            add_2 = dec2hex(
                                hex2dec(add) + Count_Max_Check * (slices + 1)
                            ).zfill(
                                len(add)
                            )  # TBD depending Flash address LME
                            if slices == line_slices - 1:
                                Number_Bytes = (AddLen) % Count_Max_Check
                                End_index = (AddLen) * 2
                            else:
                                Number_Bytes = Count_Max_Check
                                End_index = (
                                    (Count_Max_Check * (slices + 1)) * 2
                                ) + Count_Max_Check * 2
                            Start_index = (Count_Max_Check * (slices + 1)) * 2

                            if Number_Bytes != 0:
                                Bytes_Dict_Flash_Par.append(
                                    [
                                        add_2,
                                        dec2hex(Number_Bytes),
                                        Bytes[Start_index:End_index],
                                    ]
                                )

                    else:
                        Bytes_Dict_Flash_Par.append(
                            [add, dec2hex(AddLen), Bytes]
                        )

        # Combine the two sets from Data Pools and Parameters - for Flash only
        All_Adds = []
        Bytes_Dict_Flash_All = Bytes_Dict_Flash + Bytes_Dict_Flash_Par
        Bytes_Dict_Flash_All_Sorted_Unique = [["Start", "Count", "Data"]]

        for sets in Bytes_Dict_Flash_All:
            All_Adds.append(sets[0])

        sorted_addresses = sorted(set(All_Adds))

        for addr in sorted_addresses:
            single = []
            for sets in Bytes_Dict_Flash_All:
                if sets[0] == addr and single == []:
                    single = sets
                    Bytes_Dict_Flash_All_Sorted_Unique.append(sets)

    # STEP 3 (Patch ONLY) - Create Memory Images
    if PAForPatch == "Patch":
        # For filename and something else TBC
        if part_n == 0:
            part = ""
        else:
            part = f"PART{part_n}_"

        if Memory_Type == "Flash":
            device_id = "1".zfill(3)
            device = "LI_FLASH"
            dt_time = datetime.now().strftime("%Y%m%dT%H%M%S")

            # Preparation of data for the filename details
            str_filename = f"{str_sat[0:4]}{str_sat[6]}_{dict_config['file_class']}_OBS_MIMG_{device_id}_{device}_{dt_time}_{part}{str(icid).zfill(2)[-2:]}{str(icid_ver).zfill(2)[-2:]}.IMG"
            str_path_full = os.path.join(str_output_folder, str_filename)
            memory_image_file(
                Bytes_Dict_Flash_All_Sorted_Unique,
                "LME_CONF",
                "All",
                str_sat,
                device,
                part,
                activity_params,
                str_path_full,
                bln_merge,
            )
        elif Memory_Type == "RAM":
            device_id = "0".zfill(3)
            device = "LI_SDRAM"
            dt_time = datetime.now().strftime("%Y%m%dT%H%M%S")

            # Preparation of data for the filename details
            str_filename = f"{str_sat[0:4]}{str_sat[6]}_{dict_config['file_class']}_OBS_MIMG_{device_id}_{device}_{dt_time}_{part}{str(icid).zfill(2)[-2:]}{str(icid_ver).zfill(2)[-2:]}.IMG"
            str_path_full = os.path.join(str_output_folder, str_filename)
            memory_image_file(
                Bytes_Dict_RAM,
                "LME_CONF",
                "All",
                str_sat,
                device,
                part,
                activity_params,
                str_path_full,
                bln_merge,
            )        
        elif Memory_Type == "RAM and Flash":
            device_id = "1".zfill(3)
            device = "LI_FLASH"
            dt_time = datetime.now().strftime("%Y%m%dT%H%M%S")

            # Preparation of data for the filename details
            str_filename = f"{str_sat[0:4]}{str_sat[6]}_{dict_config['file_class']}_OBS_MIMG_{device_id}_{device}_{dt_time}_{part}{str(icid).zfill(2)[-2:]}{str(icid_ver).zfill(2)[-2:]}.IMG"
            str_path_full = os.path.join(str_output_folder, str_filename)
            memory_image_file(
                Bytes_Dict_Flash_All_Sorted_Unique,
                "LME_CONF",
                "All",
                str_sat,
                device,
                part,
                activity_params,
                str_path_full,
                bln_merge,
            )

            device_id = "0".zfill(3)
            device = "LI_SDRAM"
            str_filename = f"{str_sat[0:4]}{str_sat[6]}_{dict_config['file_class']}_OBS_MIMG_{device_id}_{device}_{dt_time}_{part}{str(icid).zfill(2)[-2:]}{str(icid_ver).zfill(2)[-2:]}.IMG"
            str_path_full = os.path.join(str_output_folder, str_filename)

            memory_image_file(
                Bytes_Dict_RAM,
                "LME_CONF",
                "All",
                str_sat,
                device,
                part,
                activity_params,
                str_path_full,
                bln_merge,
            )

    icidver = activity_params.icid_ver
    # STEP 4 (PAF ONLY) - Create PAFs
    if PAForPatch == "PAF":
        # Read from configuration the affected procedures
        df_paf_config = pd.read_csv(
            os.path.join(
                config.CONFIG_VALUES["config_folder"],
                str_instrument,
                "LI_PAF_Config.csv",
            ),
            index_col="Activity_Type",
            keep_default_na=False,
        )

        for Par_to_update in Datapool_parameters:
            dict_new = {}
            str_procedures = df_paf_config.loc[Par_to_update, "Procedure_Name"]

            if str_procedures[-3] == "_":
                str_proc_clean = str_procedures[0:-3]
                PAF_Name = f"PAF_{str_procedures[0:-3]}.xml"
                logger.info(
                    f"Started PAF File Update for {str_sat} {Par_to_update} Procedure {str_proc_clean}"
                )
            else:
                PAF_Name = f"PAF_{str_procedures}.xml"
                logger.info(
                    f"Started PAF File Update for {str_sat} {Par_to_update} Procedure {str_procedures}"
                )

            # Find only variables needed for the specific procedure
            df_VSM_proc = VSM_Tab[VSM_Tab["Activity"].str.contains(str_procedures)]
            df_VSM_proc.set_index("Name", inplace=True)

            # Create dictionary with PAF variable name to netCDF value correspondence
            # This new dictionary replaces the original one with netCDF variable name to netCDF value correspondence
            # Based on VSM file containing PAF variable name to netCDF variable name correspondence
            if str_procedures[-3] == "_":
                for variable in df_VSM_proc.index:
                    PAF_var_name = df_VSM_proc.loc[variable, "Mnemonic in PAF"]
                    PAF_var_Mnem_str = df_VSM_proc.loc[variable, "Mnemonic in Map"]

                    if "[" in PAF_var_Mnem_str:
                        ICID_Upd_Dict = {}
                        ICID_Upd_Dict["ICID"] = str(icid)
                        ICID_Upd_Dict["ICID_VER"] = str(icid_ver)
                        # '[' marks the variables with at least a different mnemonic per FEE
                        PAF_var_Mnem = PAF_var_Mnem_str.strip("][").split(";")

                        # Check if all FEE were selected
                        if FEE_PAF[0] == 0:
                            for fee in lst_fees:
                                PAF_var_Mnem_FEE = PAF_var_Mnem[fee - 1]
                                dict_new[PAF_var_name] = Map_All[
                                    Map_All["Parameter Logical Identifier"]
                                    == PAF_var_Mnem_FEE
                                ]["Value"].values[0]
                                dict_new["PARAMETER_ID"] = PAF_var_Mnem_FEE
                                ICID_Upd_Dict["FEE_ID"] = f"FEE_{fee}"
                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        PAF_Name,
                                    ),
                                    dict_new,
                                    str_output_folder,
                                    str_sat,
                                    f"{PAF_var_Mnem_FEE}-FEE{fee}",
                                    str_instrument,
                                    f"{icid}-Ver{icidver}{part[:-1]}",
                                    "",
                                    "",
                                    "",
                                )

                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        config.CONFIG_VALUES["li_icid_ram_paf"],
                                    ),
                                    ICID_Upd_Dict,
                                    str_output_folder,
                                    str_sat,
                                    f"FEE{fee - 1}",
                                    str_instrument,
                                    f"{icid}-Ver{icidver}",
                                    "",
                                    "",
                                    "",
                                )
                        else:
                            for fee in FEE_PAF:
                                PAF_var_Mnem_FEE = PAF_var_Mnem[fee - 1]
                                dict_new[PAF_var_name] = Map_All[
                                    Map_All["Parameter Logical Identifier"]
                                    == PAF_var_Mnem_FEE
                                ]["Value"].values[0]
                                dict_new["PARAMETER_ID"] = PAF_var_Mnem_FEE
                                ICID_Upd_Dict["FEE_ID"] = f"FEE_{fee}"

                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        PAF_Name,
                                    ),
                                    dict_new,
                                    str_output_folder,
                                    str_sat,
                                    f"{PAF_var_Mnem_FEE}-FEE{fee}",
                                    str_instrument,
                                    f"{icid}-Ver{icidver}{part[:-1]}",
                                    "",
                                    "",
                                    "",
                                )
                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        config.CONFIG_VALUES["li_icid_ram_paf"],
                                    ),
                                    ICID_Upd_Dict,
                                    str_output_folder,
                                    str_sat,
                                    f"FEE{fee - 1}",
                                    str_instrument,
                                    f"{icid}-Ver{icidver}",
                                    "",
                                    "",
                                    "",
                                )
                    else:
                        PAF_var_Mnem = PAF_var_Mnem_str.values[0]
                        dict_new["PARAMETER_ID"] = PAF_var_Mnem
                        ICID_Upd_Dict = {}
                        ICID_Upd_Dict["ICID"] = str(icid)
                        ICID_Upd_Dict["ICID_VER"] = str(icid_ver)
                        ICID_Upd_Dict["FEE_ID"] = "ALL_FEES"

                        if variable[-6:] == "vector":
                            Value_list = Map_All[
                                Map_All["Parameter Logical Identifier"] == PAF_var_Mnem
                            ]["Value"].values[0]

                            Bytes_Content = ""

                            for byte in Value_list:
                                Bytes_Content += dec2hex(byte).zfill(2)

                            dict_new[PAF_var_name] = Bytes_Content
                        else:
                            dict_new[PAF_var_name] = Map_All[
                                Map_All["Parameter Logical Identifier"] == PAF_var_Mnem
                            ]["Value"].values[0]

                        PAF_Update(
                            os.path.join(
                                config.CONFIG_VALUES["paf_folder"],
                                str_instrument,
                                PAF_Name,
                            ),
                            dict_new,
                            str_output_folder,
                            str_sat,
                            f"{PAF_var_Mnem}-FEEs",
                            str_instrument,
                            f"{icid}-Ver{icidver}",
                            "",
                            "",
                            "",
                        )
                        PAF_Update(
                            os.path.join(
                                config.CONFIG_VALUES["paf_folder"],
                                str_instrument,
                                config.CONFIG_VALUES["li_icid_ram_paf"],
                            ),
                            ICID_Upd_Dict,
                            str_output_folder,
                            str_sat,
                            "FEEs",
                            str_instrument,
                            f"{icid}-Ver{icidver}",
                            "",
                            "",
                            "",
                        )
            else:
                for variable in df_VSM_proc.index:
                    PAF_var_name = df_VSM_proc.loc[variable, "Mnemonic in PAF"]
                    PAF_var_Mnem_str = df_VSM_proc.loc[variable, "Mnemonic in Map"]

                    if "[" in PAF_var_Mnem_str:
                        # '[' marks the variables with at least a different mnemonic per FEE
                        PAF_var_Mnem = PAF_var_Mnem_str.strip("][").split(";")
                    else:
                        PAF_var_Mnem = PAF_var_Mnem_str

                    if (
                        variable == "mvf_en"
                        or variable == "dt_clustering_en"
                        or variable == "dt_isolated_filter_en"
                    ):
                        for fee in FEE_PAF:
                            ICID_Upd_Dict = {}
                            if variable == "mvf_en":
                                PAF_var_Mnem_FEE_All = (
                                    PAF_var_Mnem[fee - 1].strip(")(").split(",")
                                )
                                PAF_var_Mnem_FEE = PAF_var_Mnem_FEE_All[
                                    0
                                ]  # Name of first ASIC taken since common in procedure
                            else:
                                PAF_var_Mnem_FEE = PAF_var_Mnem[fee - 1]
                            dict_new[PAF_var_name] = Map_All[
                                Map_All["Parameter Logical Identifier"]
                                == PAF_var_Mnem_FEE
                            ]["Value"].values[0]
                            ICID_Upd_Dict["ICID"] = str(icid)
                            ICID_Upd_Dict["ICID_VER"] = str(icid_ver)

                            if fee == 0:
                                dict_new["FEE"] = "ALL_FEES"
                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        PAF_Name,
                                    ),
                                    dict_new,
                                    str_output_folder,
                                    str_sat,
                                    "FEEs",
                                    str_instrument,
                                    f"{icid}-Ver{icid_ver}",
                                    "",
                                    "",
                                    "",
                                    bln_test,
                                )
                                ICID_Upd_Dict["FEE_ID"] = "ALL_FEES"
                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        config.CONFIG_VALUES["li_icid_ram_paf"],
                                    ),
                                    ICID_Upd_Dict,
                                    str_output_folder,
                                    str_sat,
                                    "FEEs",
                                    str_instrument,
                                    f"{icid}-Ver{icid_ver}",
                                    "",
                                    "",
                                    "",
                                    bln_test,
                                )
                            else:
                                dict_new["FEE"] = f"FEE_{fee}"

                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        PAF_Name,
                                    ),
                                    dict_new,
                                    str_output_folder,
                                    str_sat,
                                    f"FEE{fee - 1}",
                                    str_instrument,
                                    f"{icid}-Ver{icid_ver}",
                                    "",
                                    "",
                                    "",
                                    bln_test,
                                )
                                ICID_Upd_Dict["FEE_ID"] = f"FEE_{fee}"
                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        config.CONFIG_VALUES["li_icid_ram_paf"],
                                    ),
                                    ICID_Upd_Dict,
                                    str_output_folder,
                                    str_sat,
                                    f"FEE{fee - 1}",
                                    str_instrument,
                                    f"{icid}-Ver{icid_ver}",
                                    "",
                                    "",
                                    "",
                                    bln_test,
                                )
                    elif (
                        variable == "loh_dr_en"
                        or variable == "lme_dr_enabled"
                        or variable == "season_conf_sel"
                    ):
                        dict_new[PAF_var_name] = Map_All[
                            Map_All["Parameter Logical Identifier"] == PAF_var_Mnem
                        ]["Value"].values[0]
                        PAF_Update(
                            os.path.join(
                                config.CONFIG_VALUES["paf_folder"],
                                str_instrument,
                                PAF_Name,
                            ),
                            dict_new,
                            str_output_folder,
                            str_sat,
                            "FEEs",
                            str_instrument,
                            f"{icid}-Ver{icid_ver}",
                            "",
                            "",
                            "",
                            bln_test,
                        )
                        ICID_Upd_Dict = {}
                        ICID_Upd_Dict["ICID"] = str(icid)
                        ICID_Upd_Dict["ICID_VER"] = str(icid_ver)
                        if variable == "season_conf_sel":
                            for LOC in lst_fees:
                                ICID_Upd_Dict["FEE_ID"] = f"FEE_{LOC}"

                                PAF_Update(
                                    os.path.join(
                                        config.CONFIG_VALUES["paf_folder"],
                                        str_instrument,
                                        config.CONFIG_VALUES["li_icid_ram_paf"],
                                    ),
                                    ICID_Upd_Dict,
                                    str_output_folder,
                                    str_sat,
                                    f"FEE{LOC-1}",
                                    str_instrument,
                                    f"{icid}-Ver{icid_ver}",
                                    "",
                                    "",
                                    "",
                                    bln_test,
                                )
                        else:
                            ICID_Upd_Dict["FEE_ID"] = "ALL_FEES"
                            PAF_Update(
                                os.path.join(
                                    config.CONFIG_VALUES["paf_folder"],
                                    str_instrument,
                                    config.CONFIG_VALUES["li_icid_ram_paf"],
                                ),
                                ICID_Upd_Dict,
                                str_output_folder,
                                str_sat,
                                "FEEs",
                                str_instrument,
                                f"{icid}-Ver{icid_ver}",
                                "",
                                "",
                                "",
                                bln_test,
                            )
                    else:
                        if "#" in PAF_var_name:
                            # '#' marks the variables with dimensions >1, which in the VSM are a single entry but multiple entries in the PAF
                            name_counter = 0
                            for specific in PAF_var_Mnem:
                                PAF_var_spec_name = PAF_var_name.replace(
                                    "#", str(name_counter + 1)
                                )
                                dict_new[PAF_var_spec_name] = Map_All[
                                    Map_All["Parameter Logical Identifier"] == specific
                                ]["Value"].values[0]
                                name_counter = name_counter + 1
                        else:
                            dict_new[PAF_var_name] = Map_All[
                                Map_All["Parameter Logical Identifier"] == PAF_var_Mnem
                            ]["Value"].values[0]
                            if (
                                PAF_var_name == "MICROVIB"
                            ):  # To be done because in VSM provided as int, in PAF it's enum string
                                if dict_new[PAF_var_name] == 1:
                                    dict_new[PAF_var_name] = "ENABLED"
                                elif dict_new[PAF_var_name] == 0:
                                    dict_new[PAF_var_name] = "DISABLED"

                        if Par_to_update == "Configure Background/MV Windows":
                            ICID_Upd_Dict = {}
                            dict_new["ADJUST_MV_WIN"] = "false"
                            for loh in lst_fees:
                                # Not used
                                dict_new[f"ICID_OC{loh}"] = ""
                                dict_new[f"ICID_VER_OC{loh}"] = ""

                            ICID_Upd_Dict["ICID"] = str(icid)
                            ICID_Upd_Dict["ICID_VER"] = str(icid_ver)
                            ICID_Upd_Dict["FEE_ID"] = "ALL_FEES"
                            PAF_Update(
                                os.path.join(
                                    config.CONFIG_VALUES["paf_folder"],
                                    str_instrument,
                                    config.CONFIG_VALUES["li_icid_ram_paf"],
                                ),
                                ICID_Upd_Dict,
                                str_output_folder,
                                str_sat,
                                "FEEs",
                                str_instrument,
                                f"{icid}-Ver{icid_ver}",
                                "",
                                "",
                                "",
                                bln_test,
                            )
                        # Hardcoded
                        if (
                            Par_to_update
                            == "Configure Background & Activate/Deactivate MV Windows"
                        ):
                            dict_new["ADJUST_MV_WIN"] = "true"

                            for int_fee in lst_fees:
                                dict_new[f"ICID_OC{int_fee}"] = icid
                                dict_new[f"ICID_VER_OC{int_fee}"] = icid_ver

                        PAF_Update(
                            os.path.join(
                                config.CONFIG_VALUES["paf_folder"],
                                str_instrument,
                                PAF_Name,
                            ),
                            dict_new,
                            str_output_folder,
                            str_sat,
                            "FEEs",
                            str_instrument,
                            f"{icid}-Ver{icid_ver}",
                            "",
                            "",
                            "",
                            bln_test,
                        )


def LI_MEMORY_MERGE(
    activity_params,
    lst_fees: list,
    output_folder: str,
    str_sat: str,
):
    """LI Basic/Oper Conf Memory Image Merging function
    Find and read the SDRAM .IMG files in the spacecraft specific folder (assumes all to be merged)
    """

    icid = activity_params.icid
    icidver = activity_params.icid_ver

    df_content = pd.DataFrame()
    lst_files = []
    data_dict = {}
    start_dict = []
    end_dict = []
    lenstot = 0
    data_dict_sort = {}
    AllData = ""
    Patch_Dict = {}
    CONF_Dict = {
        "Basic_Conf_Pix": "PIXEL",
        "Conf_Det_Thr": "DET_THR",
        "Delta_Thr": "DELTA_THR",
        "Clamp": "CLAMP",
        "Asic_Oper": "ASIC",
        "Asic_Calib": "ASIC",
        "Noise_Corr": "NOISE_CORR",
        "Offset_Lut": "OFFSET_LUT",
        "Filter_Thr": "FILTER_THR",
        "Oper_Conf_Init": "OPER_CONF",
        "Oper_Conf_Oper": "OPER_CONF",
    }

    # Search for the images files in the output folder
    for str_file in basics.files_in_dir(output_folder, ".IMG"):
        if "SDRAM" in str_file:
            with open(os.path.join(output_folder, str_file)) as obj_file:
                df_content[str_file] = obj_file.readlines()

            # Append the files to be use
            lst_files.append(str_file)
        else:
            continue

        # Dictionary with all data, merged
        for lines in df_content[str_file]:
            if lines.startswith("START="):
                address = hex2dec(
                    (re.search("START=(.*),COUNT", lines)).group(1)
                )

                if lines.endswith("\n"):
                    data_dict[address] = lines
                else:
                    data_dict[address] = lines + "\n"

            elif lines.startswith("LENGTH="):
                # Finds new LENGTH
                lenstot += int((re.search("LENGTH=(.*)\n", lines)).group(1))
            elif lines.startswith("STARTADDR="):
                # Finds new STARTADDR
                start_dict.append(
                    hex2dec((re.search("STARTADDR=0x(.*)\n", lines)).group(1))
                )
            elif lines.startswith("ENDADDR="):
                # Finds new ENDADDR
                end_dict.append(
                    hex2dec((re.search("ENDADDR=0x(.*)\n", lines)).group(1))
                )

        # Delete Files as they have been merge
        os.remove(os.path.join(output_folder, str_file))

    start_dict_tot = f"0x{dec2hex(min(start_dict))}"
    end_dict_tot = f"0x{dec2hex(max(end_dict))}"

    # Sorts merged data into one dictionary
    keys = sorted(data_dict)

    for key in keys:
        data_dict_sort[key] = data_dict[key]

        # DataHEX
        AllData += (re.search(",DATA=(.*)", data_dict[key])).group(1)

    data_dict_sort[keys[-1]] = data_dict_sort[keys[-1]][0:-1]

    # Calculates new Checksum
    str_checksum = basics.checksum(AllData, "fletcher16").zfill(4)

    TCTypes = (re.search(",TC_Type(.*)", df_content[lst_files[0]][4])).group(1)

    # Creates new Data
    Data = "".join(
        [
            df_content[lst_files[0]][0],
            df_content[lst_files[0]][1],
            df_content[lst_files[0]][2],
            df_content[lst_files[0]][3],
            f"DESCRIPTION=LI_SDRAM,for_LI_FEE_ID_{lst_fees},TC_Type{TCTypes}" + "\n",
            df_content[lst_files[0]][5],
            df_content[lst_files[0]][6],
            df_content[lst_files[0]][7],
            df_content[lst_files[0]][8],
            df_content[lst_files[0]][9],
            f"STARTADDR={start_dict_tot}" + "\n",
            f"ENDADDR={end_dict_tot}" + "\n",
            f"LENGTH={lenstot}" + "\n",
            f"CHECKSUM={str_checksum}" + "\n",
            df_content[lst_files[0]][14],
        ]
    )

    for key in keys:
        Data += data_dict_sort[key]

    # Final creation of the file
    filename = re.sub("PART(.*)_", "", lst_files[0])

    with open(os.path.join(output_folder, filename), "wb") as obj_memory_image:
        obj_memory_image.write(bytes(Data, "utf-8"))

    # Generate PAF associated to Memory Image
    # Generation of data for PAF - Case of BASIC CONF Single TC or OPER CONF
    Patch_Dict["FEE_ID"] = "ALL_FEES"
    Patch_Dict["IMG_FileName"] = filename
    Patch_Dict["ADDRESS"] = start_dict_tot[2:]

    if "," not in str(TCTypes) or "Oper_Conf" in str(TCTypes):
        if "Oper_Conf" in str(TCTypes):
            # This because if all FEEs are updated, this is done in WAIT, and assuming this is always only OPER CONF OPER, there is no need to send the configuration to the FEEs (so we avoid not necessary commanding)
            Patch_Dict["CONF_ID"] = "CONF_NOT_APPLIED"
            Patch_Dict["CKS16"] = ""
        else:
            Patch_Dict["CONF_ID"] = CONF_Dict[TCTypes[4:]]
            if "Asic" in str(TCTypes):
                Patch_Dict["CKS16"] = ""
            else:
                Patch_Dict["CKS16"] = str_checksum

    # Generation of data for PAF - Case of BASIC CONF Combined TCs
    else:
        Patch_Dict["CONF_ID"] = "ALL_BASIC_CONF"

        Patch_Dict["CKS16"] = ""

    Patch_Dict["LENGTH"] = (
        hex2dec(end_dict_tot) - hex2dec(start_dict_tot) + 1
    )

    # Call of PAF function update to generate the PAFs
    PAF_Update(
        os.path.join(
            config.CONFIG_VALUES["paf_folder"],
            str_instrument,
            config.CONFIG_VALUES["li_patch_ram_paf"],
        ),
        Patch_Dict,
        output_folder,
        str_sat,
        "AllFEEs",
        str_instrument,
        f"{icid}-Ver{icidver}",
        "",
        "",
        "",
    )
