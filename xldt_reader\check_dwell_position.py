#!/usr/bin/env python3
"""
Check dwell_position values in NetCDF file to see if they match expected pattern.
"""

import os
import sys
import numpy as np

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    import netCDF4 as nc
except ImportError:
    print("❌ netCDF4 not available")
    sys.exit(1)

def check_dwell_position():
    """Check dwell_position values for scan law ID 16384."""
    
    netcdf_file = "../assets/Input_Files/IRS/MTS1_IRS_SCANLAW_0000_v2.nc"
    
    if not os.path.exists(netcdf_file):
        print(f"❌ NetCDF file not found: {netcdf_file}")
        return
    
    print("🔍 Checking dwell_position Values")
    print("=" * 50)
    
    try:
        with nc.Dataset(netcdf_file, 'r') as dataset:
            # Get scan law IDs
            scan_law_ids = dataset.variables['scan_law_id'][:]
            print(f"Available scan law IDs: {scan_law_ids}")
            
            # Find index for scan law ID 16384
            target_id = 16384
            target_indices = np.where(scan_law_ids == target_id)[0]
            if len(target_indices) == 0:
                print(f"❌ Scan law ID {target_id} not found")
                return
            
            target_index = target_indices[0]
            print(f"Found scan law ID {target_id} at index {target_index}")
            
            # Check all variables
            print(f"\n📊 All Variables in NetCDF:")
            for var_name in dataset.variables:
                var = dataset.variables[var_name]
                print(f"   {var_name}: shape={var.shape}, dtype={var.dtype}")
            
            # Get dwell_position data
            if 'dwell_position_alpha' in dataset.variables:
                dwell_alpha = dataset.variables['dwell_position_alpha'][target_index, :]
                print(f"\n🔍 dwell_position_alpha for scan law {target_id}:")
                print(f"   Shape: {dwell_alpha.shape}")
                print(f"   First 50 values: {dwell_alpha[:50]}")
                
                # Convert to regular array if masked
                if hasattr(dwell_alpha, 'data'):
                    dwell_alpha = dwell_alpha.data
                
                # Look for filler values
                filler_positions = np.where(dwell_alpha == 255)[0]
                if len(filler_positions) > 0:
                    valid_length = filler_positions[0]
                    print(f"   Valid data length: {valid_length}")
                    valid_data = dwell_alpha[:valid_length]
                    print(f"   Valid data: {valid_data}")
                else:
                    print(f"   No filler values found")
                    valid_data = dwell_alpha
                
                # Check if this matches expected pattern
                expected_start = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]
                actual_start = valid_data[:10] if len(valid_data) >= 10 else valid_data
                print(f"\n   Expected pattern: {expected_start}")
                print(f"   Actual pattern:   {list(actual_start)}")
                
                if np.array_equal(actual_start, expected_start):
                    print(f"   ✅ MATCHES expected pattern!")
                else:
                    print(f"   ❌ Does not match expected pattern")
            
            if 'dwell_position_epsilon' in dataset.variables:
                dwell_epsilon = dataset.variables['dwell_position_epsilon'][target_index, :]
                print(f"\n🔍 dwell_position_epsilon for scan law {target_id}:")
                print(f"   Shape: {dwell_epsilon.shape}")
                print(f"   First 50 values: {dwell_epsilon[:50]}")
                
                # Convert to regular array if masked
                if hasattr(dwell_epsilon, 'data'):
                    dwell_epsilon = dwell_epsilon.data
                
                # Look for filler values
                filler_positions = np.where(dwell_epsilon == 255)[0]
                if len(filler_positions) > 0:
                    valid_length = filler_positions[0]
                    print(f"   Valid data length: {valid_length}")
                    valid_data = dwell_epsilon[:valid_length]
                    print(f"   Valid data: {valid_data}")
                else:
                    print(f"   No filler values found")
                    valid_data = dwell_epsilon
            
            # Also check FDA pointers for comparison
            if 'fda_mp_pointer_alpha' in dataset.variables:
                fda_alpha = dataset.variables['fda_mp_pointer_alpha'][target_index, :]
                print(f"\n🔍 fda_mp_pointer_alpha for scan law {target_id}:")
                print(f"   Shape: {fda_alpha.shape}")
                print(f"   First 20 values: {fda_alpha[:20]}")
                
                # Convert to regular array if masked
                if hasattr(fda_alpha, 'data'):
                    fda_alpha = fda_alpha.data
                
                # Look for filler values
                filler_positions = np.where(fda_alpha == 255)[0]
                if len(filler_positions) > 0:
                    valid_length = filler_positions[0]
                    print(f"   Valid data length: {valid_length}")
                    valid_data = fda_alpha[:valid_length]
                    print(f"   Valid data first 20: {valid_data[:20]}")
                
                # Check if this matches expected pattern
                expected_start = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]
                actual_start = valid_data[:10] if len(valid_data) >= 10 else valid_data
                print(f"   Expected pattern: {expected_start}")
                print(f"   Actual pattern:   {list(actual_start)}")
                
                if np.array_equal(actual_start, expected_start):
                    print(f"   ✅ MATCHES expected pattern!")
                else:
                    print(f"   ❌ Does not match expected pattern")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_dwell_position()
