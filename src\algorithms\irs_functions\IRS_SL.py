# Standard library imports
import pandas as pd
import numpy as np
from typing import Dict, Any
import os
import datetime
# Local imports
from src.utils.import_utils import config, basics
from src.logger_wrapper import logger
from src.utils.conversion_utils import dec2hex, float_to_hex, hex2dec
from src.utils.activity_params import ActivityParams
from src.utils.netcdf_utils import read_netCDF
from src.utils.excel_utils import Read_Sheet
from src.functions import create_XLDT


def is_filler_value(value, variable_name=""):
    """Check if a value is filler data (masked, very large numbers, or specific patterns).
    Also logs debug info for diagnosis.
    Args:
        value: The value to check
        variable_name: Name of the variable (for context-specific checks)
    Returns:
        bool: True if the value is considered filler data
    """
    # Check for masked values (NetCDF masked arrays)
    if hasattr(value, 'mask') and np.ma.is_masked(value):
        return True
    if np.ma.is_masked(value):
        return True
    try:
        if np.issubdtype(type(value), np.floating):
            if abs(float(value)) > 1e30:
                return True
        # Never treat integers as fillers
    except Exception as e:
        logger.debug(f"is_filler_value EXCEPTION: {e} for value={value}, variable={variable_name}")
        return True
    return False


def IRS_SL(act_params: ActivityParams, satellite: str) -> None:
    """Generate IRS Scan Law binary file."""
    # Initialize Variables
    Binary_Section_Hex = {}
    Section_Pos = {}
    Body_Size = 0

    # Load Configuration
    dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
    df_SL_config = pd.read_csv(
        os.path.join(
            config.CONFIG_VALUES["config_folder"], act_params.instrument, "IRS_SL_Conf.csv"
        ),
        keep_default_na=False,
        index_col=None,
    )
    df_SL_config.set_index("Section", inplace=True)

    # Identify the netCDF file in which the "slicer" should be used to identify the configuration
    main_files = used_files = ["SCANLAW"]
    
    # Get the dictionary of values from the netCDF files for the desired slicing parameter
    logger.info("Reading netCDF files for IRS Scan Law")

    # Read netCDF file using updated signature
    dict_netCDF = read_netCDF(
        act_params=act_params,
        satellite=satellite,
        main_files=main_files,
        used_files=used_files,
        instrument_conf=dict_config
    )

    # Log the entire content of dwell_position_alpha if it exists
    if "dwell_position_alpha" in dict_netCDF:
        arr = dict_netCDF["dwell_position_alpha"]
        logger.info(f"dwell_position_alpha: shape={arr.shape}, dtype={arr.dtype}, min={np.min(arr)}, max={np.max(arr)}")
        # Prepare a string with float32 values, 'FILLER' for >1e30
        def val_to_str(val):
            if np.issubdtype(type(val), np.floating) and abs(float(val)) > 1e30:
                return 'FILLER'
            return f"{np.float32(val):.7g}"
        arr_str = ' '.join(val_to_str(val) for val in arr)
        # Wrap lines at 120 characters
        import textwrap
        arr_str_wrapped = '\n'.join(textwrap.wrap(arr_str, width=120))
        logger.info(f"dwell_position_alpha float32 content (FILLER for >1e30):\n{arr_str_wrapped}")

    # For each Section, generate the HEX string
    lst_XLDT_body_hex = []

    # Read VSM file
    df_file_tab = Read_Sheet(dict_config["vsm_path"], "SCANLAW")

    # Find only variables needed for the specific Section
    for sections in df_SL_config.index:        
        logger.info(f"Processing section: {sections}")
        df_file_tab_proc = df_file_tab[
            df_file_tab["XLDT_Section"].str.contains(sections)
        ]

        Section_Pos[sections] = df_SL_config.loc[sections]["Order"]
        df_file_tab_proc.set_index("Name", inplace=True)

        # Generate the Section HEX string
        section_hex = IRS_SL_Section_Build(
            df_file_tab_proc,
            sections,
            df_SL_config,
            act_params.scan_law_id,
            act_params.mm_slot,
            dict_netCDF
        )
        
        # Only store and include section if it has data
        if section_hex:
            Binary_Section_Hex[Section_Pos[sections]] = section_hex
            # Calculate the size of the XLDT Body
            if sections != "XLDT_Header" and sections != "CRC":
                Body_Size += len(section_hex) / 2
            logger.info(f"Section {sections}: entries included = {len(section_hex) // 2} bytes")
        else:
            logger.info(f"Section {sections} omitted - no valid data")
            # Remove the section position to exclude it from final assembly
            del Section_Pos[sections]

    # Because Scan Law Size in Body Header not yet added and it is two bytes
    Body_Size += 2

    logger.info(f"IRS_SL: Body_Size (decimal): {Body_Size}")
    Body_Size_Hex = dec2hex(Body_Size).zfill(4)
    logger.info(f"IRS_SL: Body_Size_Hex: {Body_Size_Hex}")
    # Finish the XLDT Body HEX String
    Binary_Section_Hex[Section_Pos["Header"]] = (
        Binary_Section_Hex[Section_Pos["Header"]] + Body_Size_Hex
    )
    logger.info(f"IRS_SL: Header hex string: {Binary_Section_Hex[Section_Pos['Header']]}")
    # Add XLDT Body Length to XLDT Header
    XLDT_Size_Hex = dec2hex(hex2dec(Body_Size_Hex) + 2)  # Because XLDT Length in XLDT Header counts also the CRC (2 bytes)
    logger.info(f"IRS_SL: XLDT_Size_Hex: {XLDT_Size_Hex}")
    Binary_Section_Hex[Section_Pos["XLDT_Header"]] = Binary_Section_Hex[
        Section_Pos["XLDT_Header"]
    ] + XLDT_Size_Hex.zfill(8)
    logger.info(f"IRS_SL: XLDT_Header hex string: {Binary_Section_Hex[Section_Pos['XLDT_Header']]}")

    # Put together the different Sections of the XLDT Body in order in one Hex String
    for Part in range(1, df_SL_config["Order"].max() + 1):
        str_section = df_SL_config[df_SL_config["Order"] == Part].index[0]
        if str_section != "XLDT_Header" and str_section != "CRC" and Part in Binary_Section_Hex:
            lst_XLDT_body_hex.append(Binary_Section_Hex[Part])

    # Join the XLDT_Body_Hex string
    XLDT_Body_Hex = "".join(lst_XLDT_body_hex)

    # Calculate checksum (CRC) on XLDT Body Hex String
    str_CRC = basics.checksum(XLDT_Body_Hex, "CRC16").zfill(4)

    # Put everything together in one Hex String
    XLDT_Hex = "".join(
        [Binary_Section_Hex[Section_Pos["XLDT_Header"]], str_CRC, XLDT_Body_Hex]
    )

    # Create XLDT Binary file for Scan Law
    filename = f"MTS{satellite[-1]}_IRS_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_SL-MM{act_params.mm_slot}-SLID{str(act_params.scan_law_id).zfill(5)}.bin"

    file_name = os.path.join(config.CONFIG_VALUES["output_folder"], satellite, filename)
    create_XLDT(XLDT_Hex, file_name)


def IRS_SL_Section_Build(
    df_file_tab_proc: pd.DataFrame,
    str_section: str,
    df_SL_config: pd.DataFrame,
    int_slicer_1: int,
    int_MM_Slot: int,
    dict_netCDF: Dict[str, Any],
) -> str:
    """Build binary file content of SL for each section in hex format.
    
    Args:
        df_file_tab_proc: Processed file table for this section
        str_section: Section name ("XLDT_Header", "Header", "LAC_Pointer", etc.)
        df_SL_config: Scan Law configuration dataframe
        int_slicer_1: Scan Law ID
        int_MM_Slot: Mass Memory slot ID
        dict_netCDF: Dictionary containing netCDF file data
        
    Returns:
        String containing hexadecimal data for this section
    """
    logger.info(f"Writing the binary file content of SL for section {str_section}")

    # XLDT Header - without body length
    if str_section == "XLDT_Header":
        str_hex = f"0001{dec2hex(int_MM_Slot).zfill(4)}"

    elif str_section == "CRC":
        str_hex = ""

    # XLDT Body Header - without body length
    elif str_section == "Header":
        str_hex = "".join(
            [
                df_SL_config.loc["Header"]["MSDF_ID_Hex"].zfill(2),
                dec2hex(int(df_SL_config.loc["Header"]["Length"])).zfill(6),
                dec2hex(int_slicer_1).zfill(4),
            ]
        )

    # XLDT LAC Pointers - All as many as there are in one Hex string
    elif str_section == "LAC_Pointer":
        # Initialize Variables
        lst_hex = []

        int_LACs = len(dict_netCDF[df_file_tab_proc.index[0]])

        for int_lac in range(int_LACs):
            # Check if this LAC contains any non-filler data
            has_valid_data = False
            dict_LAC = {}

            for variable in df_file_tab_proc.index:
                LAC_var = dict_netCDF[variable][int_lac]
                LAC_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])
                
                if pd.api.types.is_integer_dtype(LAC_var.dtype):
                    dict_LAC[LAC_str_Pos] = dec2hex(LAC_var).zfill(4)
                elif pd.api.types.is_float_dtype(LAC_var.dtype):
                    if is_filler_value(LAC_var, variable):
                        dict_LAC[LAC_str_Pos] = float_to_hex(0.0).zfill(8)  # or your chosen default
                    else:
                        dict_LAC[LAC_str_Pos] = float_to_hex(LAC_var * 1000).zfill(8)
                    has_valid_data = True
            # Only include this LAC if it has valid data
            if has_valid_data:
                LAC_Header = dec2hex(
                    hex2dec(df_SL_config.loc["LAC_Pointer"]["MSDF_ID_Hex"])
                    + int_lac
                ).zfill(2)
                LAC_Length = dec2hex(
                    int(df_SL_config.loc["LAC_Pointer"]["Length"])
                ).zfill(6)

                lst_hex.append(LAC_Header + LAC_Length)

                for int_pos in range(
                    1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1
                ):
                    if int_pos in dict_LAC:
                        lst_hex.append(dict_LAC[int_pos])
        # Join hexadecimal string
        str_hex = "".join(lst_hex)
        
        # If no valid LAC entries found, return empty string to omit this section entirely
        if not str_hex:
            return ""
        logger.info(f"Section LAC_Pointer: entries included = {len(lst_hex) // 2} bytes")

    # XLDT Retraces - All as many as there are in one Hex string
    elif str_section == "Retrace":
        str_hex = ""

    # XLDT Rallies - All as many as there are in one Hex string
    elif str_section == "Rally":
        str_hex = ""

    # XLDT FDA - All as many as there are in one Hex string
    elif str_section == "FDA":
        # Initialize Variables
        lst_hex = []

        number_of_Pos = len(dict_netCDF[df_file_tab_proc.index[0]])
        FDA_Header = dec2hex(
            hex2dec(df_SL_config.loc["FDA"]["MSDF_ID_Hex"])
        ).zfill(2)
        FDA_Length_Max = int(df_SL_config.loc["FDA"]["Length"])

        # Process only entries with valid (non-filler) data
        valid_entries = []
        
        for Pos in range(number_of_Pos):
            FDA_str = {}
            has_valid_data = False

            for variable in df_file_tab_proc.index:
                FDA_var = dict_netCDF[variable][Pos]
                FDA_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])
                
                if pd.api.types.is_integer_dtype(FDA_var.dtype):
                    FDA_str[FDA_str_Pos] = dec2hex(FDA_var).zfill(4)
                    has_valid_data = True
                elif pd.api.types.is_float_dtype(FDA_var.dtype):
                    if is_filler_value(FDA_var, variable):
                        FDA_str[FDA_str_Pos] = float_to_hex(0.0).zfill(8)  # or your chosen default
                    else:
                        FDA_str[FDA_str_Pos] = float_to_hex(FDA_var * 1000).zfill(8)
                        has_valid_data = True
            # Only include this entry if it has valid data
            if has_valid_data:
                valid_entries.append(FDA_str)
        # Build hex string from valid entries only
        for FDA_str in valid_entries:
            for pos in range(1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1):
                if pos in FDA_str:
                    lst_hex.append(FDA_str[pos])

        FDA_Length_Dec = int(len("".join(lst_hex)) / 2)

        # If no valid data found, return empty string to omit this section entirely
        if FDA_Length_Dec == 0:
            return ""

        if FDA_Length_Dec > FDA_Length_Max:
            logger.error(
                "The size of the FDA Section of the Scan Law is larger than the maximum allowed. The generated binary should not be trusted."
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        FDA_Length = dec2hex(FDA_Length_Dec).zfill(6)

        # Find last non-filler entry
        last_valid_idx = -1
        num_entries = len(dict_netCDF[df_file_tab_proc.index[0]])
        for idx in range(num_entries):
            entry_has_valid = False
            for var in ["dwell_position_alpha", "dwell_position_epsilon", "fda_mp_pointer_alpha", "fda_mp_pointer_epsilon"]:
                val = dict_netCDF[var][idx]
                if not is_filler_value(val, var):
                    entry_has_valid = True
                    break
            if entry_has_valid:
                last_valid_idx = idx
        valid_lst_hex = lst_hex[:last_valid_idx+1]
        logger.info(f"Section FDA: valid entries included = {len(valid_lst_hex)}, total hex size = {len(''.join(valid_lst_hex)) // 2} bytes")
        lst_hex = valid_lst_hex

        # Join hexadecimal string
        str_hex = "".join([FDA_Header, FDA_Length] + lst_hex)
        logger.info(f"Section FDA: entries included = {len(lst_hex) // 2} bytes")

    # XLDT MPA - All as many as there are in one Hex string
    elif str_section == "MPA":
        # Initialize Variables
        lst_hex = []

        int_Prof = len(dict_netCDF[df_file_tab_proc.index[0]])
        MPA_Header = dec2hex(
            hex2dec(df_SL_config.loc["MPA"]["MSDF_ID_Hex"])
        ).zfill(2)
        MPA_Length_Max = int(df_SL_config.loc["MPA"]["Length"])

        # Process only entries with valid (non-filler) data
        valid_entries = []
        
        for Prof in range(int_Prof):
            MPA_str = {}
            has_valid_data = False

            for variable in df_file_tab_proc.index:
                MPA_var = dict_netCDF[variable][Prof]
                MPA_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])

                if pd.api.types.is_integer_dtype(MPA_var.dtype):
                    MPA_str[MPA_str_Pos] = dec2hex(MPA_var).zfill(8)
                    has_valid_data = True
                elif pd.api.types.is_float_dtype(MPA_var.dtype):
                    if is_filler_value(MPA_var, variable):
                        MPA_str[MPA_str_Pos] = float_to_hex(0.0).zfill(8)  # or your chosen default
                    else:
                        if "1" in variable:
                            MPA_str[MPA_str_Pos] = float_to_hex(
                                (MPA_var * pow(2, 25) / 360) / 1000
                            ).zfill(8)
                        else:
                            MPA_str[MPA_str_Pos] = float_to_hex(
                                MPA_var * 1000
                            ).zfill(8)
                        has_valid_data = True
            # Only include this entry if it has valid data
            if has_valid_data:
                valid_entries.append(MPA_str)
        # Build hex string from valid entries only
        for MPA_str in valid_entries:
            for pos in range(1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1):
                if pos in MPA_str:
                    lst_hex.append(MPA_str[pos])

        MPA_Length_Dec = int(len("".join(lst_hex)) / 2)

        # If no valid data found, return empty string to omit this section entirely
        if MPA_Length_Dec == 0:
            return ""

        if MPA_Length_Dec > MPA_Length_Max:
            logger.error(
                "The size of the MPA Section of the Scan Law is larger than the maximum allowed. The generated binary should not be trusted."
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        MPA_Length = dec2hex(MPA_Length_Dec).zfill(6)

        # Find last non-filler entry
        last_valid_idx = -1
        num_entries = len(dict_netCDF[df_file_tab_proc.index[0]])
        for idx in range(num_entries):
            entry_has_valid = False
            for var in ["mpa_profile_type", "mpa_profile_param1", "mpa_profile_param2", "mpa_profile_param3", "mpa_profile_param4"]:
                val = dict_netCDF[var][idx]
                if not is_filler_value(val, var):
                    entry_has_valid = True
                    break
            if entry_has_valid:
                last_valid_idx = idx
        valid_lst_hex = lst_hex[:last_valid_idx+1]
        logger.info(f"Section MPA: valid entries included = {len(valid_lst_hex)}, total hex size = {len(''.join(valid_lst_hex)) // 2} bytes")
        lst_hex = valid_lst_hex

        # Join Hexadecimal string
        str_hex = "".join([MPA_Header, MPA_Length] + lst_hex)
        logger.info(f"Section MPA: entries included = {len(lst_hex) // 2} bytes")

    return str_hex
