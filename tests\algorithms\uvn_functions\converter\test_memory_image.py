# Standard library imports
import os

import pytest
# Local imports
from src.algorithms.uvn_functions.converter.memory_image_converter import \
    MemoryImageConverter
from tests.helpers import (assert_img_files, assert_ncd_files, assert_obj,
                           load_input_files)
from tests.utils.test_uvn_utils import Helper


@pytest.fixture(scope="class")
def suspend_capture(pytestconfig):
    """Use to make user interactions in the test or order stops for modify configurations file, HW..."""

    class suspend_guard:
        def __init__(self):
            self.capmanager = pytestconfig.pluginmanager.getplugin("capturemanager")

        def __enter__(self):
            self.capmanager.suspend_global_capture(in_=True)

        def __exit__(self, _1, _2, _3):
            self.capmanager.resume_global_capture()

    yield suspend_guard()


@pytest.fixture()
def load_UVN_test_files(request):

    # Set global  variables for testing
    global satellite
    global instrument
    satellite = "MTG-S 1"
    instrument = "UVN"
    # Load input files
    load_input_files(instrument)

    file_name = "MTS1_UVN_PTD_ref.img"

    # Set the memory image input test file
    input_directory = os.path.join(Helper.get_test_directory(), "Input")

    image_memory_test_file = os.path.join(Helper.get_test_directory(), file_name)

    # Set the memory image output test file
    output_directory = os.path.join(Helper.get_test_directory(), "Output")
    output_memory_img_file = os.path.join(output_directory, file_name)

    # Delete output file if exists
    os.remove(output_memory_img_file) if os.path.isfile(
        output_memory_img_file
    ) else None

    # Attach the instance variable to the request
    request.cls.image_memory_test_file = image_memory_test_file
    request.cls.output_memory_img_file = output_memory_img_file

    yield
    # cleanup
    os.remove(output_memory_img_file) if os.path.isfile(
        output_memory_img_file
    ) else None


@pytest.mark.usefixtures("load_UVN_test_files")
class TestMemoryImgConverter:
    def test_memory_img_conversion(self):
        """Test aims to verify the  following methods of the class image memory conversion:
        ptd_dict_to_file_format(self,dict_ptd,output_file_path):None

        This test verifies that:
        1. An Memory image file is correctly converted to a dictionary
        2. The dictionary generated is correctly converted to an memory ptd format
        3. A ptd memory file format is correctly converted to a dictionary

        """

        # Execute the method  to generate a ptd dictionary format
        # memory_img_conv = MemoryImageConverter(software_id="ASW", memory_id="SDRAM",icu_id='ICU_A')
        memory_img_conv = MemoryImageConverter(memory_id="SDRAM")

        PATH_SCRIPT = Helper.get_test_directory()

        file_path_img_10_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_1_0_S4AARAM_ref.img"
        )
        file_path_img_11_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_1_0_S4AAEEPA_ref.img"
        )
        file_path_img_12_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_1_0_S4AAEEPB_ref.img"
        )
        file_path_img_13_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_1_0_S4ASRAM_ref.img"
        )
        file_path_img_14_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_1_0_S4ASEEPA_ref.img"
        )
        file_path_img_15_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_1_0_S4ASEEPB_ref.img"
        )
        file_path_img_20_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_2_0_S4BARAM_ref.img"
        )
        file_path_img_21_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_2_0_S4BAEEPA_ref.img"
        )
        file_path_img_22_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_2_0_S4BAEEPB_ref.img"
        )
        file_path_img_23_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_2_0_S4BSRAM_ref.img"
        )
        file_path_img_24_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_2_0_S4BSEEPA_ref.img"
        )
        file_path_img_25_ref = os.path.join(
            PATH_SCRIPT, "Input", "ptd_3_7_2_0_S4BSEEPB_ref.img"
        )

        file_path_img_10_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_1_0_S4AARAM_test.img"
        )
        file_path_img_11_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_1_0_S4AAEEPA_test.img"
        )
        file_path_img_12_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_1_0_S4AAEEPB_test.img"
        )
        file_path_img_13_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_1_0_S4ASRAM_test.img"
        )
        file_path_img_14_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_1_0_S4ASEEPA_test.img"
        )
        file_path_img_15_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_1_0_S4ASEEPB_test.img"
        )
        file_path_img_20_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_2_0_S4BARAM_test.img"
        )
        file_path_img_21_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_2_0_S4BAEEPA_test.img"
        )
        file_path_img_22_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_2_0_S4BAEEPB_test.img"
        )
        file_path_img_23_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_2_0_S4BSRAM_test.img"
        )
        file_path_img_24_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_2_0_S4BSEEPA_test.img"
        )
        file_path_img_25_tst = os.path.join(
            PATH_SCRIPT, "Output", "ptd_3_7_2_0_S4BSEEPB_test.img"
        )

        os.remove(file_path_img_10_tst) if os.path.isfile(
            file_path_img_10_tst
        ) else None

        # Memory image conversions
        memory_img_conv = MemoryImageConverter(memory_id="SDRAM")
        dict_ptd_img_input = memory_img_conv.convert_image_to_dict(
            filename_img=file_path_img_10_ref
        )
        memory_img_conv.convert_dict_to_image(
            dict_ptd=dict_ptd_img_input,
            filename_img=file_path_img_10_tst,
            icu_id="ICU_A",
            software_id="ASW",
        )

        dict_ptd_img_output = memory_img_conv.convert_image_to_dict(
            filename_img=file_path_img_10_tst
        )

        assert assert_obj(dict_ptd_img_output, dict_ptd_img_input)

        assert assert_img_files([file_path_img_10_ref], [file_path_img_10_tst])
