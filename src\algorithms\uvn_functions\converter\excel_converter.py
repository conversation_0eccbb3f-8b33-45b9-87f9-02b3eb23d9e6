"""This class hold the implementation to converter excel files to dictionary

    and vice versa, containing PTD tables.

"""
import pandas as pd

from .file_format_converter import FileFormatConverterInterface


class ExcelConverter(FileFormatConverterInterface):
    """
    This is a subclass of the FileFormatConverterInterface and override the methods
     file_to_dict(self,filename='',memory_id='SDRAM')-> dict and
     dict_to_file(self,dict_ptd,output_file_path)
    """

    def __init__(self):
        """
        Instantiate a NetcdfConverter object

        Parameters
        ----------

        """
        super().__init__()

    def convert_excel_to_dict(self, filename_xls=""):
        """Function to convert the content of an excel file into a dictionary containing PTD tables

        :param filename_xls: excel file path, defaults to ''
        :type filename_xls: str
        :return: dictionary containing the PTD tables read from the excel file
        :rtype: dictionary
        """

        excel_data = pd.ExcelFile(filename_xls, engine="openpyxl")
        dict_ptd = {}

        for k_ptd in self.ptd_layout:

            # Extract columns name and type from PTD layout info:
            dict_col = {}
            for var_str in self.ptd_layout[k_ptd]["def"].split(","):
                var_name = var_str.split(":")[0]
                var_type = var_str.split(":")[1].split("[")[0]
                dict_col[var_name] = var_type

            k_ptd_ = k_ptd
            if (
                k_ptd_ not in excel_data.sheet_names
            ):  # Workaround to allow loading of PTD excel files delivered by ADS in PFM SRN
                k_ptd_ = self.ptd_layout[k_ptd]["title"][0:31]
            df = excel_data.parse(k_ptd_)
            dict_ptd[k_ptd] = {}
            for k_col in df.columns:
                if dict_col[k_col] == "Float32":
                    dict_ptd[k_ptd][k_col] = [
                        float(self.var_format.format(var)) for var in df[k_col].tolist()
                    ]
                else:
                    dict_ptd[k_ptd][k_col] = df[k_col].tolist()

        return dict_ptd

    def convert_dict_to_excel(self, dict_ptd={}, filename_xls=""):
        """Function to convert the content of a dictionary containing PTD tables into an excel file

        :param dict_ptd: Dictionary containing PTD tables, defaults to {}
        :type dict: dict, optional
        :param filename_xls: Path of the excel file to be created, defaults to ''
        :type filename_xls: str, optional
        """
        writer = pd.ExcelWriter(filename_xls, engine="openpyxl")
        # for k_ptd in dict_ptd:
        lst_ptd = sorted([k_ptd for k_ptd in dict_ptd])
        for k_ptd in lst_ptd:
            df = pd.DataFrame.from_dict(dict_ptd[k_ptd])
            df.to_excel(writer, sheet_name=k_ptd, index=False)
        writer.close()
