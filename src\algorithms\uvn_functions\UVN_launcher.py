""" This class contains the implementation of the uvn functions.
"""
from typing import List
import os
from src.utils.import_utils import config, basics
from src.algorithms.uvn_functions.memory_image_generator import \
    MemoryImageGenerator
from src.utils.activity_params import ActivityParams


def uvn_functions_launcher(
    act_params_value: ActivityParams, lst_parameters_value: List
) -> None:
    """
    Launch appropriate UVN functions based on activity parameters.

    Parameters
    -------------
    :act_params_value : selected activity to generate
    :lst_parameters_value: selected values to generate the activity

    """

    for satellite in act_params_value.satellites:
        if "Generate PTD Memory image File" in act_params_value.activity:
            memory = MemoryImageGenerator(
                act_params_value, lst_parameters_value, satellite
            )
            memory.generate()

    # Handle output commit
    if not act_params_value.test_mode:
        _handle_output_commit(act_params_value.satellites)
        


def _handle_output_commit(satellites: List[str]) -> None:
    """Handle committing output files to gitlab."""
    bln_commit = basics.pop_up_message(
        "Output Outcome",
        "Execution Completed\nDo you wish to commit the generated output?",
        "askyesno",
    )
    
    if bln_commit:
        for satellite in satellites:
            # Create gitlab for the specific satellite
            str_gitlab_project = (
                config.CONFIG_VALUES[f"gitlab_uvn"]
                + satellite[-1]
            )

            # Get files to commit
            lst_files_to_commit = basics.files_in_dir(
                os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
            )

            # Add full paths
            lst_files_to_commit = [
                os.path.join(config.CONFIG_VALUES["output_folder"], satellite, f)
                for f in lst_files_to_commit
            ]

            # Commit files
            basics.commit_to_gitlab(str_gitlab_project, lst_files_to_commit)

        basics.pop_up_message(
            "The_End", 
            "Commit Performed Successfully", 
            "info"
        )
