# #########################################################################################################################################
## \file ../output/TCL/workplan/Memory1/ActivityTable_Central_Summer.tcl
# File: ../output/TCL/workplan/Memory1/ActivityTable_Central_Summer.tcl
# \b Prerequisit: \n
#
# Author: 
#    generated by IRS Workplan Editor
# History:
# 23.06.23 17:54:47 - first issue
#
# #########################################################################################################################################

package require Tk


# Activity_Table_Initialize_TC, for ActivityTable_Central_Summer
# TC Name: DSWC005S
# TC Description: Act Tab Init
# DSWH09SX: Activity_Tab_Selected
# DSWH09TX: Activity_Tab_Id
# DSWH09YX: Number_Of_LACs
#  authorise DSWC005S
logTCSend DSWC005S {DSWH09SX "ACTIVI_TA_1"} {DSWH09TX 21201} {DSWH09YX 4}

# Activity_Table_LAC_Initialize_TC  for LAC1
# TC Name: DSWC005T
# TC Description: ActTab LAC Init
# DSWH0A1X: Activity_Tab_Selected_1
# DSWH0A4X: Activity_Tab_Id_1
# DSWH0AJX: Number_Of_theLAC
# DSWH0AKX: LAC_Id
# DSWH0ALX: Dwell_Num
# DSWH0AMX: First_Dwell_Id
# DSWH0ANX: Num_Act
# DSWH0AOX: LAC_Activities: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
# authorise DSWC005T
logTCSend DSWC005T {DSWH0A1X "ACTIVI_TA_1"} {DSWH0A4X 21201} {DSWH0AJX 1}\
{DSWH0AKX 3} {DSWH0ALX 78} {DSWH0AMX 2} {DSWH0ANX 244} {DSWH0AOX 100}\
{DSWH0APX "MOVE_COM"} {DSWH0AQX 128} {DSWH0ARX 0}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 3000}\
{DSWH0APX "CHECK_COM"} {DSWH0AQX 128} {DSWH0ARX 3900}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 64} {DSWH0ARX 14900}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 25400}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 28588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 64} {DSWH0ARX 35900}\
{DSWH0APX "MOVE_COM"} {DSWH0AQX 0} {DSWH0ARX 46000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 46788}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 49400}\
{DSWH0APX "CHECK_COM"} {DSWH0AQX 0} {DSWH0ARX 49900}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 59488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 60500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 61688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 70588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 71000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 72188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 81088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 81500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 82688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 91588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 92000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 93188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 102088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 102500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 103688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 112588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 113000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 114188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 123088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 123500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 124688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 133588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 134000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 135188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 144088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 145700}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 146888}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 155788}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 156500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 157688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 166588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 167000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 168188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 177088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 177500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 178688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 187588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 188000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 189188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 198088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 198500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 199688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 208588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 209000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 210188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 219088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 219500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 220688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 229588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 230000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 231188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 240088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 240500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 241688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 250588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 251000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 252188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 261088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 261500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 262688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 271588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 273200}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 274388}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 283288}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 283900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 285088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 293988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 294400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 295588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 304488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 304900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 306088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 314988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 315400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 316588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 325488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 325900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 327088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 335988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 336400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 337588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 346488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 346900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 348088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 356988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 357400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 358588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 367488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 367900}\

# Activity_Table_LAC_Update_TC  for LAC1
# TC Name: DSWC005U
# TC Description: Act Tab Update
# DSWH0ASX: Activity_Tab_Selected_2
# DSWH0ATX: Activity_Tab_Id_3
# DSWH0AUX:  Number_Of_theLAC
# DSWH0AVX: Start_Index
# DSWH0AOX: LAC_Activities_1: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
#   authorise DSWC005U
logTCSend DSWC005U {DSWH0ASX "ACTIVI_TA_1"} {DSWH0ATX 21201} {DSWH0AUX 1} {DSWH0AVX 101} {DSWH0AOX 100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 369088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 377988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 378400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 379588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 388488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 388900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 390088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 398988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 399400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 400588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 409488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 409900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 411088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 419988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 421600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 422788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 431688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 432300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 433488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 442388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 442800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 443988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 452888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 453300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 454488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 463388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 463800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 464988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 473888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 474300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 475488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 484388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 484800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 485988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 494888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 495300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 496488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 505388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 505800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 506988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 515888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 516300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 517488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 526388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 526800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 527988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 536888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 537300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 538488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 547388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 547800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 548988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 557888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 558300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 559488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 568388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 568800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 569988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 578888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 579300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 580488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 589388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 591000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 592188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 601088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 601700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 602888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 611788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 612200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 613388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 622288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 622700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 623888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 632788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 633200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 634388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 643288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 643700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 644888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 653788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 654200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 655388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 664288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 664700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 665888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 674788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 675200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 676388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 685288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 685700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 686888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 695788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 696200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 697388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 706288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 706700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 707888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 716788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 717200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 718388}\
 
# Activity_Table_LAC_Update_TC  for LAC1
# TC Name: DSWC005U
# TC Description: Act Tab Update
# DSWH0ASX: Activity_Tab_Selected_2
# DSWH0ATX: Activity_Tab_Id_3
# DSWH0AUX:  Number_Of_theLAC
# DSWH0AVX: Start_Index
# DSWH0AOX: LAC_Activities_1: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
#   authorise DSWC005U
logTCSend DSWC005U {DSWH0ASX "ACTIVI_TA_1"} {DSWH0ATX 21201} {DSWH0AUX 1} {DSWH0AVX 201} {DSWH0AOX 44}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 727288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 727700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 728888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 737788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 738200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 739388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 748288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 748700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 749888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 758788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 759200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 760388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 769288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 112} {DSWH0ARX 770900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 772088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 780988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 781600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 782788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 791688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 792100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 793288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 802188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 802600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 803788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 812688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 813100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 814288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 823188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 823600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 824788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 833688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 834100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 835288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 844188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 844600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 845788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 854688}\
 {DSWH0APX "MOVE_COM"} {DSWH0AQX 64} {DSWH0ARX 854700}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 48} {DSWH0ARX 857700}\
 {DSWH0APX "CHECK_COM"} {DSWH0AQX 64} {DSWH0ARX 858600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 866688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 868500}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 48} {DSWH0ARX 879000}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 889500}\
 
# Activity_Table_LAC_Initialize_TC  for LAC2
# TC Name: DSWC005T
# TC Description: ActTab LAC Init
# DSWH0A1X: Activity_Tab_Selected_1
# DSWH0A4X: Activity_Tab_Id_1
# DSWH0AJX: Number_Of_theLAC
# DSWH0AKX: LAC_Id
# DSWH0ALX: Dwell_Num
# DSWH0AMX: First_Dwell_Id
# DSWH0ANX: Num_Act
# DSWH0AOX: LAC_Activities: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
# authorise DSWC005T
logTCSend DSWC005T {DSWH0A1X "ACTIVI_TA_1"} {DSWH0A4X 21201} {DSWH0AJX 2}\
{DSWH0AKX 4} {DSWH0ALX 77} {DSWH0AMX 79} {DSWH0ANX 242} {DSWH0AOX 100}\
{DSWH0APX "MOVE_COM"} {DSWH0AQX 128} {DSWH0ARX 0}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 3000}\
{DSWH0APX "CHECK_COM"} {DSWH0AQX 128} {DSWH0ARX 3900}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 64} {DSWH0ARX 14900}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 25400}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 64} {DSWH0ARX 35900}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 45288}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 46400}\
{DSWH0APX "MOVE_COM"} {DSWH0AQX 0} {DSWH0ARX 56500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 57288}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 59900}\
{DSWH0APX "CHECK_COM"} {DSWH0AQX 0} {DSWH0ARX 60400}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 69988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 71000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 72188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 81088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 81500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 82688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 91588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 92000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 93188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 102088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 102500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 103688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 112588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 113000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 114188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 123088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 123500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 124688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 133588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 134000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 135188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 144088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 144500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 145688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 154588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 155000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 156188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 165088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 165500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 166688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 175588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 112} {DSWH0ARX 177200}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 178388}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 187288}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 187900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 189088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 197988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 198400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 199588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 208488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 208900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 210088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 218988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 219400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 220588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 229488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 229900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 231088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 239988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 240400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 241588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 250488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 250900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 252088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 260988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 261400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 262588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 271488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 271900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 273088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 281988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 282400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 283588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 292488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 292900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 294088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 302988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 303400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 304588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 313488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 313900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 315088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 323988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 324400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 325588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 334488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 334900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 336088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 344988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 345400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 346588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 355488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 355900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 357088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 365988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 366400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 367588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 376488}\

# Activity_Table_LAC_Update_TC  for LAC2
# TC Name: DSWC005U
# TC Description: Act Tab Update
# DSWH0ASX: Activity_Tab_Selected_2
# DSWH0ATX: Activity_Tab_Id_3
# DSWH0AUX:  Number_Of_theLAC
# DSWH0AVX: Start_Index
# DSWH0AOX: LAC_Activities_1: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
#   authorise DSWC005U
logTCSend DSWC005U {DSWH0ASX "ACTIVI_TA_1"} {DSWH0ATX 21201} {DSWH0AUX 2} {DSWH0AVX 101} {DSWH0AOX 100}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 378100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 379288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 388188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 388800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 389988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 398888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 399300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 400488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 409388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 409800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 410988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 419888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 420300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 421488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 430388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 430800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 431988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 440888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 441300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 442488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 451388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 451800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 452988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 461888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 462300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 463488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 472388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 472800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 473988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 482888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 483300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 484488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 493388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 493800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 494988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 503888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 504300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 505488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 514388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 514800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 515988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 524888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 525300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 526488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 535388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 535800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 536988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 545888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 546300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 547488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 556388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 556800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 557988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 566888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 567300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 568488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 577388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 112} {DSWH0ARX 579000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 580188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 589088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 589900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 591088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 599988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 600400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 601588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 610488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 610900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 612088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 620988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 621400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 622588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 631488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 631900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 633088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 641988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 642400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 643588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 652488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 652900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 654088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 662988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 663400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 664588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 673488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 673900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 675088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 683988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 684400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 685588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 694488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 694900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 696088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 704988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 705400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 706588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 715488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 715900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 717088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 725988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 726400}\
 
# Activity_Table_LAC_Update_TC  for LAC2
# TC Name: DSWC005U
# TC Description: Act Tab Update
# DSWH0ASX: Activity_Tab_Selected_2
# DSWH0ATX: Activity_Tab_Id_3
# DSWH0AUX:  Number_Of_theLAC
# DSWH0AVX: Start_Index
# DSWH0AOX: LAC_Activities_1: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
#   authorise DSWC005U
logTCSend DSWC005U {DSWH0ASX "ACTIVI_TA_1"} {DSWH0ATX 21201} {DSWH0AUX 2} {DSWH0AVX 201} {DSWH0AOX 42}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 727588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 736488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 736900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 738088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 746988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 747400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 748588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 757488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 757900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 759088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 767988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 768400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 769588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 778488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 780100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 781288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 790188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 791000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 792188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 801088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 801500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 802688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 811588}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 812000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 813188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 822088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 822500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 823688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 832588}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 833000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 834188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 843088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 843500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 844688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 853588}\
 {DSWH0APX "MOVE_COM"} {DSWH0AQX 64} {DSWH0ARX 853600}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 48} {DSWH0ARX 856600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 857388}\
 {DSWH0APX "CHECK_COM"} {DSWH0AQX 64} {DSWH0ARX 857500}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 867500}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 48} {DSWH0ARX 878000}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 888500}\
 
# Activity_Table_LAC_Initialize_TC  for LAC3
# TC Name: DSWC005T
# TC Description: ActTab LAC Init
# DSWH0A1X: Activity_Tab_Selected_1
# DSWH0A4X: Activity_Tab_Id_1
# DSWH0AJX: Number_Of_theLAC
# DSWH0AKX: LAC_Id
# DSWH0ALX: Dwell_Num
# DSWH0AMX: First_Dwell_Id
# DSWH0ANX: Num_Act
# DSWH0AOX: LAC_Activities: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
# authorise DSWC005T
logTCSend DSWC005T {DSWH0A1X "ACTIVI_TA_1"} {DSWH0A4X 21201} {DSWH0AJX 3}\
{DSWH0AKX 5} {DSWH0ALX 77} {DSWH0AMX 155} {DSWH0ANX 242} {DSWH0AOX 100}\
{DSWH0APX "MOVE_COM"} {DSWH0AQX 128} {DSWH0ARX 0}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 3000}\
{DSWH0APX "CHECK_COM"} {DSWH0AQX 128} {DSWH0ARX 3900}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 64} {DSWH0ARX 14900}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 25400}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 64} {DSWH0ARX 35900}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 43388}\
{DSWH0APX "MOVE_COM"} {DSWH0AQX 0} {DSWH0ARX 46000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 46788}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 49400}\
{DSWH0APX "CHECK_COM"} {DSWH0AQX 0} {DSWH0ARX 49900}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 59488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 60500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 61688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 70588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 71000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 72188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 81088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 81500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 82688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 91588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 92000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 93188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 102088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 102500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 103688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 112588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 113000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 114188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 123088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 123500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 124688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 133588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 134000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 135188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 144088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 144500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 145688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 154588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 155000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 156188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 165088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 165500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 166688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 175588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 112} {DSWH0ARX 177200}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 178388}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 187288}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 187900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 189088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 197988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 198400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 199588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 208488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 208900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 210088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 218988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 219400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 220588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 229488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 229900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 231088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 239988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 240400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 241588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 250488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 250900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 252088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 260988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 261400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 262588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 271488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 271900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 273088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 281988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 282400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 283588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 292488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 292900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 294088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 302988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 303400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 304588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 313488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 313900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 315088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 323988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 324400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 325588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 334488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 334900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 336088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 344988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 345400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 346588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 355488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 355900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 357088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 365988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 366400}\

# Activity_Table_LAC_Update_TC  for LAC3
# TC Name: DSWC005U
# TC Description: Act Tab Update
# DSWH0ASX: Activity_Tab_Selected_2
# DSWH0ATX: Activity_Tab_Id_3
# DSWH0AUX:  Number_Of_theLAC
# DSWH0AVX: Start_Index
# DSWH0AOX: LAC_Activities_1: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
#   authorise DSWC005U
logTCSend DSWC005U {DSWH0ASX "ACTIVI_TA_1"} {DSWH0ATX 21201} {DSWH0AUX 3} {DSWH0AVX 101} {DSWH0AOX 100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 367588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 376488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 378100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 379288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 388188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 388800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 389988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 398888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 399300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 400488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 409388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 409800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 410988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 419888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 420300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 421488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 430388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 430800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 431988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 440888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 441300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 442488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 451388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 451800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 452988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 461888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 462300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 463488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 472388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 472800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 473988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 482888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 483300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 484488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 493388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 493800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 494988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 503888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 504300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 505488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 514388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 514800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 515988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 524888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 525300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 526488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 535388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 535800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 536988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 545888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 546300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 547488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 556388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 556800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 557988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 566888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 567300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 568488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 577388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 112} {DSWH0ARX 579000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 580188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 589088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 589900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 591088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 599988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 600400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 601588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 610488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 610900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 612088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 620988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 621400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 622588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 631488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 631900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 633088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 641988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 642400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 643588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 652488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 652900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 654088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 662988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 663400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 664588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 673488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 673900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 675088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 683988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 684400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 685588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 694488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 694900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 696088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 704988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 705400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 706588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 715488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 715900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 717088}\
 
# Activity_Table_LAC_Update_TC  for LAC3
# TC Name: DSWC005U
# TC Description: Act Tab Update
# DSWH0ASX: Activity_Tab_Selected_2
# DSWH0ATX: Activity_Tab_Id_3
# DSWH0AUX:  Number_Of_theLAC
# DSWH0AVX: Start_Index
# DSWH0AOX: LAC_Activities_1: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
#   authorise DSWC005U
logTCSend DSWC005U {DSWH0ASX "ACTIVI_TA_1"} {DSWH0ATX 21201} {DSWH0AUX 3} {DSWH0AVX 201} {DSWH0AOX 42}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 725988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 726400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 727588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 736488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 736900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 738088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 746988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 747400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 748588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 757488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 757900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 759088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 767988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 768400}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 769588}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 778488}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 780100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 781288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 790188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 791000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 792188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 801088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 801500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 802688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 811588}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 812000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 813188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 822088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 822500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 823688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 832588}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 833000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 834188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 843088}\
 {DSWH0APX "MOVE_COM"} {DSWH0AQX 64} {DSWH0ARX 843100}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 846100}\
 {DSWH0APX "CHECK_COM"} {DSWH0AQX 64} {DSWH0ARX 847000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 855088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 48} {DSWH0ARX 857000}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 867500}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 48} {DSWH0ARX 878000}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 888500}\
 
# Activity_Table_LAC_Initialize_TC  for LAC4
# TC Name: DSWC005T
# TC Description: ActTab LAC Init
# DSWH0A1X: Activity_Tab_Selected_1
# DSWH0A4X: Activity_Tab_Id_1
# DSWH0AJX: Number_Of_theLAC
# DSWH0AKX: LAC_Id
# DSWH0ALX: Dwell_Num
# DSWH0AMX: First_Dwell_Id
# DSWH0ANX: Num_Act
# DSWH0AOX: LAC_Activities: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
# authorise DSWC005T
logTCSend DSWC005T {DSWH0A1X "ACTIVI_TA_1"} {DSWH0A4X 21201} {DSWH0AJX 4}\
{DSWH0AKX 6} {DSWH0ALX 80} {DSWH0AMX 231} {DSWH0ANX 248} {DSWH0AOX 100}\
{DSWH0APX "MOVE_COM"} {DSWH0AQX 128} {DSWH0ARX 0}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 3000}\
{DSWH0APX "CHECK_COM"} {DSWH0AQX 128} {DSWH0ARX 3900}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 64} {DSWH0ARX 14900}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 24088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 80} {DSWH0ARX 25400}\
{DSWH0APX "MOVE_COM"} {DSWH0AQX 0} {DSWH0ARX 35500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 36288}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 38900}\
{DSWH0APX "CHECK_COM"} {DSWH0AQX 0} {DSWH0ARX 39400}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 48988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 50000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 51188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 60088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 60500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 61688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 70588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 71000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 72188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 81088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 81500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 82688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 91588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 92000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 93188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 102088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 102500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 103688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 112588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 113000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 114188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 123088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 123500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 124688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 133588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 134000}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 135188}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 144088}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 144500}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 145688}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 154588}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 112} {DSWH0ARX 156200}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 157388}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 166288}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 166900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 168088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 176988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 177400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 178588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 187488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 187900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 189088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 197988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 198400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 199588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 208488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 208900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 210088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 218988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 219400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 220588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 229488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 229900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 231088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 239988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 240400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 241588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 250488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 250900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 252088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 260988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 261400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 262588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 271488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 271900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 273088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 281988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 282400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 283588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 292488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 292900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 294088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 302988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 303400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 304588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 313488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 313900}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 315088}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 323988}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 324400}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 325588}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 334488}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 336100}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 337288}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 346188}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 346800}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 347988}\
{DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 356888}\
{DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 357300}\
{DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 358488}\

# Activity_Table_LAC_Update_TC  for LAC4
# TC Name: DSWC005U
# TC Description: Act Tab Update
# DSWH0ASX: Activity_Tab_Selected_2
# DSWH0ATX: Activity_Tab_Id_3
# DSWH0AUX:  Number_Of_theLAC
# DSWH0AVX: Start_Index
# DSWH0AOX: LAC_Activities_1: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
#   authorise DSWC005U
logTCSend DSWC005U {DSWH0ASX "ACTIVI_TA_1"} {DSWH0ATX 21201} {DSWH0AUX 4} {DSWH0AVX 101} {DSWH0AOX 100}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 367388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 367800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 368988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 377888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 378300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 379488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 388388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 388800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 389988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 398888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 399300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 400488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 409388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 409800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 410988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 419888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 420300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 421488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 430388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 430800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 431988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 440888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 441300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 442488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 451388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 451800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 452988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 461888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 462300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 463488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 472388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 472800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 473988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 482888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 483300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 484488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 493388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 493800}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 494988}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 503888}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 505500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 506688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 515588}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 516200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 517388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 526288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 526700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 527888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 536788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 537200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 538388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 547288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 547700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 548888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 557788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 558200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 559388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 568288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 568700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 569888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 578788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 579200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 580388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 589288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 589700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 590888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 599788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 600200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 601388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 610288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 610700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 611888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 620788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 621200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 622388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 631288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 631700}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 632888}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 641788}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 642200}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 643388}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 652288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 653900}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 655088}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 663988}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 664600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 665788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 674688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 675100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 676288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 685188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 685600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 686788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 695688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 696100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 697288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 706188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 706600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 707788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 716688}\
 
# Activity_Table_LAC_Update_TC  for LAC4
# TC Name: DSWC005U
# TC Description: Act Tab Update
# DSWH0ASX: Activity_Tab_Selected_2
# DSWH0ATX: Activity_Tab_Id_3
# DSWH0AUX:  Number_Of_theLAC
# DSWH0AVX: Start_Index
# DSWH0AOX: LAC_Activities_1: Group with Repeater
# DSWH0APX: Act_Type
# DSWH0AQX: Act_Param
# DSWH0ARX: Toffset
#   authorise DSWC005U
logTCSend DSWC005U {DSWH0ASX "ACTIVI_TA_1"} {DSWH0ATX 21201} {DSWH0AUX 4} {DSWH0AVX 201} {DSWH0AOX 48}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 717100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 718288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 727188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 727600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 728788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 737688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 738100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 739288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 748188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 748600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 749788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 758688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 759100}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 760288}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 769188}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 769600}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 770788}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 779688}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 96} {DSWH0ARX 781300}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 782488}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 791388}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 792000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 793188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 802088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 802500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 803688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 812588}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 813000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 814188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 823088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 823500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 824688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 833588}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 834000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 835188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 844088}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 0} {DSWH0ARX 844500}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 845688}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 854588}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 16} {DSWH0ARX 855000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 856188}\
 {DSWH0APX "MOVE_SCA"} {DSWH0AQX 0} {DSWH0ARX 865088}\
 {DSWH0APX "MOVE_COM"} {DSWH0AQX 64} {DSWH0ARX 865100}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 868100}\
 {DSWH0APX "CHECK_COM"} {DSWH0AQX 64} {DSWH0ARX 869000}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 48} {DSWH0ARX 879000}\
 {DSWH0APX "CHECK_SCA"} {DSWH0AQX 0} {DSWH0ARX 882288}\
 {DSWH0APX "MOVE_CCM"} {DSWH0AQX 32} {DSWH0ARX 889500}\
 

# Activity_Table_Validate_TC, for ActivityTable_Central_Summer
# TC Name: DSWC005V
# TC Description: ActTab Validate
#  DSWH0AWX: ActivityTab_6
#  DSWH0AXX: ActivityTab_7
#  DSWH0AYX: Checksum_3
# authorise DSWC005V
logTCSend DSWC005V {DSWH0AWX "ACTIVI_TA_1"} {DSWH0AXX 21201} {DSWH0AYX 39208}


