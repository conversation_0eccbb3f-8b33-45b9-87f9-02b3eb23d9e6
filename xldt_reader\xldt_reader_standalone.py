"""
Standalone XLDT File Reader
===========================

This is a standalone version of the XLDT reader that doesn't depend on 
the complex import structure of the main codebase.

XLDT files are binary files with the following structure:
- XLDT Header: Format identifier and MM slot information
- CRC: Checksum for data validation
- Body: Various data sections (Header, LAC_Pointer, Retrace, Rally, FDA, MPA, etc.)
"""

import os
import struct
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
from dataclasses import dataclass
import logging

# Set up basic logging
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)





def hex2dec(hex_str: str) -> int:
    """Convert hex string to decimal integer."""
    return int(hex_str, 16)


def calculate_crc16(data: str) -> str:
    """
    Calculate CRC16 checksum for hex string data.
    This is a simplified implementation.
    """
    total = sum(int(data[i:i+2], 16) for i in range(0, len(data), 2))
    return f"{total & 0xFFFF:04X}"


@dataclass
class FieldDefinition:
    """Definition of a field within an XLDT section"""
    name: str
    data_type: str
    size_bytes: int
    description: str = ""
    long_name: str = ""
    scaling_factor: float = 1.0
    is_filler_check: bool = True


@dataclass
class StructuredParameter:
    """Structured parameter with metadata like CDC system"""
    name: str
    value: Any
    data_type: str
    long_name: str = ""
    dimensions: str = ""
    shape: str = ""
    description: str = ""


@dataclass
class XLDTHeader:
    """XLDT Header structure"""
    format_id: int
    mm_slot: int
    body_length: Optional[int] = None


@dataclass
class XLDTSection:
    """XLDT Section structure"""
    section_name: str
    msdf_id: Optional[int]
    length: int
    order: int
    data: bytes


def parse_binary_field(hex_data: str, field_def: FieldDefinition, offset: int) -> Tuple[Any, int]:
    """
    Parse a single field from binary hex data.

    Args:
        hex_data: Hex string data
        field_def: Field definition
        offset: Current offset in hex string (in characters, not bytes)

    Returns:
        Tuple of (parsed_value, new_offset)
    """
    # Extract the hex bytes for this field
    field_hex = hex_data[offset:offset + field_def.size_bytes * 2]

    if len(field_hex) < field_def.size_bytes * 2:
        raise ValueError(f"Not enough data for field {field_def.name}")

    # Convert hex to bytes
    field_bytes = bytes.fromhex(field_hex)

    # Parse based on data type
    if field_def.data_type == "float32":
        # IEEE 754 float32, big-endian
        value = struct.unpack('>f', field_bytes)[0]
        # Apply scaling factor if specified
        if field_def.scaling_factor != 1.0:
            value = value / field_def.scaling_factor
    elif field_def.data_type == "ushort":
        # 2-byte unsigned short, big-endian
        value = struct.unpack('>H', field_bytes)[0]
    elif field_def.data_type == "ubyte":
        # 1-byte unsigned byte
        value = struct.unpack('B', field_bytes)[0]
    elif field_def.data_type == "profile_type":
        # Profile type - treat as 4-byte unsigned int
        value = struct.unpack('>I', field_bytes)[0]
    else:
        # Default: treat as raw hex
        value = field_hex

    new_offset = offset + field_def.size_bytes * 2
    return value, new_offset


def is_filler_value(value: Any, data_type: str) -> bool:
    """
    Check if a value is a filler/placeholder value.

    Args:
        value: The parsed value
        data_type: The data type of the field

    Returns:
        True if the value appears to be filler data
    """
    if data_type == "float32":
        # Check for very large values that might be filler
        if isinstance(value, (int, float)):
            # Common filler patterns: very large numbers, NaN, infinity
            if abs(value) > 1e10 or value != value or abs(value) == float('inf'):
                return True
            # Check for specific filler patterns (all 0xFF bytes = very large float)
            if abs(value) > 3.4e38:  # Close to float32 max
                return True
    elif data_type in ["ushort", "ubyte"]:
        # Check for max values (all bits set)
        if data_type == "ushort" and value == 0xFFFF:
            return True
        if data_type == "ubyte" and value == 0xFF:
            return True
    elif data_type == "profile_type":
        # Check for invalid profile type values
        if value == 0xFFFFFFFF or value == 0:
            return True

    return False


def parse_section_fields(hex_data: str, section_name: str) -> List[StructuredParameter]:
    """
    Parse a section's hex data into structured parameters.

    Args:
        hex_data: Hex string data for the section
        section_name: Name of the section (FDA, LAC_Pointer, MPA)

    Returns:
        List of StructuredParameter objects
    """
    if section_name not in XLDT_FIELD_MAPPINGS:
        logger.warning(f"No field mapping found for section: {section_name}")
        return []

    field_defs = XLDT_FIELD_MAPPINGS[section_name]
    parameters = []

    # Calculate entry size (sum of all field sizes)
    entry_size_bytes = sum(field.size_bytes for field in field_defs)
    entry_size_hex = entry_size_bytes * 2

    # Calculate number of entries
    total_hex_length = len(hex_data)
    num_entries = total_hex_length // entry_size_hex

    logger.info(f"Parsing {section_name}: {num_entries} entries, {entry_size_bytes} bytes per entry")

    # Parse each entry
    for entry_idx in range(num_entries):
        entry_offset = entry_idx * entry_size_hex
        field_offset = entry_offset

        # Check if this entry has any valid (non-filler) data
        entry_has_valid_data = False
        entry_fields = []

        # Parse all fields in this entry
        for field_def in field_defs:
            try:
                value, field_offset = parse_binary_field(hex_data, field_def, field_offset)

                # Check if this is a filler value
                is_filler = field_def.is_filler_check and is_filler_value(value, field_def.data_type)
                if not is_filler:
                    entry_has_valid_data = True

                # Create structured parameter
                param = StructuredParameter(
                    name=f"{field_def.name}[{entry_idx}]",
                    value=value,
                    data_type=field_def.data_type,
                    long_name=field_def.long_name,
                    dimensions=f"({section_name.lower()}_size)",
                    shape=f"({num_entries})",
                    description=field_def.description
                )
                entry_fields.append(param)

            except Exception as e:
                logger.error(f"Error parsing field {field_def.name} in entry {entry_idx}: {e}")
                continue

        # Only include entries with valid data
        if entry_has_valid_data:
            parameters.extend(entry_fields)
        else:
            # Stop at first all-filler entry (like the original code does)
            logger.info(f"Stopping at entry {entry_idx} - all filler values")
            break

    return parameters


class XLDTReader:
    """
    XLDT File Reader class for parsing binary XLDT files.

    This class can read XLDT files created by the satellite operations system
    and parse them into structured data.
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize XLDT Reader.

        Args:
            config_path: Path to configuration CSV file (e.g., IRS_SL_Conf.csv)
        """
        self.config_path = config_path
        self.config_df: Optional[pd.DataFrame] = None
        self.sections_config: Dict[str, Dict] = {}

        if config_path and os.path.exists(config_path):
            self._load_config()

    def _load_config(self) -> None:
        """Load section configuration from CSV file."""
        try:
            self.config_df = pd.read_csv(self.config_path)
            self.config_df.set_index("Section", inplace=True)

            # Convert to dictionary for easier access
            for section in self.config_df.index:
                self.sections_config[section] = {
                    'length': self.config_df.loc[section]['Length'],
                    'msdf_id_hex': self.config_df.loc[section]['MSDF_ID_Hex'],
                    'order': self.config_df.loc[section]['Order']
                }

            logger.info(f"Loaded XLDT configuration from {self.config_path}")

        except Exception as e:
            logger.error(f"Failed to load XLDT configuration: {e}")
            raise

    def read_xldt_file(self, file_path: str) -> Dict[str, Any]:
        """
        Read and parse an XLDT binary file.

        Args:
            file_path: Path to the XLDT binary file

        Returns:
            Dictionary containing parsed XLDT data
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XLDT file not found: {file_path}")

        logger.info(f"Reading XLDT file: {file_path}")

        try:
            with open(file_path, 'rb') as file:
                binary_data = file.read()

            # Convert binary data to hex string for processing
            hex_data = binary_data.hex().upper()

            logger.info(f"Read {len(binary_data)} bytes from XLDT file")

            # Parse the XLDT structure
            parsed_data = self._parse_xldt_data(hex_data)

            # Add file metadata
            parsed_data['file_info'] = {
                'file_path': file_path,
                'file_size': len(binary_data),
                'hex_length': len(hex_data)
            }

            return parsed_data

        except Exception as e:
            logger.error(f"Error reading XLDT file {file_path}: {e}")
            raise

    def _parse_xldt_data(self, hex_data: str) -> Dict[str, Any]:
        """
        Parse hex data into XLDT structure.

        Args:
            hex_data: Hexadecimal string representation of XLDT data

        Returns:
            Dictionary containing parsed sections
        """
        result = {
            'header': None,
            'crc': None,
            'sections': {},
            'raw_hex': hex_data
        }

        offset = 0

        # Parse XLDT Header (first 8 hex characters = 4 bytes)
        if len(hex_data) >= 8:
            header_hex = hex_data[0:8]
            result['header'] = self._parse_xldt_header(header_hex)
            offset = 8
            logger.info(f"Parsed XLDT header: {result['header']}")

        # If we have configuration, parse according to structure
        if self.sections_config:
            result['sections'] = self._parse_sections_with_config(hex_data, offset)
        else:
            # Generic parsing without configuration
            result['sections'] = self._parse_sections_generic(hex_data, offset)

        return result

    def _parse_xldt_header(self, header_hex: str) -> XLDTHeader:
        """
        Parse XLDT header from hex string.

        Args:
            header_hex: 8-character hex string representing the header

        Returns:
            XLDTHeader object
        """
        # XLDT Header structure: 0001 + MM_Slot (4 hex chars)
        format_id = hex2dec(header_hex[0:4])
        mm_slot = hex2dec(header_hex[4:8])

        return XLDTHeader(
            format_id=format_id,
            mm_slot=mm_slot
        )

    def _parse_sections_with_config(self, hex_data: str, offset: int) -> Dict[str, XLDTSection]:
        """
        Parse sections using configuration file.

        Args:
            hex_data: Complete hex data string
            offset: Current offset in hex string

        Returns:
            Dictionary of parsed sections
        """
        sections = {}
        current_offset = offset

        # Sort sections by order
        sorted_sections = sorted(
            self.sections_config.items(),
            key=lambda x: x[1]['order']
        )

        for section_name, config in sorted_sections:
            if section_name in ['XLDT_Header']:
                continue  # Already parsed

            if section_name == 'CRC':
                # CRC is typically 4 hex characters (2 bytes)
                if current_offset + 4 <= len(hex_data):
                    crc_hex = hex_data[current_offset:current_offset + 4]
                    sections[section_name] = XLDTSection(
                        section_name=section_name,
                        msdf_id=None,
                        length=2,
                        order=config['order'],
                        data=bytes.fromhex(crc_hex)
                    )
                    current_offset += 4
                continue

            # Parse regular sections
            section_length = config.get('length', 0)
            if isinstance(section_length, str) and section_length.strip() == '':
                continue

            section_length = int(section_length) if section_length else 0
            hex_length = section_length * 2  # Each byte = 2 hex characters

            if current_offset + hex_length <= len(hex_data):
                section_hex = hex_data[current_offset:current_offset + hex_length]
                sections[section_name] = XLDTSection(
                    section_name=section_name,
                    msdf_id=config.get('msdf_id_hex'),
                    length=section_length,
                    order=config['order'],
                    data=bytes.fromhex(section_hex)
                )
                current_offset += hex_length

                logger.info(f"Parsed section {section_name}: {section_length} bytes")

        return sections

    def get_structured_parameters(self, parsed_data: Dict[str, Any]) -> Dict[str, List[StructuredParameter]]:
        """
        Extract structured parameters from parsed XLDT data.

        Args:
            parsed_data: Parsed XLDT data from read_xldt_file()

        Returns:
            Dictionary mapping section names to lists of StructuredParameter objects
        """
        structured_params = {}

        if 'sections' not in parsed_data:
            logger.warning("No sections found in parsed data")
            return structured_params

        for section_name, section_data in parsed_data['sections'].items():
            if isinstance(section_data, XLDTSection):
                # Convert section data to hex string
                section_hex = section_data.data.hex().upper()

                # Parse structured fields for supported sections
                if section_name in XLDT_FIELD_MAPPINGS:
                    try:
                        parameters = parse_section_fields(section_hex, section_name)
                        if parameters:
                            structured_params[section_name] = parameters
                            logger.info(f"Extracted {len(parameters)} structured parameters from {section_name}")
                        else:
                            logger.info(f"No valid parameters found in {section_name}")
                    except Exception as e:
                        logger.error(f"Error parsing structured fields for {section_name}: {e}")
                else:
                    logger.info(f"No field mapping available for section: {section_name}")

        return structured_params

    def _parse_sections_generic(self, hex_data: str, offset: int) -> Dict[str, Any]:
        """
        Generic parsing when no configuration is available.

        Args:
            hex_data: Complete hex data string
            offset: Current offset in hex string

        Returns:
            Dictionary with generic parsing results
        """
        remaining_data = hex_data[offset:]

        return {
            'remaining_hex': remaining_data,
            'remaining_bytes': len(remaining_data) // 2,
            'note': 'Generic parsing - no configuration file provided'
        }


def format_structured_parameters_cdc_style(structured_params: Dict[str, List[StructuredParameter]]) -> str:
    """
    Format structured parameters in CDC system style.

    Args:
        structured_params: Dictionary of structured parameters by section

    Returns:
        Formatted string in CDC style
    """
    output_lines = []
    output_lines.append("XLDT Structured Parameters (CDC Style)")
    output_lines.append("=" * 50)
    output_lines.append("")

    for section_name, parameters in structured_params.items():
        if not parameters:
            continue

        output_lines.append(f"Section: {section_name}")
        output_lines.append("-" * 30)

        # Group parameters by base name (without array index)
        param_groups = {}
        for param in parameters:
            # Extract base name (remove array index)
            base_name = param.name.split('[')[0]
            if base_name not in param_groups:
                param_groups[base_name] = []
            param_groups[base_name].append(param)

        # Display each parameter group
        for base_name, group_params in param_groups.items():
            if len(group_params) == 1:
                param = group_params[0]
                output_lines.append(f"Name: {param.name}")
                output_lines.append(f"Long name: {param.long_name}")
                output_lines.append(f"Dimensions: {param.dimensions}")
                output_lines.append(f"Shape: {param.shape}")
                output_lines.append(f"Description: {param.description}")
                output_lines.append(f"Value: {param.value}")
                output_lines.append("")
            else:
                # Multiple values - show as array
                param = group_params[0]  # Use first for metadata
                values = [p.value for p in group_params]
                output_lines.append(f"Name: {base_name}")
                output_lines.append(f"Long name: {param.long_name}")
                output_lines.append(f"Dimensions: {param.dimensions}")
                output_lines.append(f"Shape: {param.shape}")
                output_lines.append(f"Description: {param.description}")
                output_lines.append(f"Values: {values}")
                output_lines.append("")

        output_lines.append("")

    return "\n".join(output_lines)


def export_structured_parameters_to_dict(structured_params: Dict[str, List[StructuredParameter]]) -> Dict[str, Any]:
    """
    Export structured parameters to dictionary format for JSON export.

    Args:
        structured_params: Dictionary of structured parameters by section

    Returns:
        Dictionary suitable for JSON export
    """
    export_data = {}

    for section_name, parameters in structured_params.items():
        if not parameters:
            continue

        section_data = {}

        # Group parameters by base name
        param_groups = {}
        for param in parameters:
            base_name = param.name.split('[')[0]
            if base_name not in param_groups:
                param_groups[base_name] = []
            param_groups[base_name].append(param)

        # Export each parameter group
        for base_name, group_params in param_groups.items():
            param_info = {
                'long_name': group_params[0].long_name,
                'description': group_params[0].description,
                'data_type': group_params[0].data_type,
                'dimensions': group_params[0].dimensions,
                'shape': group_params[0].shape,
            }

            if len(group_params) == 1:
                param_info['value'] = group_params[0].value
            else:
                # Convert to float32 arrays for numeric types
                values = []
                for param in group_params:
                    if param.data_type == "float32":
                        values.append(float(param.value))
                    else:
                        values.append(param.value)
                param_info['values'] = values

            section_data[base_name] = param_info

        export_data[section_name] = section_data

    return export_data


def main():
    """Main function for testing the XLDT reader."""
    import sys

    if len(sys.argv) < 2:
        print("Usage: python xldt_reader_standalone.py <xldt_file> [config_file]")
        return

    xldt_file = sys.argv[1]
    config_file = sys.argv[2] if len(sys.argv) > 2 else None

    try:
        # Initialize reader
        reader = XLDTReader(config_file)

        # Read and parse XLDT file
        parsed_data = reader.read_xldt_file(xldt_file)

        print(f"Successfully parsed XLDT file: {xldt_file}")
        print(f"Header: {parsed_data['header']}")
        print(f"Sections found: {list(parsed_data['sections'].keys())}")

        # Get structured parameters
        structured_params = reader.get_structured_parameters(parsed_data)

        if structured_params:
            print("\nStructured Parameters:")
            formatted_output = format_structured_parameters_cdc_style(structured_params)
            print(formatted_output)
        else:
            print("\nNo structured parameters found.")

    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
    """Structured parameter with metadata like CDC system"""
    name: str
    value: Any
    data_type: str
    long_name: str = ""
    dimensions: str = ""
    shape: str = ""
    description: str = ""


@dataclass
class XLDTHeader:
    """XLDT Header structure"""
    format_id: int
    mm_slot: int
    body_length: Optional[int] = None


@dataclass
class XLDTSection:
    """XLDT Section structure"""
    section_name: str
    msdf_id: Optional[int]
    length: int
    order: int
    data: bytes


# Field mapping configuration based on VSM analysis
# Note: dwell_position_alpha/epsilon are NOT in XLDT sections according to VSM
XLDT_FIELD_MAPPINGS = {
    "FDA": [
        FieldDefinition(
            name="fda_mp_pointer_alpha",
            data_type="ubyte",
            size_bytes=1,
            description="Alpha motion profile array pointer",
            long_name="FDA Motion Profile Pointer Alpha",
            is_filler_check=True
        ),
        FieldDefinition(
            name="fda_mp_pointer_epsilon",
            data_type="ubyte",
            size_bytes=1,
            description="Epsilon motion profile array pointer",
            long_name="FDA Motion Profile Pointer Epsilon",
            is_filler_check=True
        )
    ],
    "LAC_Pointer": [
        FieldDefinition(
            name="lac_start",
            data_type="ushort",
            size_bytes=2,
            description="LAC start index",
            long_name="LAC Start"
        ),
        FieldDefinition(
            name="lac_end",
            data_type="ushort",
            size_bytes=2,
            description="LAC end index",
            long_name="LAC End"
        ),
        FieldDefinition(
            name="lac_start_delta_time_alpha",
            data_type="float32",
            size_bytes=4,
            description="LAC start delta time alpha",
            long_name="LAC Start Delta Time Alpha",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="lac_start_delta_time_epsilon",
            data_type="float32",
            size_bytes=4,
            description="LAC start delta time epsilon",
            long_name="LAC Start Delta Time Epsilon",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="lac_end_delta_time_alpha",
            data_type="float32",
            size_bytes=4,
            description="LAC end delta time alpha",
            long_name="LAC End Delta Time Alpha",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="lac_end_delta_time_epsilon",
            data_type="float32",
            size_bytes=4,
            description="LAC end delta time epsilon",
            long_name="LAC End Delta Time Epsilon",
            scaling_factor=1000.0
        )
    ],
    "MPA": [
        FieldDefinition(
            name="mpa_profile_type",
            data_type="profile_type",
            size_bytes=4,
            description="Motion profile type identifier",
            long_name="MPA Profile Type"
        ),
        FieldDefinition(
            name="mpa_profile_param1",
            data_type="float32",
            size_bytes=4,
            description="Motion profile parameter 1",
            long_name="MPA Profile Parameter 1",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="mpa_profile_param2",
            data_type="float32",
            size_bytes=4,
            description="Motion profile parameter 2",
            long_name="MPA Profile Parameter 2",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="mpa_profile_param3",
            data_type="float32",
            size_bytes=4,
            description="Motion profile parameter 3",
            long_name="MPA Profile Parameter 3",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="mpa_profile_param4",
            data_type="float32",
            size_bytes=4,
            description="Motion profile parameter 4",
            long_name="MPA Profile Parameter 4",
            scaling_factor=1000.0
        )
    ]
}


class XLDTReader:
    """
    XLDT File Reader class for parsing binary XLDT files.
    
    This class can read XLDT files created by the satellite operations system
    and parse them into structured data.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize XLDT Reader.
        
        Args:
            config_path: Path to configuration CSV file (e.g., IRS_SL_Conf.csv)
        """
        self.config_path = config_path
        self.config_df: Optional[pd.DataFrame] = None
        self.sections_config: Dict[str, Dict] = {}
        
        if config_path and os.path.exists(config_path):
            self._load_config()
    
    def _load_config(self) -> None:
        """Load section configuration from CSV file."""
        try:
            self.config_df = pd.read_csv(self.config_path)
            self.config_df.set_index("Section", inplace=True)
            
            # Convert to dictionary for easier access
            for section in self.config_df.index:
                self.sections_config[section] = {
                    'length': self.config_df.loc[section]['Length'],
                    'msdf_id_hex': self.config_df.loc[section]['MSDF_ID_Hex'],
                    'order': self.config_df.loc[section]['Order']
                }
            
            logger.info(f"Loaded XLDT configuration from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load XLDT configuration: {e}")
            raise
    
    def read_xldt_file(self, file_path: str) -> Dict[str, Any]:
        """
        Read and parse an XLDT binary file.
        
        Args:
            file_path: Path to the XLDT binary file
            
        Returns:
            Dictionary containing parsed XLDT data
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XLDT file not found: {file_path}")
        
        logger.info(f"Reading XLDT file: {file_path}")
        
        try:
            with open(file_path, 'rb') as file:
                binary_data = file.read()
            
            # Convert binary data to hex string for processing
            hex_data = binary_data.hex().upper()
            
            logger.info(f"Read {len(binary_data)} bytes from XLDT file")
            
            # Parse the XLDT structure
            parsed_data = self._parse_xldt_data(hex_data)
            
            # Add file metadata
            parsed_data['file_info'] = {
                'file_path': file_path,
                'file_size': len(binary_data),
                'hex_length': len(hex_data)
            }
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"Error reading XLDT file {file_path}: {e}")
            raise
    
    def _parse_xldt_data(self, hex_data: str) -> Dict[str, Any]:
        """
        Parse hex data into XLDT structure.
        
        Args:
            hex_data: Hexadecimal string representation of XLDT data
            
        Returns:
            Dictionary containing parsed sections
        """
        result = {
            'header': None,
            'crc': None,
            'sections': {},
            'raw_hex': hex_data
        }
        
        offset = 0
        
        # Parse XLDT Header (first 8 hex characters = 4 bytes)
        if len(hex_data) >= 8:
            header_hex = hex_data[0:8]
            result['header'] = self._parse_xldt_header(header_hex)
            offset = 8
            logger.info(f"Parsed XLDT header: {result['header']}")
        
        # If we have configuration, parse according to structure
        if self.sections_config:
            result['sections'] = self._parse_sections_with_config(hex_data, offset)
        else:
            # Generic parsing without configuration
            result['sections'] = self._parse_sections_generic(hex_data, offset)
        
        return result
    
    def _parse_xldt_header(self, header_hex: str) -> XLDTHeader:
        """
        Parse XLDT header from hex string.
        
        Args:
            header_hex: 8-character hex string representing the header
            
        Returns:
            XLDTHeader object
        """
        # XLDT Header structure: 0001 + MM_Slot (4 hex chars)
        format_id = hex2dec(header_hex[0:4])
        mm_slot = hex2dec(header_hex[4:8])
        
        return XLDTHeader(
            format_id=format_id,
            mm_slot=mm_slot
        )
    
    def _parse_sections_with_config(self, hex_data: str, offset: int) -> Dict[str, XLDTSection]:
        """
        Parse sections using configuration file.
        
        Args:
            hex_data: Complete hex data string
            offset: Current offset in hex string
            
        Returns:
            Dictionary of parsed sections
        """
        sections = {}
        current_offset = offset
        
        # Sort sections by order
        sorted_sections = sorted(
            self.sections_config.items(),
            key=lambda x: x[1]['order']
        )
        
        for section_name, config in sorted_sections:
            if section_name in ['XLDT_Header']:
                continue  # Already parsed
                
            if section_name == 'CRC':
                # CRC is typically 4 hex characters (2 bytes)
                if current_offset + 4 <= len(hex_data):
                    crc_hex = hex_data[current_offset:current_offset + 4]
                    sections[section_name] = XLDTSection(
                        section_name=section_name,
                        msdf_id=None,
                        length=2,
                        order=config['order'],
                        data=bytes.fromhex(crc_hex)
                    )
                    current_offset += 4
                continue
            
            # Parse regular sections
            section_length = config.get('length', 0)
            if isinstance(section_length, str) and section_length.strip() == '':
                continue
                
            section_length = int(section_length) if section_length else 0
            hex_length = section_length * 2  # Each byte = 2 hex characters
            
            if current_offset + hex_length <= len(hex_data):
                section_hex = hex_data[current_offset:current_offset + hex_length]
                sections[section_name] = XLDTSection(
                    section_name=section_name,
                    msdf_id=config.get('msdf_id_hex'),
                    length=section_length,
                    order=config['order'],
                    data=bytes.fromhex(section_hex)
                )
                current_offset += hex_length
                
                logger.info(f"Parsed section {section_name}: {section_length} bytes")
        
        return sections

    def get_structured_parameters(self, parsed_data: Dict[str, Any]) -> Dict[str, List[StructuredParameter]]:
        """
        Extract structured parameters from parsed XLDT data.

        Args:
            parsed_data: Parsed XLDT data from read_xldt_file()

        Returns:
            Dictionary mapping section names to lists of StructuredParameter objects
        """
        structured_params = {}

        if 'sections' not in parsed_data:
            logger.warning("No sections found in parsed data")
            return structured_params

        for section_name, section_data in parsed_data['sections'].items():
            if isinstance(section_data, XLDTSection):
                # Convert section data to hex string
                section_hex = section_data.data.hex().upper()

                # Parse structured fields for supported sections
                if section_name in XLDT_FIELD_MAPPINGS:
                    try:
                        parameters = parse_section_fields(section_hex, section_name)
                        if parameters:
                            structured_params[section_name] = parameters
                            logger.info(f"Extracted {len(parameters)} structured parameters from {section_name}")
                        else:
                            logger.info(f"No valid parameters found in {section_name}")
                    except Exception as e:
                        logger.error(f"Error parsing structured fields for {section_name}: {e}")
                else:
                    logger.info(f"No field mapping available for section: {section_name}")

        return structured_params

    def _parse_sections_generic(self, hex_data: str, offset: int) -> Dict[str, Any]:
        """
        Generic parsing when no configuration is available.
        
        Args:
            hex_data: Complete hex data string
            offset: Current offset in hex string
            
        Returns:
            Dictionary with generic parsing results
        """
        remaining_data = hex_data[offset:]
        
        return {
            'remaining_hex': remaining_data,
            'remaining_bytes': len(remaining_data) // 2,
            'note': 'Generic parsing - no configuration file provided'
        }
    
    def validate_crc(self, hex_data: str, crc_section: XLDTSection) -> bool:
        """
        Validate CRC checksum.
        
        Args:
            hex_data: Complete hex data string
            crc_section: CRC section data
            
        Returns:
            True if CRC is valid, False otherwise
        """
        try:
            # Extract body data (everything except header and CRC)
            body_start = 8  # After XLDT header
            crc_start = body_start + 4  # Assuming CRC comes after header
            body_data = hex_data[crc_start:]
            
            # Calculate CRC using simplified method
            calculated_crc = calculate_crc16(body_data)
            provided_crc = crc_section.data.hex().upper()
            
            is_valid = calculated_crc == provided_crc
            logger.info(f"CRC validation: calculated={calculated_crc}, provided={provided_crc}, valid={is_valid}")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"CRC validation failed: {e}")
            return False
    
    def export_to_dict(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Export parsed data to a more readable dictionary format with float32 arrays.

        Args:
            parsed_data: Parsed XLDT data

        Returns:
            Dictionary with readable format including float32 arrays
        """
        import struct

        result = {
            'file_info': parsed_data.get('file_info', {}),
            'header': {},
            'sections': {}
        }

        # Convert header
        if parsed_data['header']:
            header = parsed_data['header']
            result['header'] = {
                'format_id': float(header.format_id),
                'mm_slot': float(header.mm_slot),
                'body_length': float(header.body_length) if header.body_length is not None else None
            }

        # Convert sections
        for section_name, section in parsed_data['sections'].items():
            if isinstance(section, XLDTSection):
                section_data = {
                    'msdf_id': float(section.msdf_id) if section.msdf_id is not None else None,
                    'length': float(section.length),
                    'order': float(section.order) if section.order is not None else None,
                    'data_hex': section.data.hex().upper(),
                    'data_bytes': float(len(section.data))
                }

                # Special handling for FDA section - convert to float32 array
                if section_name.upper() == 'FDA' and len(section.data) >= 4:
                    try:
                        # Convert hex data to float32 values
                        float_values = []
                        for i in range(0, len(section.data), 4):
                            if i + 4 <= len(section.data):
                                # Extract 4 bytes and convert to float32
                                bytes_chunk = section.data[i:i+4]
                                try:
                                    # Try little-endian first (most common)
                                    float_val = struct.unpack('<f', bytes_chunk)[0]
                                    # Check if the value is reasonable (not NaN or infinite)
                                    if not (float_val != float_val or abs(float_val) == float('inf')):
                                        float_values.append(float(float_val))
                                    else:
                                        # Try big-endian
                                        float_val = struct.unpack('>f', bytes_chunk)[0]
                                        float_values.append(float(float_val) if not (float_val != float_val or abs(float_val) == float('inf')) else 0.0)
                                except:
                                    # If both fail, try interpreting as integer
                                    try:
                                        int_val = struct.unpack('<I', bytes_chunk)[0]
                                        float_values.append(float(int_val))
                                    except:
                                        float_values.append(0.0)

                        section_data['data_float32_array'] = float_values
                        section_data['array_length'] = float(len(float_values))
                        section_data['data_format'] = 'float32_little_endian_preferred'

                    except Exception as e:
                        section_data['data_conversion_error'] = str(e)

                # For other sections, provide additional interpretations
                elif len(section.data) >= 2:
                    try:
                        # For smaller sections, try different interpretations
                        if len(section.data) % 4 == 0 and len(section.data) >= 4:
                            # Try as 32-bit values
                            uint32_values = []
                            int32_values = []
                            for i in range(0, len(section.data), 4):
                                bytes_chunk = section.data[i:i+4]
                                try:
                                    uint_val = struct.unpack('<I', bytes_chunk)[0]
                                    int_val = struct.unpack('<i', bytes_chunk)[0]
                                    uint32_values.append(float(uint_val))
                                    int32_values.append(float(int_val))
                                except:
                                    uint32_values.append(0.0)
                                    int32_values.append(0.0)

                            section_data['data_uint32_array'] = uint32_values
                            section_data['data_int32_array'] = int32_values

                        if len(section.data) % 2 == 0:
                            # Try as 16-bit values
                            uint16_values = []
                            for i in range(0, len(section.data), 2):
                                bytes_chunk = section.data[i:i+2]
                                try:
                                    uint_val = struct.unpack('<H', bytes_chunk)[0]
                                    uint16_values.append(float(uint_val))
                                except:
                                    uint16_values.append(0.0)

                            section_data['data_uint16_array'] = uint16_values

                    except Exception as e:
                        section_data['data_interpretation_note'] = f"Additional interpretation failed: {str(e)}"

                result['sections'][section_name] = section_data
            else:
                result['sections'][section_name] = section

        return result


def create_xldt_file(hex_data: str, file_path: str) -> None:
    """
    Create an XLDT file from hex data.
    
    Args:
        hex_data: Hex string containing file data
        file_path: Path where to create the XLDT file
    """
    try:
        binary_data = bytes.fromhex(hex_data)
        
        # Ensure directory exists
        dir_path = os.path.dirname(file_path)
        if dir_path:  # Only create directory if there is one
            os.makedirs(dir_path, exist_ok=True)
        
        with open(file_path, 'wb') as f:
            f.write(binary_data)
        
        logger.info(f"Created XLDT file: {file_path} ({len(binary_data)} bytes)")
        
    except Exception as e:
        logger.error(f"Error creating XLDT file: {e}")
        raise


def format_structured_parameters_cdc_style(structured_params: Dict[str, List[StructuredParameter]]) -> str:
    """
    Format structured parameters in CDC system style.

    Args:
        structured_params: Dictionary of structured parameters by section

    Returns:
        Formatted string similar to CDC system display
    """
    output = []
    output.append("XLDT Structured Parameters (CDC Format)")
    output.append("=" * 60)

    for section_name, parameters in structured_params.items():
        output.append(f"\n📊 {section_name} Section")
        output.append("-" * 40)

        if not parameters:
            output.append("   No parameters found")
            continue

        # Group parameters by base name (remove array indices)
        param_groups = {}
        for param in parameters:
            base_name = param.name.split('[')[0]  # Remove [index] part
            if base_name not in param_groups:
                param_groups[base_name] = []
            param_groups[base_name].append(param)

        # Display each parameter group in CDC style
        for base_name, param_list in param_groups.items():
            first_param = param_list[0]

            output.append(f"\n📋 {base_name}")
            output.append(f"   Name: {base_name}")
            output.append(f"   Long name: {first_param.long_name}")
            output.append(f"   Type: {first_param.data_type}")

            # Calculate dimensions and shape
            if len(param_list) > 1:
                dimensions = f"[{len(param_list)}]"
                shape = f"({len(param_list)},)"
            else:
                dimensions = "scalar"
                shape = "()"

            output.append(f"   Dimensions: {dimensions}")
            output.append(f"   Shape: {shape}")
            output.append(f"   Description: {first_param.description}")

            # Show values in CDC format
            if len(param_list) <= 10:
                values_str = ", ".join([str(p.value) for p in param_list])
                output.append(f"   Values: [{values_str}]")
            else:
                first_values = [str(p.value) for p in param_list[:5]]
                last_values = [str(p.value) for p in param_list[-2:]]
                output.append(f"   Values: [{', '.join(first_values)}, ..., {', '.join(last_values)}]")
                output.append(f"   Total entries: {len(param_list)}")

    return "\n".join(output)


def get_netcdf_fda_data(scan_law_id: int) -> Dict[str, list]:
    """
    Get FDA data from NetCDF file for the specified scan law ID.

    Args:
        scan_law_id: The scan law ID to look up

    Returns:
        Dictionary with fda_mp_pointer_alpha and fda_mp_pointer_epsilon arrays
    """
    try:
        import netCDF4 as nc
        import numpy as np

        netcdf_file = "assets/Input_Files/IRS/MTS1_IRS_SCANLAW_0000_v2.nc"

        # Try different possible paths
        possible_paths = [
            netcdf_file,
            f"../{netcdf_file}",
            f"../../{netcdf_file}",
            f"../../../{netcdf_file}"
        ]

        netcdf_path = None
        for path in possible_paths:
            if os.path.exists(path):
                netcdf_path = path
                break

        if not netcdf_path:
            logger.warning(f"NetCDF file not found, tried paths: {possible_paths}")
            return {}

        with nc.Dataset(netcdf_path, 'r') as dataset:
            # Get scan law IDs
            scan_law_ids = dataset.variables['scan_law_id'][:]

            # Find index for the target scan law ID
            target_indices = np.where(scan_law_ids == scan_law_id)[0]
            if len(target_indices) == 0:
                logger.warning(f"Scan law ID {scan_law_id} not found in NetCDF file")
                return {}

            target_index = target_indices[0]
            logger.info(f"Found scan law ID {scan_law_id} at NetCDF index {target_index}")

            # Get FDA pointer data
            fda_alpha = dataset.variables['fda_mp_pointer_alpha'][target_index, :]
            fda_epsilon = dataset.variables['fda_mp_pointer_epsilon'][target_index, :]

            # Convert to regular arrays if masked
            if hasattr(fda_alpha, 'data'):
                fda_alpha = fda_alpha.data
            if hasattr(fda_epsilon, 'data'):
                fda_epsilon = fda_epsilon.data

            # Remove filler values (255)
            filler_positions_alpha = np.where(fda_alpha == 255)[0]
            if len(filler_positions_alpha) > 0:
                valid_alpha = fda_alpha[:filler_positions_alpha[0]]
            else:
                valid_alpha = fda_alpha

            filler_positions_epsilon = np.where(fda_epsilon == 255)[0]
            if len(filler_positions_epsilon) > 0:
                valid_epsilon = fda_epsilon[:filler_positions_epsilon[0]]
            else:
                valid_epsilon = fda_epsilon

            return {
                'fda_mp_pointer_alpha': valid_alpha.tolist(),
                'fda_mp_pointer_epsilon': valid_epsilon.tolist()
            }

    except ImportError:
        logger.warning("netCDF4 not available, cannot read NetCDF data")
        return {}
    except Exception as e:
        logger.warning(f"Error reading NetCDF data: {e}")
        return {}


def format_xldt_cdc_input_style(parsed_data: Dict[str, Any]) -> str:
    """
    Format XLDT data in CDC input style showing NetCDF-sourced parameter values.

    Args:
        parsed_data: Parsed XLDT data from read_xldt_file()

    Returns:
        Formatted string showing CDC-style input format with NetCDF values
    """
    output = []
    output.append("XLDT Data - CDC Input Format")
    output.append("=" * 50)

    # File information
    if 'file_info' in parsed_data:
        file_info = parsed_data['file_info']
        output.append(f"\n📁 File Information:")
        output.append(f"   Path: {file_info.get('file_path', 'N/A')}")
        output.append(f"   Size: {file_info.get('file_size', 0)} bytes")

    # Header information
    if 'header' in parsed_data and parsed_data['header']:
        header = parsed_data['header']
        output.append(f"\n📋 XLDT Header:")
        output.append(f"   Format ID: {header.format_id}")
        output.append(f"   MM Slot: {header.mm_slot}")

        # Try to extract scan law ID from the file name or header
        scan_law_id = None
        file_path = parsed_data.get('file_info', {}).get('file_path', '')

        # Extract scan law ID from filename (e.g., ScanLaw_Central_Summer_16384.xldt)
        import re
        match = re.search(r'(\d+)\.xldt$', file_path)
        if match:
            scan_law_id = int(match.group(1))
            output.append(f"   Scan Law ID: {scan_law_id} (from filename)")

    # Get NetCDF data for FDA section
    if scan_law_id:
        netcdf_data = get_netcdf_fda_data(scan_law_id)
        if netcdf_data:
            output.append(f"\n📊 FDA Parameters (from NetCDF):")
            output.append(f"\n🔹 FDA Section")

            for param_name, values in netcdf_data.items():
                output.append(f"\n   {param_name} = {values}")
        else:
            output.append(f"\n❌ Could not load NetCDF data for scan law ID {scan_law_id}")

    # Parse and display other sections
    if 'sections' in parsed_data:
        output.append(f"\n📊 Other Sections:")

        for section_name, section_data in parsed_data['sections'].items():
            if section_name == 'FDA':
                # Skip FDA - we show NetCDF data instead
                continue

            if isinstance(section_data, XLDTSection) and section_name in XLDT_FIELD_MAPPINGS:
                output.append(f"\n🔹 {section_name} Section")

                # Parse the section data into structured parameters
                hex_data = section_data.data.hex().upper()
                try:
                    parameters = parse_section_fields(hex_data, section_name)

                    if parameters:
                        # Group parameters by base name
                        param_groups = {}
                        for param in parameters:
                            base_name = param.name.split('[')[0]
                            if base_name not in param_groups:
                                param_groups[base_name] = []
                            param_groups[base_name].append(param)

                        # Display each parameter group
                        for base_name, param_list in param_groups.items():
                            values = [p.value for p in param_list]
                            output.append(f"\n   {base_name} = {values}")
                    else:
                        output.append(f"   No valid parameters found")

                except Exception as e:
                    output.append(f"   Error parsing {section_name}: {e}")

            elif isinstance(section_data, XLDTSection):
                # Show basic info for unsupported sections
                output.append(f"\n🔹 {section_name} Section")
                output.append(f"   Type: binary")
                output.append(f"   Length: {section_data.length} bytes")
                hex_data = section_data.data.hex().upper()
                if len(hex_data) <= 32:
                    output.append(f"   Data: {hex_data}")
                else:
                    output.append(f"   Data: {hex_data[:32]}... ({len(hex_data)//2} bytes total)")

    return "\n".join(output)


def export_structured_parameters_to_dict(structured_params: Dict[str, List[StructuredParameter]]) -> Dict[str, Any]:
    """
    Export structured parameters to a dictionary format suitable for JSON export.

    Args:
        structured_params: Dictionary of structured parameters by section

    Returns:
        Dictionary with structured parameter data
    """
    result = {}

    for section_name, parameters in structured_params.items():
        section_data = {}

        # Group parameters by base name
        param_groups = {}
        for param in parameters:
            base_name = param.name.split('[')[0]
            if base_name not in param_groups:
                param_groups[base_name] = []
            param_groups[base_name].append(param)

        # Export each parameter group
        for base_name, param_list in param_groups.items():
            first_param = param_list[0]

            section_data[base_name] = {
                'name': base_name,
                'long_name': first_param.long_name,
                'data_type': first_param.data_type,
                'dimensions': first_param.dimensions,
                'shape': first_param.shape,
                'description': first_param.description,
                'values': [p.value for p in param_list],
                'count': len(param_list)
            }

        result[section_name] = section_data

    return result


# Example usage
if __name__ == "__main__":
    # Create a sample XLDT file and read it
    print("XLDT Reader Standalone Demo")
    print("=" * 40)
    
    # Sample XLDT data
    # Header: 0001 (format) + 0005 (MM slot 5)
    # CRC: 1234
    # Sample body data
    sample_hex = "000100051234DEADBEEFCAFEBABE"
    
    # Create sample file
    sample_file = "sample_xldt.bin"
    create_xldt_file(sample_hex, sample_file)
    
    # Read it back
    reader = XLDTReader()
    data = reader.read_xldt_file(sample_file)
    
    # Display results
    print(f"File: {data['file_info']['file_path']}")
    print(f"Size: {data['file_info']['file_size']} bytes")
    print(f"Format ID: {data['header'].format_id}")
    print(f"MM Slot: {data['header'].mm_slot}")
    print(f"Raw hex: {data['raw_hex']}")
    
    # Clean up
    os.remove(sample_file)
    print(f"Cleaned up: {sample_file}")
