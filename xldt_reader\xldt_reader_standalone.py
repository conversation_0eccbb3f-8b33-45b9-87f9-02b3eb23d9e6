"""
Standalone XLDT File Reader
===========================

This is a standalone version of the XLDT reader that doesn't depend on 
the complex import structure of the main codebase.

XLDT files are binary files with the following structure:
- XLDT Header: Format identifier and MM slot information
- CRC: Checksum for data validation
- Body: Various data sections (Header, LAC_Pointer, Retrace, Rally, FDA, MPA, etc.)
"""

import os
import struct
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
from dataclasses import dataclass
import logging

# Set up basic logging
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)


def hex2dec(hex_str: str) -> int:
    """Convert hex string to decimal integer."""
    return int(hex_str, 16)


def calculate_crc16(data: str) -> str:
    """
    Calculate CRC16 checksum for hex string data.
    This is a simplified implementation.
    """
    total = sum(int(data[i:i+2], 16) for i in range(0, len(data), 2))
    return f"{total & 0xFFFF:04X}"


@dataclass
class XLDTHeader:
    """XLDT Header structure"""
    format_id: int
    mm_slot: int
    body_length: Optional[int] = None


@dataclass
class XLDTSection:
    """XLDT Section structure"""
    section_name: str
    msdf_id: Optional[int]
    length: int
    order: int
    data: bytes


class XLDTReader:
    """
    XLDT File Reader class for parsing binary XLDT files.
    
    This class can read XLDT files created by the satellite operations system
    and parse them into structured data.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize XLDT Reader.
        
        Args:
            config_path: Path to configuration CSV file (e.g., IRS_SL_Conf.csv)
        """
        self.config_path = config_path
        self.config_df: Optional[pd.DataFrame] = None
        self.sections_config: Dict[str, Dict] = {}
        
        if config_path and os.path.exists(config_path):
            self._load_config()
    
    def _load_config(self) -> None:
        """Load section configuration from CSV file."""
        try:
            self.config_df = pd.read_csv(self.config_path)
            self.config_df.set_index("Section", inplace=True)
            
            # Convert to dictionary for easier access
            for section in self.config_df.index:
                self.sections_config[section] = {
                    'length': self.config_df.loc[section]['Length'],
                    'msdf_id_hex': self.config_df.loc[section]['MSDF_ID_Hex'],
                    'order': self.config_df.loc[section]['Order']
                }
            
            logger.info(f"Loaded XLDT configuration from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load XLDT configuration: {e}")
            raise
    
    def read_xldt_file(self, file_path: str) -> Dict[str, Any]:
        """
        Read and parse an XLDT binary file.
        
        Args:
            file_path: Path to the XLDT binary file
            
        Returns:
            Dictionary containing parsed XLDT data
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"XLDT file not found: {file_path}")
        
        logger.info(f"Reading XLDT file: {file_path}")
        
        try:
            with open(file_path, 'rb') as file:
                binary_data = file.read()
            
            # Convert binary data to hex string for processing
            hex_data = binary_data.hex().upper()
            
            logger.info(f"Read {len(binary_data)} bytes from XLDT file")
            
            # Parse the XLDT structure
            parsed_data = self._parse_xldt_data(hex_data)
            
            # Add file metadata
            parsed_data['file_info'] = {
                'file_path': file_path,
                'file_size': len(binary_data),
                'hex_length': len(hex_data)
            }
            
            return parsed_data
            
        except Exception as e:
            logger.error(f"Error reading XLDT file {file_path}: {e}")
            raise
    
    def _parse_xldt_data(self, hex_data: str) -> Dict[str, Any]:
        """
        Parse hex data into XLDT structure.
        
        Args:
            hex_data: Hexadecimal string representation of XLDT data
            
        Returns:
            Dictionary containing parsed sections
        """
        result = {
            'header': None,
            'crc': None,
            'sections': {},
            'raw_hex': hex_data
        }
        
        offset = 0
        
        # Parse XLDT Header (first 8 hex characters = 4 bytes)
        if len(hex_data) >= 8:
            header_hex = hex_data[0:8]
            result['header'] = self._parse_xldt_header(header_hex)
            offset = 8
            logger.info(f"Parsed XLDT header: {result['header']}")
        
        # If we have configuration, parse according to structure
        if self.sections_config:
            result['sections'] = self._parse_sections_with_config(hex_data, offset)
        else:
            # Generic parsing without configuration
            result['sections'] = self._parse_sections_generic(hex_data, offset)
        
        return result
    
    def _parse_xldt_header(self, header_hex: str) -> XLDTHeader:
        """
        Parse XLDT header from hex string.
        
        Args:
            header_hex: 8-character hex string representing the header
            
        Returns:
            XLDTHeader object
        """
        # XLDT Header structure: 0001 + MM_Slot (4 hex chars)
        format_id = hex2dec(header_hex[0:4])
        mm_slot = hex2dec(header_hex[4:8])
        
        return XLDTHeader(
            format_id=format_id,
            mm_slot=mm_slot
        )
    
    def _parse_sections_with_config(self, hex_data: str, offset: int) -> Dict[str, XLDTSection]:
        """
        Parse sections using configuration file.
        
        Args:
            hex_data: Complete hex data string
            offset: Current offset in hex string
            
        Returns:
            Dictionary of parsed sections
        """
        sections = {}
        current_offset = offset
        
        # Sort sections by order
        sorted_sections = sorted(
            self.sections_config.items(),
            key=lambda x: x[1]['order']
        )
        
        for section_name, config in sorted_sections:
            if section_name in ['XLDT_Header']:
                continue  # Already parsed
                
            if section_name == 'CRC':
                # CRC is typically 4 hex characters (2 bytes)
                if current_offset + 4 <= len(hex_data):
                    crc_hex = hex_data[current_offset:current_offset + 4]
                    sections[section_name] = XLDTSection(
                        section_name=section_name,
                        msdf_id=None,
                        length=2,
                        order=config['order'],
                        data=bytes.fromhex(crc_hex)
                    )
                    current_offset += 4
                continue
            
            # Parse regular sections
            section_length = config.get('length', 0)
            if isinstance(section_length, str) and section_length.strip() == '':
                continue
                
            section_length = int(section_length) if section_length else 0
            hex_length = section_length * 2  # Each byte = 2 hex characters
            
            if current_offset + hex_length <= len(hex_data):
                section_hex = hex_data[current_offset:current_offset + hex_length]
                sections[section_name] = XLDTSection(
                    section_name=section_name,
                    msdf_id=config.get('msdf_id_hex'),
                    length=section_length,
                    order=config['order'],
                    data=bytes.fromhex(section_hex)
                )
                current_offset += hex_length
                
                logger.info(f"Parsed section {section_name}: {section_length} bytes")
        
        return sections
    
    def _parse_sections_generic(self, hex_data: str, offset: int) -> Dict[str, Any]:
        """
        Generic parsing when no configuration is available.
        
        Args:
            hex_data: Complete hex data string
            offset: Current offset in hex string
            
        Returns:
            Dictionary with generic parsing results
        """
        remaining_data = hex_data[offset:]
        
        return {
            'remaining_hex': remaining_data,
            'remaining_bytes': len(remaining_data) // 2,
            'note': 'Generic parsing - no configuration file provided'
        }
    
    def validate_crc(self, hex_data: str, crc_section: XLDTSection) -> bool:
        """
        Validate CRC checksum.
        
        Args:
            hex_data: Complete hex data string
            crc_section: CRC section data
            
        Returns:
            True if CRC is valid, False otherwise
        """
        try:
            # Extract body data (everything except header and CRC)
            body_start = 8  # After XLDT header
            crc_start = body_start + 4  # Assuming CRC comes after header
            body_data = hex_data[crc_start:]
            
            # Calculate CRC using simplified method
            calculated_crc = calculate_crc16(body_data)
            provided_crc = crc_section.data.hex().upper()
            
            is_valid = calculated_crc == provided_crc
            logger.info(f"CRC validation: calculated={calculated_crc}, provided={provided_crc}, valid={is_valid}")
            
            return is_valid
            
        except Exception as e:
            logger.error(f"CRC validation failed: {e}")
            return False
    
    def export_to_dict(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Export parsed data to a more readable dictionary format.
        
        Args:
            parsed_data: Parsed XLDT data
            
        Returns:
            Dictionary with readable format
        """
        result = {
            'file_info': parsed_data.get('file_info', {}),
            'header': {},
            'sections': {}
        }
        
        # Convert header
        if parsed_data['header']:
            header = parsed_data['header']
            result['header'] = {
                'format_id': header.format_id,
                'mm_slot': header.mm_slot,
                'body_length': header.body_length
            }
        
        # Convert sections
        for section_name, section in parsed_data['sections'].items():
            if isinstance(section, XLDTSection):
                result['sections'][section_name] = {
                    'msdf_id': section.msdf_id,
                    'length': section.length,
                    'order': section.order,
                    'data_hex': section.data.hex().upper(),
                    'data_bytes': len(section.data)
                }
            else:
                result['sections'][section_name] = section
        
        return result


def create_xldt_file(hex_data: str, file_path: str) -> None:
    """
    Create an XLDT file from hex data.
    
    Args:
        hex_data: Hex string containing file data
        file_path: Path where to create the XLDT file
    """
    try:
        binary_data = bytes.fromhex(hex_data)
        
        # Ensure directory exists
        dir_path = os.path.dirname(file_path)
        if dir_path:  # Only create directory if there is one
            os.makedirs(dir_path, exist_ok=True)
        
        with open(file_path, 'wb') as f:
            f.write(binary_data)
        
        logger.info(f"Created XLDT file: {file_path} ({len(binary_data)} bytes)")
        
    except Exception as e:
        logger.error(f"Error creating XLDT file: {e}")
        raise


# Example usage
if __name__ == "__main__":
    # Create a sample XLDT file and read it
    print("XLDT Reader Standalone Demo")
    print("=" * 40)
    
    # Sample XLDT data
    # Header: 0001 (format) + 0005 (MM slot 5)
    # CRC: 1234
    # Sample body data
    sample_hex = "000100051234DEADBEEFCAFEBABE"
    
    # Create sample file
    sample_file = "sample_xldt.bin"
    create_xldt_file(sample_hex, sample_file)
    
    # Read it back
    reader = XLDTReader()
    data = reader.read_xldt_file(sample_file)
    
    # Display results
    print(f"File: {data['file_info']['file_path']}")
    print(f"Size: {data['file_info']['file_size']} bytes")
    print(f"Format ID: {data['header'].format_id}")
    print(f"MM Slot: {data['header'].mm_slot}")
    print(f"Raw hex: {data['raw_hex']}")
    
    # Clean up
    os.remove(sample_file)
    print(f"Cleaned up: {sample_file}")
