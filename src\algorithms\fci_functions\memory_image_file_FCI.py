
# Local imports
from src.utils.import_utils import mamut
from src.utils.conversion_utils import dec2hex, hex2dec

# Utility functions (copied from mamut.py for standalone use)
def int_2_hex(dec):
    if dec >= 0:
        return "{:X}".format(dec)
    else:
        return hex((dec + (1 << 32)) % (1 << 32))[2:]

def hex_2_int(hex_string):
    if not isinstance(hex_string, str):
        hex_string = str(hex_string)
    return int(hex_string, 16)

from src.logger_wrapper import logger

def memory_image_file_FCI(
    image,
    TC_Type,
    str_path_full: str,
    str_sat: str,
    device,
    lst_channels: list
):
    """MEMORY IMAGE  Writing the memory image"""
    # Preparation of data for the Image File header structure
    dict_mem_header = {}
    # image: list of [start, count, data] (header row at index 0)
    # Build hex_data and get start/end/count
    hex_data = ""
    for row in image[1:]:
        hex_data += row[2]

    # Unit
    if device == "VCU_RAMN" or device == "VCU_RAMR":
        int_SAU = 2
    else:
        int_SAU = 1

    # Calculate integer value of the start address
    start_addr_int = hex2dec(image[1][0])
    start_addr_hex = int_2_hex(start_addr_int)

    # Calculate integer value of the end address
    last_block_start_addr_int = hex2dec(image[-1][0])
    last_block_count_int = hex2dec(image[-1][1])
    end_addr_int = last_block_start_addr_int + last_block_count_int - int_SAU
    end_addr_hex = int_2_hex(end_addr_int)

    # Creation of the Header
    dict_mem_header["ID"] = 1
    dict_mem_header["VERSION"] = 3.0
    dict_mem_header["DOMAIN"] = f"{str_sat[0:2]}{str_sat[4]}{str_sat[6]}"
    dict_mem_header["TYPE"] = "PATCH"
    dict_mem_header["DESCRIPTION"] = (
        f"{device},for_FCI_{TC_Type},channel_{','.join(lst_channels).replace('.', '')}"
    )
    dict_mem_header["SOURCE"] = "CDC SOET"
    dict_mem_header["CREATIONDATE"] = ""
    dict_mem_header["MODEL"] = ""
    dict_mem_header["MODELVER"] = ""
    dict_mem_header["DEVICE"] = device
    dict_mem_header["STARTADDR"] = f"0x{start_addr_hex}"
    dict_mem_header["ENDADDR"] = f"0x{end_addr_hex}"
    # Set LENGTH as the number of data lines (words)
    dict_mem_header["LENGTH"] = str(len(image) - 1)
    dict_mem_header["CHECKSUM"] = ""
    dict_mem_header["UNIT"] = int_SAU

    # Use mamut.memory_image to handle header, checksum, and file writing
    # Create memory_image object with header and image rows (excluding header row)
    mem_img = mamut.memory_image(dict_header=dict_mem_header, image_rows=image[1:])
    # Write file with fletcher16 checksum, hex_count=2 (for PIXMAP)
    mem_img.create_file(str_path_full, str_checksum="fletcher16", hex_count="2")

