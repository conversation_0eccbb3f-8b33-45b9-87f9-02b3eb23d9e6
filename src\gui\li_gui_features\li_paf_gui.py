import tkinter as tk
from tkinter import ttk
from typing import List
from src import functions
from src.utils.import_utils import basics, config
from src.utils.activity_params import ActivityParams
from ...logger_wrapper import logger
from ..frames import BaseFrame
from ..theme_manager import ThemeManager
from ..custom_widgets import PrimaryCard, SecondaryCard, AccentCard, SuccessCard

class LI_PAF_GUI(BaseFrame):
    """GUI for LI PAF Configuration"""

    def __init__(self, parent: tk.Widget, act_params: ActivityParams, *args, **kwargs):
        """Initialize LI PAF Configuration GUI frame."""
        self.act_params = act_params
        # Initialize widget variables
        self.lst_variables_fee = []
        self.input_icid = None
        self.input_icid_ver = None
        super().__init__(parent, *args, **kwargs)

    def create_widgets(self):
        """Create and layout GUI components using grid."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Main frame using grid
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Configure main layout rows/columns
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)

    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"LI PAF Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew") # Use grid

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5) # Use grid

    def _create_body(self, parent):
        """Creates the body section with two columns using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)

        # Configure grid columns for body
        body_frame.columnconfigure(0, weight=1, minsize=250) # Left column
        body_frame.columnconfigure(1, weight=1, minsize=250) # Right column
        body_frame.rowconfigure(0, weight=1) # Allow cards to expand vertically if needed

        # Create left and right column frames using grid
        left_column = ttk.Frame(body_frame)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        left_column.rowconfigure(0, weight=0) # Card row
        left_column.rowconfigure(1, weight=1) # Spacer
        left_column.columnconfigure(0, weight=1)

        right_column = ttk.Frame(body_frame)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        right_column.rowconfigure(0, weight=0) # Card row
        right_column.rowconfigure(1, weight=1) # Spacer
        right_column.columnconfigure(0, weight=1)

        self._create_fee_selection(left_column)
        self._create_icid_config(right_column)

    def _create_fee_selection(self, parent):
        """Creates the FEE Selection card in the left column."""
        fee_card = PrimaryCard(parent, title="FEE Selection", padding=5)
        fee_card.grid(row=0, column=0, sticky="ew") # Use grid, stick ew

        fee_content = fee_card.get_content_frame()
        fee_content.columnconfigure(0, weight=1) # Configure grid inside card

        lst_fee_name = ["ALL FEEs", "FEE 1", "FEE 2", "FEE 3", "FEE 4"]
        self.lst_variables_fee = []

        # --- Helper functions for FEE checkboxes --- (Keep these local to _create_fee_selection)
        def toggle_all_fees():
            is_checked = self.lst_variables_fee[0].get() == lst_fee_name[0]
            for i in range(1, len(lst_fee_name)):
                self.lst_variables_fee[i].set(lst_fee_name[i] if is_checked else "")

        def fee_checkbox_clicked(index):
            if self.lst_variables_fee[index].get() == "": # If one is unchecked
                self.lst_variables_fee[0].set("") # Uncheck "ALL"
            else: # Check if all others are checked
                all_checked = all(self.lst_variables_fee[i].get() != "" for i in range(1, len(lst_fee_name)))
                if all_checked:
                    self.lst_variables_fee[0].set(lst_fee_name[0]) # Check "ALL"
        # --- End Helper functions --- 

        # Create checkboxes using grid
        for i, fee_name in enumerate(lst_fee_name):
            str_variable = tk.StringVar()
            command = toggle_all_fees if i == 0 else lambda idx=i: fee_checkbox_clicked(idx)
            fee_cb = ttk.Checkbutton(
                fee_content,
                text=fee_name,
                variable=str_variable,
                onvalue=fee_name,
                offvalue="",
                command=command,
                style="PrimaryCard.TCheckbutton" # Apply card style
            )
            fee_cb.grid(row=i, column=0, sticky="w", pady=1, padx=5)
            self.lst_variables_fee.append(str_variable)

    def _create_icid_config(self, parent):
        """Creates the ICID Configuration card in the right column."""
        icid_card = SuccessCard(parent, title="ICID Configuration", padding=5)
        icid_card.grid(row=0, column=0, sticky="ew") # Use grid, stick ew

        icid_content = icid_card.get_content_frame()        # Use grid for labels and entries within the card content for better alignment
        icid_content.columnconfigure(1, weight=1) # Allow entry to expand slightly        
        ttk.Label(icid_content, text="ICID:", style="SuccessCard.TLabel").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.input_icid = ttk.Entry(icid_content, width=10)
        self.input_icid.grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        self.input_icid.insert(0, "1")

        ttk.Label(icid_content, text="ICID Version:", style="SuccessCard.TLabel").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.input_icid_ver = ttk.Entry(icid_content, width=10)
        self.input_icid_ver.grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        self.input_icid_ver.insert(0, "1")

    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=1)

        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5) # Use row 1

        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )

    def _get_selected_check_button(self, variables: List[tk.StringVar]) -> List[str]:
        """Helper to get selected values from a list of checkbutton variables."""
        return [var.get() for var in variables if var.get()]

    def execute(self):
        """Validate inputs and execute the PAF generation."""
        # Ensure other widgets are created
        if self.input_icid is None or self.input_icid_ver is None:
            logger.error("Execute called before widgets created in LI_PAF_GUI")
            basics.pop_up_message("Error", "GUI not fully initialized.", "error")
            return

        self.update_status("Validating inputs...")
        self.update_idletasks()

        icid_val = self.input_icid.get()
        icid_ver_val = self.input_icid_ver.get()
        selected_fees_raw = self._get_selected_check_button(self.lst_variables_fee)

        # --- Input Validation ---
        if not icid_val:
            raise ValueError("ICID cannot be empty.")
        icid_int = int(icid_val) # This can raise ValueError

        if not icid_ver_val:
            raise ValueError("ICID Version cannot be empty.")
        icid_ver_int = int(icid_ver_val)

        if not selected_fees_raw:
            raise ValueError("No FEE selected.")

        # Handle "ALL FEEs" selection
        if "ALL FEEs" in selected_fees_raw:
            fees_to_process = [f"FEE {i}" for i in range(1, 5)]
        else:
            fees_to_process = selected_fees_raw

        # --- Execute Generation ---
        self.update_idletasks()
        self.act_params.icid = icid_int
        self.act_params.icid_ver = icid_ver_int
        logger.info(f"Generating PAF Configuration - ICID: {icid_int}, Version: {icid_ver_int}, FEEs: {fees_to_process}")

        functions.generate_outputs(
            act_params=self.act_params,
            lst_fee=fees_to_process
        )

        basics.pop_up_message("Success", "PAF generated successfully.", "info")

    def back(self):
        """Handle back navigation"""
        self.app.back()
