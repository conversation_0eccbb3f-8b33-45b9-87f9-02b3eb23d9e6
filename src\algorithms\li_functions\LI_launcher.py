"""
Lightning Imager (LI) launcher module.

This module contains the main entry points for different LI instrument configuration activities.
"""

import os
import pandas as pd
from typing import List

# Local imports
from src.utils.import_utils import config, basics
from .LI_functions import ( # Import specific functions
    LI_OPER_CONF,
    LI_MEMORY_MERGE,
    LI_LME_CONF,
    LI_BASIC_CONF,
    LI_Cal,
)
from src.utils.activity_params import ActivityParams
from src.logger_wrapper import logger
from src.utils.excel_utils import Read_Sheet

str_instrument = "LI"  # Instrument name
dict_config = config.CONFIG_INSTRUMENT["LI"]


def li_function_launcher(
    act_params: ActivityParams,
    lst_fee: List[str],
    lst_tc: List[str],
    str_mem_type: str,
    bln_merge: bool,
    str_file_for: str,
    lst_excl: List[str],
    lst_par: List[str]
) -> None:
    """Launch appropriate LI functions based on activity parameters"""
    logger.info(f"Launching LI function for activity: {act_params.activity}")
    
    lst_fee_channels = []
    # Get fee channels
    for str_fee in lst_fee:
        lst_fee_channels.append(int(str_fee[-1]))

    if "LOH BASIC Conf" in act_params.activity:
        LI_BASIC_CONF_Launcher(
            act_params,
            lst_fee_channels,
            str_mem_type, 
            lst_tc,       
            str_file_for, 
            bln_merge,    
        )

    elif "LOH OPER Conf" in act_params.activity:
        LI_OPER_CONF_Launcher(
            act_params,
            lst_fee_channels,
            str_mem_type, 
            lst_tc,       
            bln_merge,    
        )

    elif "LME Conf (Patch)" in act_params.activity:
        LI_LME_CONF_Launcher(
            act_params,
            str_mem_type, 
            lst_excl,     
            "Patch",
            "",           # Datapool_parameters (was empty string in original getattr-based call)
            "",           # FEE_PAF (was empty string in original getattr-based call)
        )

    elif "LME Conf (PAF)" in act_params.activity:
        LI_LME_CONF_Launcher(
            act_params,
            "",           # Memory_Type (was empty string in original getattr-based call)
            "",           # to_be_excluded (was empty string in original getattr-based call)
            "PAF",
            lst_par,
            lst_fee,      # FEE_PAF
        )

    elif "LI Calibration" in act_params.activity:
        LI_Calibr_Launcher(
            act_params,
            lst_fee_channels,
        )
        
    else:
        logger.warning(f"Unsupported activity: {act_params.activity}")



def LI_OPER_CONF_Launcher(
    activity_params,
    fee_id,
    Memory_Type,
    Which_TCs,
    bln_merge: bool = False,
):
    logger.info("LI_OPER_CONF_Launcher")

    # Specify here the files used by the specific section of code
    lst_used_files = ["DETCONF", "INSTCONF"]

    for str_sat in activity_params.satellites:
        str_tc_conf_folder = os.path.join(
            config.CONFIG_VALUES["config_folder"],
            str_sat,
            "LI_OPER_CONF_TC_Config.csv",
        )

        TC_Config_Input = pd.read_csv(
            str_tc_conf_folder,
            index_col="TC_Type",
            keep_default_na=False,
        )

        if len(fee_id) > 1:
            part_n = 1
        else:
            part_n = 0

        for int_fee in fee_id:
            logger.info(
                f"Started LI OPER CONF Memory Image File creation for {str_sat} FEE {int_fee}"
            )

            LI_OPER_CONF(
                activity_params,
                int_fee,
                str_sat,
                lst_used_files,
                Which_TCs,
                Memory_Type,
                TC_Config_Input,
                part_n,
                bln_merge,
            )

            part_n += 1 * len(Which_TCs)

        if bln_merge:
            logger.info(f"Merging LI OPER CONF SDRAM Memory Image Files for {str_sat}")

            LI_MEMORY_MERGE(
                activity_params,
                fee_id,
                os.path.join(config.CONFIG_VALUES["output_folder"], str_sat),
                str_sat,
            )

    logger.info("LI OPER CONF Memory Image File Creation Completed")


def LI_LME_CONF_Launcher(
    activity_params,
    Memory_Type,
    to_be_excluded,
    PAForPatch,
    Datapool_parameters,
    FEE_PAF,
):
    logger.info("LI_LME_CONF_Launcher")

    # Specify here the files used by the specific section of code
    lst_used_files = ["INSTCONF"]

    for str_sat in activity_params.satellites:
        str_tc_conf_folder = os.path.join(
            config.CONFIG_VALUES["config_folder"], str_sat
        )

        for File in lst_used_files:
            VSM_Tab = Read_Sheet(dict_config["vsm_path"], File)

        df_Mem_Map = Read_Sheet(
            os.path.join(str_tc_conf_folder, "LI_LME_CONF_Config.xlsx"), "Map"
        )
        df_Mem_Map_Par = Read_Sheet(
            os.path.join(str_tc_conf_folder, "LI_LME_PARAM_Config.xlsx"), "Map"
        )

        if PAForPatch == "Patch":
            logger.info(f"Started LI LME CONF Memory Image File creation for {str_sat}")
        else:
            logger.info(f"Started LI LME CONF PAF(s) creation for {str_sat}")

        LI_LME_CONF(
            activity_params,
            str_sat,
            lst_used_files,
            Memory_Type,
            df_Mem_Map,
            df_Mem_Map_Par,
            VSM_Tab,
            to_be_excluded,
            0,
            PAForPatch,
            Datapool_parameters,
            FEE_PAF,
        )

        if PAForPatch == "Patch":
            logger.info("LI LME CONF Memory Image File(s) Creation Completed")
        else:
            logger.info("LI LME CONF PAF(s) Creation Completed")


def LI_Calibr_Launcher(
    activity_params: ActivityParams,
    fee_id: int,
):
    logger.info("LI_Calibr_Launcher")

    # Specify here the files used by the specific section of code
    lst_used_files = ["INSTCONF"]

    str_tool = activity_params.activity

    if len(fee_id) < 4:
        str_tool = "LI Calibration Single"
    else:
        str_tool = "LI Calibration All"

    # Read from configuration the affected procedures
    df_paf_config = pd.read_csv(
        os.path.join(
            os.path.join(config.CONFIG_VALUES["config_folder"], str_instrument),
            "LI_PAF_Config.csv",
        ),
        index_col="Activity_Type",
        keep_default_na=False,
    )

    str_procedures = df_paf_config.loc[str_tool, "Procedure_Name"]

    for str_sat in activity_params.satellites:
        if str_tool == "LI Calibration All":
            # Update each PAF file required for the activity
            logger.info(
                f"Started PAF File Update for {str_sat} All FEEs {str_tool} Procedure {str_procedures}"
            )

            LI_Cal(
                activity_params,
                str_sat,
                lst_used_files,
                str_tool,
                str_procedures,
                fee_id[0]
            )
            # Important note: if generic Calibration, one setting is for all OCs. The values for FEE0 are taken, assuming they are the same across all FEEs and the ICID/ICIDVer is the same as well
        else:
            for int_fee in fee_id:
                # Update each PAF file required for the activity
                logger.info(
                    f"Started PAF File Update for {str_sat} FEE {int_fee} {str_tool} Procedure {str_procedures}"
                )

                LI_Cal(
                    activity_params,
                    str_sat,
                    lst_used_files,
                    str_tool,
                    str_procedures,
                    int_fee
                )

    logger.info("LI PAF File(s) Update Completed")


def LI_BASIC_CONF_Launcher(
    activity_params: ActivityParams,
    lst_fee_id: list,
    Memory_Type: str,
    Which_TCs: list,
    Memory_File_Flag: str,
    bln_merge: bool,
):
    logger.info("LI_BASIC_CONF_Launcher")

    # Specify here the files used by the specific section of code
    lst_used_files = ["DETCONF", "INSTCONF"]


    for str_sat in activity_params.satellites:
        df_TC_Config = pd.read_csv(
            os.path.join(
                config.CONFIG_VALUES["config_folder"],
                "LI",
                "LI_BASIC_CONF_TC_Config.csv",
            ),
            index_col="TC_Type",
            keep_default_na=False,
        )

        if len(lst_fee_id) > 1:
            part_n = 1
        else:
            part_n = 0

        for int_fee in lst_fee_id:
            logger.info(
                f"Started LI BASIC CONF Memory Image File(s) creation for {str_sat} FEE {int_fee}"
            )

            LI_BASIC_CONF(
                activity_params,
                int(int_fee),
                str_sat,
                lst_used_files,
                Which_TCs,
                Memory_Type,
                df_TC_Config,
                Memory_File_Flag,
                part_n,
                bln_merge,
            )

            if Memory_File_Flag == "Single TC":
                part_n += 1 * len(Which_TCs)
            else:
                part_n += 1

        if bln_merge:
            logger.info(f"Merging LI BASIC CONF SDRAM Memory Image Files for {str_sat}")

            LI_MEMORY_MERGE(
                activity_params,
                lst_fee_id,
                os.path.join(config.CONFIG_VALUES["output_folder"], str_sat),
                str_sat,
            )

    logger.info("LI BASIC CONF Memory Image File(s) Creation Completed")
