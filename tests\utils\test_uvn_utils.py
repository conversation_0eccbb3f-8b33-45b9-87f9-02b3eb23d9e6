import os
import shutil

import config
from src.logger_wrapper import logger

class Helper:
    """
    Helper class uses for uvn tests

    Returns:
        None
    """

    test_directory = os.path.join(config.CONFIG_VALUES["test_files"], "UVN")

    @staticmethod
    def get_test_files(test_directory):
        """Get the locations of the files used for testing

        Args:
            test_directory (str): path to the tests directory

        Returns:
            _type_: Location of the test files format xlsx,netcdf,image memory
        """

        excel_file_name = "MTS1_UVN_PTD_ref.xlsx"
        image_memory_file_name = "MTS1_UVN_PTD_ref.img"
        netcdf_file_name = "MTS1_UVN_PTD_ref.nc"

        # Reference files to compare against test results:
        input_file_xls = os.path.join(test_directory, excel_file_name)
        input_file_ncd = os.path.join(test_directory, netcdf_file_name)
        input_file_img = os.path.join(test_directory, image_memory_file_name)

        return input_file_xls, input_file_ncd, input_file_img

    @staticmethod
    def tear_down(output_file_xls, output_file_ncd, output_file_img):
        """
        Delete the output test files generated during test execution

        Args:
            output_file_xls (str): Location of the output xls file
            output_file_ncd (str): Location of the output netcdf file
            output_file_img (str): Location of the output image memory file

        """
        # Delete test files from previous validation runs
        os.remove(output_file_xls) if os.path.isfile(output_file_xls) else None
        os.remove(output_file_ncd) if os.path.isfile(output_file_ncd) else None
        os.remove(output_file_img) if os.path.isfile(output_file_img) else None

    @staticmethod
    def attach_input_instance_variable(
        request, input_file_xls, input_file_ncd, input_file_img
    ):
        """
        Attach  to the test input files to the request object

        Args:
            request (str): request object used to attach instance variable
            input_file_xls (str): Location of the input xls file
            input_file_ncd (str): Location of the input netcdf file
            input_file_img (str): Location of the input image memory file
        """
        request.cls.input_file_xls = input_file_xls
        request.cls.input_file_ncd = input_file_ncd
        request.cls.input_file_img = input_file_img

    @staticmethod
    def attach_output_instance_variable(
        request, output_file_xls, output_file_ncd, output_file_img
    ):
        """
        Attach  to the test output files to the request object

        Args:
            request (str): request object used to attach instance variable
            output_file_xls (str): _Location of the output xls file
            output_file_ncd (str): Location of the output netcdf file
            output_file_img (str): Location of the output image memory file
        """
        # Attach to the test class
        request.cls.output_file_xls = output_file_xls
        request.cls.output_file_ncd = output_file_ncd
        request.cls.output_file_img = output_file_img

    @staticmethod
    def clear_directory(path):
        """
        Keep the directory itself intact and remove all contents, including subdirectories

        Args:
            request (str): path of the directory to delete its content
        """

        for filename in os.listdir(path):
            file_path = os.path.join(path, filename)
            try:
                if os.path.isfile(file_path) or os.path.islink(file_path):
                    os.unlink(file_path)  # delete file or symbolic link
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)  # delete folder
            except Exception as e:
                logger.info(f"Failed to delete {file_path}. Reason: {e}")

    @staticmethod
    def get_netcdf_test_files():
        """
        Get the location of input and output netcdf test files

        """
        netcdf_file_name = "MTS1_UVN_PTD_ref.nc"
        input_file_ncd = os.path.join(Helper.test_directory, "Input", netcdf_file_name)
        output_file_ncd = os.path.join(
            Helper.test_directory, "Output", netcdf_file_name
        )

        return input_file_ncd, output_file_ncd

    @staticmethod
    def get_xml_test_files():
        """
        Get the location of input and output xml test files

        """

        xml_file_name = "MTS1_UVN_PTD_ref.xlsx"
        input_file_xml = os.path.join(Helper.test_directory, "Input", xml_file_name)
        output_file_xml = os.path.join(Helper.test_directory, "Output", xml_file_name)

        return input_file_xml, output_file_xml

    @staticmethod
    def get_image_memory_test_files():
        """
        Get the location of input and output image memory test files

        """
        image_memory_file_name = "MTS1_UVN_PTD_ref.img"
        input_file_img = os.path.join(
            Helper.test_directory, "Input", image_memory_file_name
        )
        output_file_img = os.path.join(
            Helper.test_directory, "Output", image_memory_file_name
        )

        return input_file_img, output_file_img

    @staticmethod
    def delete_test_files():
        """
        Delete test files in the output directory
        """
        directory = os.path.join(Helper.test_directory, "Output")
        for filename in os.listdir(directory):
            file_path = os.path.join(directory, filename)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)  # delete file
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)  # delete folder
            except Exception as e:
                logger.info(f"Failed to delete {file_path}. Reason: {e}")

    @staticmethod
    def get_test_directory():
        """Get the location of the test folder"""
        return Helper.test_directory
