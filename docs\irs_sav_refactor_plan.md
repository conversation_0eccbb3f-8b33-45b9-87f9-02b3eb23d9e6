# IRS SAV Codebase Refactoring Plan and Input File Usage Analysis

## 1. Input Files Read Multiple Times

### Observations
- The main input files in the IRS SAV workflow are NetCDF files, typically located in the instrument's input folder.
- These files are accessed via functions in `netcdf_utils.py`, especially:
  - `get_netcdf_variables_value` (called repeatedly for different variables, often on the same file)
  - `read_netCDF` and `netcdf2dict` (can open the same file multiple times if called with overlapping file lists)
  - `find_instrument_file` (called for each variable/file access)
- In `IRS_PAF.py` and `IRS_launcher.py`, it is common to call `get_netcdf_variables_value` several times in a row for the same NetCDF file but with different variable names (e.g., 'repeat_sequence_id', 'lac_pointer_index', 'repeat_sequence_length').
- This results in the same NetCDF file being opened and read multiple times within a single workflow execution, which is inefficient.

### Example (from IRS_PAF.py):
```python
repeat_sequence_ids = get_netcdf_variables_value('repeat_sequence_id', act_params, 'REPSEQ')
lac_pointer_index_full = get_netcdf_variables_value('lac_pointer_index', act_params, 'REPSEQ')
repeat_sequence_lengths = get_netcdf_variables_value('repeat_sequence_length', act_params, 'REPSEQ')
```
All three lines open and read the same REPSEQ NetCDF file separately.

### Recommendation
- Cache the opened NetCDF file or its variables for the duration of the workflow, so that repeated variable accesses do not require reopening the file.
- Consider a context manager or a helper function that loads all needed variables from a file in one go.

## 2. Refactoring Plan (Summary)
- Remove redundant NetCDF file reads by introducing a caching mechanism or batch variable loader.
- Refactor `get_netcdf_variables_value` and related calls to use the cache or batch loader.
- Split large functions in `IRS_PAF.py` and `IRS_launcher.py` for clarity and maintainability.
- Refactor repeated XML update logic in `xml_utils.py` into shared helpers.
- Improve naming, type hints, and docstrings throughout the codebase.

---

**This document will be updated as the refactoring progresses.**
