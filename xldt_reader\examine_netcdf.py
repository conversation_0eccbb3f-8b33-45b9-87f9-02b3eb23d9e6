#!/usr/bin/env python3
"""
Examine the NetCDF file to understand the scan law data structure.
"""

import os
import sys
import numpy as np

# Add the src directory to the path to import netcdf4
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    import netCDF4 as nc
except ImportError:
    print("❌ netCDF4 not available. Trying xarray...")
    try:
        import xarray as xr
        nc = None
    except ImportError:
        print("❌ Neither netCDF4 nor xarray available")
        sys.exit(1)

def examine_netcdf_file():
    """Examine the NetCDF file structure and find scan law data."""

    netcdf_file = "../assets/Input_Files/IRS/MTS1_IRS_SCANLAW_0000_v2.nc"

    if not os.path.exists(netcdf_file):
        print(f"❌ NetCDF file not found: {netcdf_file}")
        return

    print("🔍 Examining NetCDF File Structure")
    print("=" * 50)
    print(f"File: {netcdf_file}")

    # Open output file
    with open("netcdf_analysis.txt", "w") as f:
        def log(msg):
            print(msg)
            f.write(msg + "\n")
            f.flush()
    
    try:
        if nc:
            # Using netCDF4
            with nc.Dataset(netcdf_file, 'r') as dataset:
                print(f"\n📊 Dataset Info:")
                print(f"  Dimensions: {list(dataset.dimensions.keys())}")
                print(f"  Variables: {list(dataset.variables.keys())}")
                
                # Look for FDA-related variables
                fda_vars = [var for var in dataset.variables.keys() if 'fda' in var.lower()]
                print(f"\n🔍 FDA-related variables: {fda_vars}")
                
                # Look for pointer variables
                pointer_vars = [var for var in dataset.variables.keys() if 'pointer' in var.lower()]
                print(f"🔍 Pointer-related variables: {pointer_vars}")
                
                # Look for alpha/epsilon variables
                alpha_vars = [var for var in dataset.variables.keys() if 'alpha' in var.lower()]
                epsilon_vars = [var for var in dataset.variables.keys() if 'epsilon' in var.lower()]
                print(f"🔍 Alpha variables: {alpha_vars}")
                print(f"🔍 Epsilon variables: {epsilon_vars}")
                
                # Examine specific variables we're interested in
                target_vars = ['fda_mp_pointer_alpha', 'fda_mp_pointer_epsilon']
                
                for var_name in target_vars:
                    if var_name in dataset.variables:
                        var = dataset.variables[var_name]
                        print(f"\n📋 Variable: {var_name}")
                        print(f"  Shape: {var.shape}")
                        print(f"  Dimensions: {var.dimensions}")
                        print(f"  Data type: {var.dtype}")
                        
                        # Get the data
                        data = var[:]
                        print(f"  Data shape: {data.shape}")
                        print(f"  Data type: {type(data)}")
                        
                        # Show first few values
                        if data.ndim == 1:
                            print(f"  First 20 values: {data[:20]}")
                            print(f"  Unique values: {np.unique(data)[:20]}")
                        elif data.ndim == 2:
                            print(f"  First row (20 values): {data[0, :20]}")
                            print(f"  Shape: {data.shape}")
                            
                            # Look for scan law ID dimension
                            if 'scan_law_id' in dataset.dimensions:
                                scan_law_dim = dataset.dimensions['scan_law_id'].size
                                print(f"  Scan law dimension size: {scan_law_dim}")
                                
                                # Check different scan law IDs
                                scan_law_ids = dataset.variables['scan_law_id'][:]
                                print(f"  Available scan law IDs: {scan_law_ids}")

                                for i in range(data.shape[0]):
                                    row_data = data[i, :]
                                    scan_law_id = scan_law_ids[i] if i < len(scan_law_ids) else i
                                    print(f"  Scan law ID {scan_law_id}: first 30 values = {row_data[:30]}")

                                    # Check if this matches our expected pattern
                                    expected_start = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]
                                    actual_start = row_data[:10]
                                    if np.array_equal(actual_start, expected_start):
                                        print(f"    ✅ MATCHES expected pattern!")
                                    else:
                                        print(f"    Expected: {expected_start}")
                                        print(f"    Actual:   {actual_start}")

                                    # Show more values for scan law 16384 (our test file)
                                    if scan_law_id == 16384:
                                        print(f"    🎯 SCAN LAW 16384 (our test file):")
                                        print(f"    Full first 100 values: {row_data[:100]}")

                                        # Look for filler values (255)
                                        filler_positions = np.where(row_data == 255)[0]
                                        if len(filler_positions) > 0:
                                            print(f"    Filler (255) starts at position: {filler_positions[0]}")
                                            print(f"    Valid data length: {filler_positions[0]}")
                                            valid_data = row_data[:filler_positions[0]]
                                            print(f"    Valid data: {valid_data}")
                                        else:
                                            print(f"    No filler values found")
                    else:
                        print(f"\n❌ Variable {var_name} not found")
                
                # Check for scan_law_id variable
                if 'scan_law_id' in dataset.variables:
                    scan_law_ids = dataset.variables['scan_law_id'][:]
                    print(f"\n📋 Available scan_law_id values: {scan_law_ids}")
                
        else:
            # Using xarray
            dataset = xr.open_dataset(netcdf_file)
            print(f"\n📊 Dataset Info:")
            print(f"  Dimensions: {list(dataset.dims.keys())}")
            print(f"  Variables: {list(dataset.data_vars.keys())}")
            
            # Look for FDA-related variables
            fda_vars = [var for var in dataset.data_vars.keys() if 'fda' in var.lower()]
            print(f"\n🔍 FDA-related variables: {fda_vars}")
            
            # Examine specific variables
            target_vars = ['fda_mp_pointer_alpha', 'fda_mp_pointer_epsilon']
            
            for var_name in target_vars:
                if var_name in dataset.data_vars:
                    var = dataset[var_name]
                    print(f"\n📋 Variable: {var_name}")
                    print(f"  Shape: {var.shape}")
                    print(f"  Dimensions: {var.dims}")
                    print(f"  Data type: {var.dtype}")
                    
                    # Get the data
                    data = var.values
                    print(f"  First 20 values: {data.flat[:20]}")
                else:
                    print(f"\n❌ Variable {var_name} not found")
            
            dataset.close()
            
    except Exception as e:
        print(f"❌ Error reading NetCDF file: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    examine_netcdf_file()
