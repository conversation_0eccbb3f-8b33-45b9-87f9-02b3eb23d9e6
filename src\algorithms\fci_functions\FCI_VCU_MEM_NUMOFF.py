# Standard library imports
from datetime import datetime
from typing import Dict, List, Any
import math
import os
import pandas as pd

# Local imports
from src.utils.import_utils import config
from src.utils.conversion_utils import dec2hex, dec2bin, hex2dec
from src.utils.netcdf_utils import read_netCDF
from src.logger_wrapper import logger
from .memory_image_file_FCI import memory_image_file_FCI
from src.utils.activity_params import ActivityParams

# Type aliases
MemoryMapType = List[List[str]]
VSMDictType = Dict[str, List[int]]
NetCDFDictType = Dict[str, Any]

# Global variables for storing NOF (Numerical Offset) data
glob_NOF_StartAdd: Dict[str, int] = {}  # Maps channel names to their starting memory addresses
glob_NOF_BlkLen: Dict[str, int] = {}    # Maps channel names to their block lengths in memory

def _process_ir_pixel_offsets(VSM_dict: VSMDictType) -> None:
    """Process IR channel offsets and map them to individual pixel offsets.
    
    Args:
        VSM_dict: Dictionary containing VSM values keyed by mnemonic
        
    Each IR group (1-3) has up to 4 channels, and each channel has multiple pixels.
    This function maps channel-based offsets to individual pixel offsets.
    """
    logger.debug("Processing IR channel offsets to pixel offsets")
    for int_group in range(1, 4):  # IR groups 1,2,3
        pix_start = 1  # Pixel numbering starts at 1
        for int_channel in range(1, 5):  # Channels 1-4 in each group
            name = f"IR{int_group}_NUMOFFSET_CH{int_channel}"
            if name in VSM_dict:
                # Map channel offsets to individual pixel offsets
                # e.g., IR1_NUMOFFSET_CH1 -> [IR1_NUMOFFSET_P1, IR1_NUMOFFSET_P2, ...]
                for pixels in range(pix_start, pix_start + len(VSM_dict[name])):
                    VSM_dict[f"IR{int_group}_NUMOFFSET_P{pixels}"] = VSM_dict[name][pixels - pix_start]
                pix_start = pixels + 1  # Update start position for next channel
                del VSM_dict[name]  # Remove channel entry as it's now split into pixels

def _create_memory_blocks(
    address: str,
    binary_data: str,
    byte_length: int,
    dict_config: Dict[str, Any]
) -> List[List[str]]:
    """Create memory blocks for given data, splitting if necessary.
    
    Args:
        address: Memory address in hex
        binary_data: Binary data string
        byte_length: Length in bytes
        dict_config: Configuration dictionary
        
    Returns:
        List of memory blocks [address, count, data]
    """
    blocks: List[List[str]] = []
    addLenSAU = byte_length // 2  # SAU=2 means 16-bit alignment

    if addLenSAU > dict_config["count_max"]:
        logger.info("Splitting large data block into multiple memory units")
        # First block
        blocks.append([
            address[2:],  # Strip '0x' prefix
            dec2hex(dict_config["count_max"]),
            binary_data[0 : dict_config["count_max"] * 2]
        ])

        # Calculate remaining blocks
        line_slices = math.floor(addLenSAU / dict_config["count_max"])
        
        for slices in range(line_slices):
            # Calculate address for this block
            add_2 = dec2hex(hex2dec(address) + dict_config["count_max"] * (slices + 1))
            
            # Handle last block
            if slices == line_slices - 1:
                Number_Bytes = addLenSAU % dict_config["count_max"]
                End_index = byte_length * 2
            else:
                Number_Bytes = dict_config["count_max"]
                End_index = ((dict_config["count_max"] * (slices + 1)) * 2) + dict_config["count_max"] * 2
            
            Start_index = (dict_config["count_max"] * (slices + 1)) * 2
            
            if Number_Bytes != 0:
                blocks.append([
                    add_2,
                    dec2hex(Number_Bytes),
                    binary_data[Start_index:End_index]
                ])
    else:
        blocks.append([address[2:], dec2hex(byte_length), binary_data])

    return blocks

def FCI_VCU_MEM_NUMOFF(
    act_params: ActivityParams,
    satellite: str,
    used_files: List[str],
    Mem_Map: pd.DataFrame,
    df_VSM: pd.DataFrame,
    lst_channels: List[str],
    n_channel: int,
) -> None:
    """Generate FCI VCU Numerical Offset Memory configuration.
    
    Args:
        act_params: Activity parameters containing instrument configuration
        used_files: Dictionary mapping file types to their paths
        Mem_Map: Memory map dataframe
        df_VSM: VSM configuration dataframe
        lst_channels: List of channels to process
        n_channel: Channel number being processed
        
    The function performs these main steps:
    1. Read and process netCDF input files
    2. Map channel numerical offsets to memory addresses
    3. Generate memory image files
    """
    try:
        # Initialize variables and get config
        dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
        str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
        VSM_dict: VSMDictType = {}
        main_files=["INSTCONF"]

        # STEP 1: Read netCDF files
        logger.info("Reading netCDF files")
        dict_netCDF = read_netCDF(
            act_params,
            satellite,
            main_files,
            used_files,
            dict_config
        )

        # STEP 2: Process VSM variables
        logger.info("Processing VSM variables")
        VSM_Clean = df_VSM[df_VSM["Activity"].str.contains("VCU_NUMOFF")]
        VSM_Clean.set_index("Name", inplace=True)

        # Map netCDF values to mnemonics
        for variable in VSM_Clean.index:
            VSM_dict[VSM_Clean.loc[variable, "Mnemonic in Map"]] = dict_netCDF[variable].tolist()

        # Process IR channel offsets
        _process_ir_pixel_offsets(VSM_dict)

        # STEP 3: Create memory map
        logger.info("Creating memory map with values")
        Map_Clean = Mem_Map[
            Mem_Map["Parameter Logical Identifier"].isin(list(VSM_dict.keys()))
        ].copy()
        Map_Clean["Value"] = Map_Clean["Parameter Logical Identifier"].map(VSM_dict)

        # Filter for selected channels
        Map_Final = Map_Clean if lst_channels == ["All"] else Map_Clean[Map_Clean["Channel"].isin(lst_channels)]

        # STEP 4: Generate memory image content
        logger.info("Generating memory image content")
        Bytes_Dict_RAM: MemoryMapType = [["Start", "Count", "Data"]]

        for add in sorted(set(Map_Final["RAM Addr (Hex)"].to_list())):
            Map_Add = Map_Final[Map_Final["RAM Addr (Hex)"] == add]
            
            # Combine all bits for this address
            Bytes_Bin_All = ""
            AddLen_Bits = 0
            
            for sb in sorted(Map_Add["Start Bit"].to_list()):
                Map_Bit = Map_Add[Map_Add["Start Bit"] == sb]
                Val = int(Map_Bit["Value"].values[0])
                Len = int(Map_Bit["Length (bits)"].values[0])
                Bytes_Bin_All += dec2bin(Val).zfill(Len)
                AddLen_Bits += Len

            # Pad to 16-bit boundary (SAU=2)
            Bits_Left = 16 - AddLen_Bits
            Bytes_Content_Bin = f"{Bytes_Bin_All}{'0' * Bits_Left}" if Bits_Left > 0 else Bytes_Bin_All

            # Convert to bytes
            AddLen = len(Bytes_Content_Bin) // 8
            Bytes = dec2hex(int(Bytes_Content_Bin, 2)).zfill(2 * AddLen)

            # Create memory blocks
            blocks = _create_memory_blocks(add, Bytes, AddLen, dict_config)
            Bytes_Dict_RAM.extend(blocks)

        # STEP 5: Created dump data for PAF
        NOF_start_add = hex2dec(Bytes_Dict_RAM[1][0])
        NOF_end_add = hex2dec(Bytes_Dict_RAM[-1][0]) + int(int(Bytes_Dict_RAM[-1][1]) / 2)
        LenSAU = NOF_end_add - NOF_start_add

        # Store for global access
        glob_NOF_StartAdd[lst_channels[0]] = NOF_start_add
        glob_NOF_BlkLen[lst_channels[0]] = LenSAU

        # STEP 6: Create memory image file
        logger.info("Creating memory image file")
        device = f"VCU_RAM{'N' if act_params.side == 'Nominal' else 'R'}"
        part = f"PART{n_channel}_" if n_channel != 0 else ""
        
        # Generate filename
        str_filename = (
            f"{satellite[0:4]}{satellite[6]}_"
            f"{dict_config['file_class']}_OBS_MIMG_000_{device}_"
            f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{part}"
            f"{str(act_params.icid).zfill(2)[-2:]}{str(act_params.icid_ver).zfill(2)[-2:]}.IMG"
        )
        str_path_full = os.path.join(str_output_folder, str_filename)

        # Create memory image
        memory_image_file_FCI(
            Bytes_Dict_RAM,
            "VCU_NUMOFFSET",
            str_path_full,
            satellite,
            device,
            lst_channels
        )
        
        logger.info(f"Successfully generated numerical offset memory image: {str_filename}")
        
    except Exception as e:
        logger.error(f"Error in FCI VCU numerical offset generation: {str(e)}")
        raise
