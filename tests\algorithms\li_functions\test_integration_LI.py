### Imports
import os
import pytest
from typing import Optional # Added import
import shutil # Added import for shutil

from src.logger_wrapper import logger

# Modules to test
from src.utils.import_utils import basics, config
from src.functions import generate_outputs
from src.utils.activity_params import ActivityParams
from tests.helpers import (
    load_input_files,
    assert_paf_files,
    assert_bin_files,
    assert_img_files # Add if IMG files are checked for LI
)


@pytest.fixture()
def test_params() -> ActivityParams:
    """Fixture providing default test parameters for LI tests."""
    # Load input files
    load_input_files("LI")    # Create and return default test parameters
    # Note: Some parameters like icid, icid_ver might be overridden in specific tests
    params = ActivityParams.create(
        activity="LOH BASIC Conf",  # Default or common activity type for LI
        satellites=["MTG-I 1"],
        instrument="LI",
        icid=0, # Default ICID
        icid_ver=0, # Default ICID version
        test_mode=True,  # Enable test mode for all tests
        # Add other common LI parameters if any, e.g., side, if applicable
    )
    return params


# @pytest.mark.usefixtures("load_LI_test_files") # Replaced by test_params fixture
class Test_LI:
    def _prepare_run_output_location(
        self,
        test_params: ActivityParams,
        test_dir_name_for_output: str, # e.g., "Test_1", "Test_3"
        sub_dir_for_output: Optional[str] = None, # e.g., "a", "b", "FEE_1"
    ) -> str:
        """
        Prepares the output location for the current test run.
        Clears the directory if it exists, recreates it, and moves generated files
        from the default output location to this test-specific directory.
        Returns the path to the test-specific output directory.
        """
        # Construct the target path for this test's outputs
        # e.g., ...\\tests\\tests_assets\\LI\\Output\\Test_1
        target_run_output_base_path = os.path.join(
            config.CONFIG_VALUES["test_files"], # ...\\tests\\tests_assets
            test_params.instrument,             # LI
            "Output",                           # "Output" directory for actuals
            test_dir_name_for_output            # e.g., Test_1
        )

        if sub_dir_for_output:
            # e.g., ...\\tests\\tests_assets\\LI\\Output\\Test_3\\a
            target_run_output_path = os.path.join(target_run_output_base_path, sub_dir_for_output)
        else:
            target_run_output_path = target_run_output_base_path

        # Clear the target directory if it exists, then recreate it
        if os.path.exists(target_run_output_path):
            shutil.rmtree(target_run_output_path)
        os.makedirs(target_run_output_path, exist_ok=True)

        # Source of generated files (default output location)
        # e.g., ...\\assets\\Output\\MTG-I 1
        source_generated_files_path = os.path.join(
            config.CONFIG_VALUES["output_folder"], test_params.satellites[0]
        )

        # Move all files from source to target
        files_to_move = basics.files_in_dir(source_generated_files_path, bln_full_path=True)
        if not files_to_move:
            logger.warning(f"No files found in source output directory: {source_generated_files_path} for {test_dir_name_for_output}{'/' + sub_dir_for_output if sub_dir_for_output else ''}")

        for file_path in files_to_move:
            file_name = os.path.basename(file_path)
            destination_path = os.path.join(target_run_output_path, file_name)
            try:
                shutil.move(file_path, destination_path)
                # logger.info(f"Moved {file_name} to {target_run_output_path}") # Optional: verbose logging
            except Exception as e:
                logger.error(f"Error moving {file_name} from {source_generated_files_path} to {target_run_output_path}: {e}")
                # Consider if this should raise an error or just log

        return target_run_output_path

    def _verify_outputs(
        self,
        test_dir_name: str, 
        test_params: ActivityParams,
        sub_dir: Optional[str] = None, 
        check_paf: bool = False,
        check_bin: bool = False,
        check_img: bool = False, 
    ) -> None:
        """Helper method to verify test outputs against expected results."""
        # Expected files path construction (remains the same)
        case_identifier = test_dir_name.replace("Test_", "") 
        expected_top_level_folder_name = f"expected_for_test_case_{case_identifier}"
        base_expected_path_for_case = os.path.join(
            config.CONFIG_VALUES["test_files"], 
            test_params.instrument,             
            "Input",                            
            expected_top_level_folder_name      
        )
        if sub_dir:
            str_path_expected = os.path.join(base_expected_path_for_case, sub_dir)
        else:
            str_path_expected = base_expected_path_for_case
            
        # Path to the run output files for the specific test case
        # This is where actual generated files are after _prepare_run_output_location
        # e.g., ...\\tests\\tests_assets\\LI\\Output\\Test_1
        # or ...\\tests\\tests_assets\\LI\\Output\\Test_3\\a
        run_output_base_for_instrument = os.path.join(
            config.CONFIG_VALUES["test_files"], # ...\\tests\\tests_assets
            test_params.instrument,             # LI
            "Output"                            # "Output" directory for actuals
        )
        
        str_path_run_main_case_dir = os.path.join(run_output_base_for_instrument, test_dir_name)

        if sub_dir:
            str_path_run = os.path.join(str_path_run_main_case_dir, sub_dir)
        else:
            str_path_run = str_path_run_main_case_dir

        assertion_tag = test_dir_name
        if sub_dir:
            assertion_tag += f"/{sub_dir}"

        # Verification logic using the new str_path_run
        if check_paf:
            lst_path_expected_paf = basics.files_in_dir(
                str_path_expected, ".xml", bln_full_path=True
            )
            lst_path_run_paf = basics.files_in_dir(str_path_run, ".xml", bln_full_path=True)
            assert assert_paf_files(lst_path_expected_paf, lst_path_run_paf), f"PAF file assertion failed for {assertion_tag}"

        if check_bin:
            lst_path_expected_bin = basics.files_in_dir(
                str_path_expected, ".bin", bln_full_path=True
            )
            lst_path_run_bin = basics.files_in_dir(str_path_run, ".bin", bln_full_path=True)
            assert assert_bin_files(lst_path_expected_bin, lst_path_run_bin), f"BIN file assertion failed for {assertion_tag}"

        if check_img: 
            lst_path_expected_img = basics.files_in_dir(
                str_path_expected, ".IMG", bln_full_path=True
            )
            lst_path_run_img = basics.files_in_dir(str_path_run, ".IMG", bln_full_path=True)
            assert assert_img_files(lst_path_expected_img, lst_path_run_img), f"IMG file assertion failed for {assertion_tag}"

    @pytest.mark.skip
    def test_case_1(self, test_params: ActivityParams):
        """This covers Case 1 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH BASIC CONF Pixel Update for SDRAM"""
        test_params.icid = 1
        test_params.icid_ver = 1
        
        current_test_dir_name = "Test_1"
        current_sub_dir = None        
        generate_outputs(
            test_params, 
            lst_tc=["Basic_Conf_Pix"],
            lst_fee=["FEE 1", "FEE 2", "FEE 3", "FEE 4"],
            str_mem_type="RAM",
            bln_merge=True,
            str_file_for="Single TC"
        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)

    @pytest.mark.skip
    def test_case_2(self, test_params: ActivityParams):
        """This covers Case 2 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        Mission Scenario 1"""
        test_params.icid = 1
        test_params.icid_ver = 1
        test_params.mm_slot = 1 # Assuming MM is Mission Mode slot, added to params

        current_test_dir_name = "Test_2"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 1", "FEE 2", "FEE 3", "FEE 4"],
            lst_tc=[
                "Basic_Conf_Pix",
                "Conf_Det_Thr",
                "Delta_Thr",
                "Clamp",
                "Asic",
                "Filter_Thr",
                "Noise_Corr",
                "Offset_Lut",            ],
            str_file_for="Combined TCs",
            str_mem_type="RAM",
            bln_merge=True,
        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)

    @pytest.mark.skip
    def test_case_3_a(self, test_params: ActivityParams):
        """This covers Case 3a of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH BASIC CONF Update for SDRAM"""
        test_params.icid = 1
        test_params.icid_ver = 1

        current_test_dir_name = "Test_3"
        current_sub_dir = "a"

        generate_outputs(
            test_params,
            lst_fee=["FEE 1", "FEE 2", "FEE 3", "FEE 4"],
            lst_tc=["Offset_Lut"],
            str_file_for="Single TC",
            str_mem_type="RAM",
            bln_merge=True,

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)

    @pytest.mark.skip
    def test_case_3_b(self, test_params: ActivityParams):
        """This covers Case 3b of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH BASIC CONF Update for SDRAM"""
        test_params.icid = 1
        test_params.icid_ver = 1

        current_test_dir_name = "Test_3"
        current_sub_dir = "b"

        logger.info("Running test_case_3_b : test_mode = " + str(test_params.test_mode))
        
        generate_outputs(
            test_params,
            lst_fee=["FEE 1", "FEE 2", "FEE 3", "FEE 4"],
            lst_tc=["Asic"],
            str_file_for="Single TC",
            str_mem_type="RAM",
            bln_merge=True,

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)

    @pytest.mark.skip
    def test_case_4(self, test_params: ActivityParams):
        """This covers Case 4 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH BASIC CONF Update for SDRAM"""
        test_params.icid = 1
        test_params.icid_ver = 1
        
        current_test_dir_name = "Test_4"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 1"],
            lst_tc=[
                "Filter_Thr",
                "Noise_Corr",
                "Offset_Lut",
            ],
            str_file_for="Single TC", 
            str_mem_type="RAM",

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)

    @pytest.mark.skip
    def test_case_5(self, test_params: ActivityParams):
        """This covers Case 5 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH BASIC CONF Update for SDRAM"""
        test_params.icid = 1
        test_params.icid_ver = 1

        current_test_dir_name = "Test_5"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 1"],
            lst_tc=[
                "Filter_Thr",
                "Noise_Corr",
                "Offset_Lut",
            ],
            str_file_for="Combined TCs",
            str_mem_type="RAM",
            bln_merge=False,

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)

    @pytest.mark.skip
    def test_case_6(self, test_params: ActivityParams):
        """This covers Case 6 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH BASIC CONF Update for FLASH"""
        test_params.icid = 1
        test_params.icid_ver = 1

        current_test_dir_name = "Test_6"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 1", "FEE 2", "FEE 3", "FEE 4"],
            lst_tc=[
                "Basic_Conf_Pix",
                "Conf_Det_Thr",
                "Delta_Thr",
                "Clamp",
                "Asic",
                "Filter_Thr",
                "Noise_Corr",
                "Offset_Lut",
            ],
            str_file_for="Single TC",
            str_mem_type="Flash",
            bln_merge=False,

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)

    @pytest.mark.skip
    def test_case_7(self, test_params: ActivityParams):
        """This covers Case 7 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH BASIC CONF Update for FLASH"""
        test_params.icid = 1
        test_params.icid_ver = 1

        current_test_dir_name = "Test_7"
        current_sub_dir = None
        # LENGTH is not matching for the pafs (comment from original)

        generate_outputs(
            test_params,
            lst_fee=["FEE 1"],
            lst_tc=[
                "Filter_Thr",
                "Noise_Corr",
                "Offset_Lut",
            ],
            str_file_for="Single TC",
            str_mem_type="Flash",
            bln_merge=False,

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        # Order of checks changed to match original comment hint: check BINs first
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_bin=True)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True)


    @pytest.mark.skip
    def test_case_8(self, test_params: ActivityParams):
        """This covers Case 8 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH BASIC CONF Update for FLASH"""
        test_params.icid = 1
        test_params.icid_ver = 1

        current_test_dir_name = "Test_8"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 1"],
            lst_tc=[
                "Filter_Thr",
                "Noise_Corr",
                "Offset_Lut",
            ],
            str_file_for="Combined TCs",
            str_mem_type="Flash",
            bln_merge=False,

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)

    @pytest.mark.skip
    def test_case_9(self, test_params: ActivityParams):
        """This covers Case 9 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH OPER CONF Update for SDRAM"""
        test_params.icid = 1
        test_params.icid_ver = 1
        test_params.activity = "LOH OPER Conf" # Update activity type for this test

        current_test_dir_name = "Test_9"
        current_sub_dir = None

        generate_outputs(
            test_params, # test_params now carries the activity_type
            lst_fee=["FEE 1", "FEE 2", "FEE 3", "FEE 4"],
            lst_tc=["Oper_Conf"],
            str_mem_type="RAM",

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)
        test_params.activity = "LOH BASIC Conf" # Reset

    @pytest.mark.skip
    def test_case_10(self, test_params: ActivityParams):
        """This covers Case 10 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LOH OPER CONF Update for SDRAM"""
        # Merge not yet updated (comment from original)
        test_params.icid = 1
        test_params.icid_ver = 1
        test_params.activity = "LOH OPER Conf"

        current_test_dir_name = "Test_10"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 1", "FEE 2", "FEE 3", "FEE 4"],
            lst_tc=["Oper_Conf"],
            str_mem_type="RAM",
            bln_merge=True,

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_bin=True)
        test_params.activity = "LOH BASIC Conf" # Reset

    @pytest.mark.skip
    def test_case_12_1(self, test_params: ActivityParams):
        """This covers Case 12 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LME CONF RAM PAF"""
        test_params.icid = 1
        test_params.icid_ver = 1
        test_params.activity = "LME Conf (PAF)"

        current_test_dir_name = "Test_12_1"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 2"],
            lst_par=[
                "Configure Background & Activate/Deactivate MV Windows",
                "Update DR Det LUT Sel Vector",
            ],

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True)
        test_params.activity = "LOH BASIC Conf" # Reset

    @pytest.mark.skip
    def test_case_12_2(self, test_params: ActivityParams):
        """This covers Case 12 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LME CONF RAM PAF"""
        test_params.icid = 1
        test_params.icid_ver = 1
        test_params.activity = "LME Conf (PAF)"

        current_test_dir_name = "Test_12_2"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 2"],
            lst_par=[
                "Activate/Deactivate LOH Regulation",
                "Activate/Deactivate LME Regulation",
                "Activate/Deactivate MV Filtering",
                "Activate/Deactivate Clustering",
                "Activate/Deactivate Isolated DT Filtering",
                "Configure Background/MV Windows",
                "Enable/Disable RTS Unlock",
                "Activate/Deactivate dt neighbour",
                "Seasonal Configuration Update",
                "Update Season Conf Enable",
                "Update SDTF Enable Vector",
                "Update KAVG Sel Vector",
                "Update DR Delta Thresholds Enable Vector",
                "Update DR SDTF LUT Sel Vector",
                "Update RTS Unlock Repetition",
                "Update RTS Unlock Period",
            ],

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True)
        test_params.activity = "LOH BASIC Conf" # Reset

    @pytest.mark.skip
    def test_case_13(self, test_params: ActivityParams):
        """This covers Case 13 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LME CONF Update for SDRAM"""
        test_params.icid = 1
        test_params.icid_ver = 1
        test_params.activity = "LME Conf (Patch)"
        
        current_test_dir_name = "Test_13"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_excl=["icid", "icid_ver", "yaw_flip_status", "t_exp"],
            str_mem_type="RAM",

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True, check_img=True) # Assuming .IMG check is needed
        test_params.activity = "LOH BASIC Conf" # Reset

    @pytest.mark.skip
    def test_case_15(self, test_params: ActivityParams):
        """This covers Case 15 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LI Calibration"""
        test_params.icid = 1
        test_params.icid_ver = 1
        test_params.activity = "LI Calibration"

        current_test_dir_name = "Test_15"
        current_sub_dir = None

        generate_outputs(
            test_params,
            lst_fee=["FEE 1", "FEE 2", "FEE 3", "FEE 4"],

        )
        self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
        self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True) # Added check_paf=True and closing parenthesis
        test_params.activity = "LOH BASIC Conf" # Reset

    @pytest.mark.skip
    def test_case_16(self, test_params: ActivityParams):
        """This covers Case 16 of CDC_Validation (EUM/FLO/TEN/22/1341742)
        LI Calibration LOC1/2/3/4"""
        test_params.icid = 1
        test_params.icid_ver = 1
        test_params.activity = "LI Calibration"
        
        current_test_dir_name = "Test_16" # Main test case identifier
        lst_cases = [["FEE 1"], ["FEE 2"], ["FEE 3"], ["FEE 4"]]

        for lst_step in lst_cases:
            current_sub_dir = lst_step[0].replace(" ", "_") # e.g., "FEE_1"
            # Potentially update test_params for each step if needed, e.g. test_params.current_fee = lst_step[0]
            generate_outputs(
                test_params,
                # activity_type="LI Calibration", # Set in test_params
                # int_icid=1, # from test_params
                # int_icid_ver=1, # from test_params
                lst_fee=lst_step, # This overrides any default fee in test_params for this call
    
            )
            
            self._prepare_run_output_location(test_params, current_test_dir_name, current_sub_dir)
            self._verify_outputs(current_test_dir_name, test_params, sub_dir=current_sub_dir, check_paf=True)
        test_params.activity = "LOH BASIC Conf" # Reset
