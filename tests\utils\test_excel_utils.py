import pytest
import os
from src.utils.import_utils import config
from src.utils.excel_utils import Read_Sheet


@pytest.fixture
def excel_test_files():
    base_path = os.path.normpath(os.path.join(config.CONFIG_VALUES['test_files'], "excel"))
    return {
        "test_file": os.path.normpath(os.path.join(base_path, "FCI_VCU_CONF_Config.xlsx"))
    }


@pytest.fixture(autouse=True)
def test_read_sheet():
    # Assuming a test Excel file exists at a known path
    df = Read_Sheet(excel_test_files["test_file"], 'Map')
    assert df is not None
    assert not df.empty
