"""
Centralized in-memory data cache for input files.
This module loads and stores all relevant input data at program startup,
providing fast, unified access for all interfaces and backend logic.
"""

import os
import threading
from typing import Dict, Any

from src.logger_wrapper import logger

class DataCache:
    def add_file(self, file_path: str, file_bytes: bytes):
        """Add a file to the cache from bytes (for programmatic cache population)."""
        norm_fpath = os.path.normcase(os.path.normpath(os.path.abspath(file_path)))
        self.data[norm_fpath] = file_bytes
        logger.info(f"Added file to cache: {file_path}")
    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self.data: Dict[str, Any] = {}
        self._initialized = True

    def load_all(self, input_dirs):
        """
        Scan and load all relevant files from one or more input directories into memory.
        Accepts a string (single dir) or list of directories.
        """
        if isinstance(input_dirs, str):
            input_dirs = [input_dirs]
        total_files = 0
        for input_dir in input_dirs:
            logger.info(f"Loading input files from {input_dir} into DataCache...")
            for root, _, files in os.walk(input_dir):
                for fname in files:
                    fpath = os.path.abspath(os.path.join(root, fname))
                    norm_fpath = os.path.normcase(os.path.normpath(fpath))
                    try:
                        with open(fpath, 'rb') as f:
                            self.data[norm_fpath] = f.read()
                            total_files += 1
                    except Exception as e:
                        logger.error(f"Failed to load {fpath}: {e}")
        logger.info(f"Loaded {total_files} files into DataCache.")

    def get(self, key: str) -> Any:
        # Normalize the key for robust lookup
        norm_key = os.path.normcase(os.path.normpath(os.path.abspath(key)))
        result = self.data.get(norm_key)
        if result is not None:
            return result
        # Fallback: try to match by basename if full path not found
        key_basename = os.path.basename(norm_key)
        for k, v in self.data.items():
            if os.path.basename(k) == key_basename:
                # Only warn if not an output file (i.e., not in Output/)
                if "output" not in norm_key.lower():
                    logger.warning(f"Cache fallback: matched by basename for {key}")
                return v
        return None

    def keys(self):
        return self.data.keys()

# Singleton accessor
cache = DataCache()
