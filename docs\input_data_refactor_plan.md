# CDC-S Input Data Refactoring Plan: Centralized In-Memory Data Cache

## Overview
This document outlines the planned and implemented changes to refactor the CDC-S application to use a centralized in-memory data cache for all input files. The goal is to eliminate repeated file I/O, improve performance, and provide a unified data access interface for all GUI and backend modules.

---

## Motivation
- **Current Problem:** Input files (NetCDF, Excel, XML, etc.) in `assets/Input_Files` are opened and read multiple times by different modules and activities, leading to inefficiency and code duplication.
- **Solution:** Build a single in-memory database/cache at program startup, making all input data instantly accessible to all modules.

---

## Refactoring Steps

### 1. Design and Implement Data Cache
- Create a new module: `src/utils/data_cache.py`.
- Implement a singleton `DataCache` class that loads all files from `assets/Input_Files` into memory at startup.
- Provide a global `cache` object for unified access.
- Store file contents as bytes initially; extend to parse NetCDF, Excel, XML, etc. as needed.

### 2. Integrate Cache Initialization at Startup
- In `src/gui/frames.py`, update `create_app()` to call `cache.load_all(input_dir)` before showing the main window.
- Ensure the cache is loaded only once and is thread-safe.

### 3. Refactor File Access in Feature Modules
- Replace all direct file reads (open/read) in GUI feature modules and backend logic with `cache.get(filepath)`.
- For NetCDF, Excel, XML, etc., parse the cached bytes as needed (or extend the cache to store parsed objects).
- Remove redundant file open/close logic throughout the codebase.

### 4. Documentation and Developer Guidance
- Document the new data access pattern: "Always use the cache for input files. Never open files from disk directly."
- Provide examples for accessing NetCDF, Excel, XML, etc. from the cache.

### 5. Testing and Validation
- Update or add tests to ensure all modules work with the cache.
- Validate that no direct file reads remain in the codebase (except for cache initialization).
- Benchmark performance improvements.

---

## Benefits
- **Performance:** Dramatically reduced file I/O and faster data access.
- **Maintainability:** Centralized, consistent data access pattern.
- **Extensibility:** Easy to add new file types or data sources to the cache.

---

## Next Steps
- [x] Implement `data_cache.py` and integrate with app startup.
- [ ] Refactor all feature modules and backend logic to use the cache.
- [ ] Update documentation and developer onboarding materials.
- [ ] Test and validate the new architecture.

---

*This document will be updated as the refactoring progresses.*
