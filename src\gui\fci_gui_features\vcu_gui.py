from typing import List
import tkinter as tk
from tkinter import ttk
from src import functions
from src.utils.import_utils import basics, config
from ...logger_wrapper import logger
from ..frames import BaseFrame
from ..theme_manager import ThemeManager
from ..custom_widgets import PrimaryCard, SecondaryCard, AccentCard, SuccessCard


class VCU_GUI(BaseFrame):
    """Frame for FCI VCU configuration"""
    def __init__(self, parent, act_params, *args, **kwargs):
        self.act_params = act_params
        self.lst_variables_activity = []
        self.lst_variables_channel = [] # Stores individual channel StringVars
        self.var_all_channels = tk.StringVar() # Stores the state of the 'All' channel checkbox
        self.var_Side = tk.StringVar()
        self.var_VCUIF = tk.StringVar()
        self.icid_entry = None
        self.icid_ver_entry = None
        super().__init__(parent, *args, **kwargs)
        
    def create_widgets(self):
        """Create VCU GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)
        
        # Remove bottom padding: padding=5 -> padding=(5, 5, 5, 0)
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure main_frame's internal layout
        main_frame.rowconfigure(0, weight=0)  # Title
        main_frame.rowconfigure(1, weight=1)  # Body (expands)
        main_frame.rowconfigure(2, weight=0)  # Footer
        main_frame.columnconfigure(0, weight=1) # Left column for cards
        main_frame.columnconfigure(1, weight=1) # Right column for channel selection

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)

    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 5))  # Span both columns
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"FCI VCU Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew")
        
        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5)
    def _create_body(self, parent):
        """Creates the body section with two columns for configuration options."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, columnspan=2, sticky="nsew", pady=5)  # Span both columns
        
        # Configure columns for the two-column layout
        body_frame.columnconfigure(0, weight=1, minsize=300) # Left column
        body_frame.columnconfigure(1, weight=1, minsize=300) # Right column
        body_frame.rowconfigure(0, weight=1) # Allow columns to expand vertically

        # Left column frame
        left_column = ttk.Frame(body_frame)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        # Configure rows within left column
        left_column.rowconfigure(0, weight=0) # Side Card
        left_column.rowconfigure(1, weight=0) # Image Type Card
        left_column.rowconfigure(2, weight=0) # Activity Card
        left_column.rowconfigure(3, weight=0) # ICID Card
        left_column.rowconfigure(4, weight=1) # Spacer
        left_column.columnconfigure(0, weight=1)

        # Right column frame
        right_column = ttk.Frame(body_frame)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        right_column.rowconfigure(0, weight=1) # Channel Card (allow expansion)
        right_column.columnconfigure(0, weight=1)

        # Populate columns
        self._create_side_selection(left_column)
        self._create_image_type_selection(left_column)
        self._create_activity_selection(left_column)
        self._create_icid_config(left_column)
        self._create_channel_selection(right_column)
    def _create_side_selection(self, parent):
        """Creates the VCU-I Memory Side selection card."""
        side_card = PrimaryCard(parent, title="VCU-I Memory Side", padding=5)
        side_card.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        
        side_content = side_card.get_content_frame()
        for side in ["Nominal", "Redundant"]:
            side_radio = ttk.Radiobutton(side_content, text=side, variable=self.var_Side, value=side, style="PrimaryCard.TRadiobutton")
            side_radio.pack(anchor="w", pady=2, padx=5)
        self.var_Side.set("Nominal")
    def _create_image_type_selection(self, parent):
        """Creates the Memory Image Type selection card."""
        image_type_card = SecondaryCard(parent, title="Memory Image Type", padding=5)
        image_type_card.grid(row=1, column=0, sticky="ew", pady=5)

        image_type_content = image_type_card.get_content_frame()
        for fmt in ["Image per Channel", "Combined Image"]:
            fmt_radio = ttk.Radiobutton(image_type_content, text=fmt, variable=self.var_VCUIF, value=fmt, style="SecondaryCard.TRadiobutton")
            fmt_radio.pack(anchor="w", pady=2, padx=5)
        self.var_VCUIF.set("Image per Channel")
    def _create_activity_selection(self, parent):
        """Creates the Activity Selection card."""
        activity_card = AccentCard(parent, title="Activity Selection", padding=5)
        activity_card.grid(row=2, column=0, sticky="ew", pady=5)

        activity_content = activity_card.get_content_frame()
        for activity in ["Instconf", "Numerical Offset", "Pixel Mapping"]:
            var = tk.StringVar()
            # FIX: Add onvalue=activity and offvalue=""
            activity_cb = ttk.Checkbutton(
                activity_content, 
                text=activity, 
                variable=var, 
                onvalue=activity,  # Set the value to the activity string when checked
                offvalue="",       # Set the value to empty string when unchecked
                style="AccentCard.TCheckbutton"
            )
            activity_cb.pack(anchor="w", pady=2, padx=5)
            self.lst_variables_activity.append(var)


    def _create_icid_config(self, parent):
        """Creates the ICID Configuration card."""
        icid_card = SuccessCard(parent, title="ICID Configuration", padding=5)
        icid_card.grid(row=3, column=0, sticky="ew", pady=5)
        icid_content = icid_card.get_content_frame()
        
        icid_container = ttk.Frame(icid_content, style="SuccessCard.TFrame")        
        icid_container.pack(fill=tk.X, expand=True, padx=5, pady=5)
        
        ttk.Label(icid_container, text="ICID:", style="SuccessCard.TLabel").pack(side=tk.LEFT, padx=(0, 5))
        self.icid_entry = ttk.Entry(icid_container, width=8)
        self.icid_entry.pack(side=tk.LEFT, padx=(0, 10))
        self.icid_entry.insert(0, "1")
        
        ttk.Label(icid_container, text="Ver:", style="SuccessCard.TLabel").pack(side=tk.LEFT, padx=(0, 5))
        self.icid_ver_entry = ttk.Entry(icid_container, width=8)
        self.icid_ver_entry.pack(side=tk.LEFT, padx=0)
        self.icid_ver_entry.insert(0, "1")


    def _create_channel_selection(self, parent):
        """Creates the Channel Selection card with two internal columns."""
        channel_card = PrimaryCard(parent, title="Channel Selection", padding=5)
        channel_card.grid(row=0, column=0, sticky="nsew") # Fill the right column
        
        channel_content = channel_card.get_content_frame()
        # Configure columns inside the card content frame
        channel_content.columnconfigure(0, weight=1)
        channel_content.columnconfigure(1, weight=1)
        
        lst_channel_names = ["All"] + config.CONFIG_INSTRUMENT["FCI"]["channel_names"]
        middle_index = (len(lst_channel_names) + 1) // 2
        
        for i, channel in enumerate(lst_channel_names):
            var = tk.StringVar()
            col = 0 if i < middle_index else 1
            row = i if i < middle_index else i - middle_index
            channel_cb = ttk.Checkbutton(
                channel_content, text=channel, variable=var,
                onvalue=channel, offvalue="", style="PrimaryCard.TCheckbutton"
            )
            # Use grid inside the card content frame
            channel_cb.grid(row=row, column=col, sticky="w", pady=1, padx=5) 
            self.lst_variables_channel.append(var)
    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, columnspan=2, sticky="ew")  # Span across both columns
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=1)
        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5) 
        controls_frame.columnconfigure(0, weight=1)  # Ensure the frame expands properly

        ThemeManager.create_action_buttons(
            parent=controls_frame, # Pass the frame where buttons should be packed/gridded
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )

    def _get_selected_check_button(self, variables: List[tk.StringVar]) -> List[str]:
        """Helper to get selected values from a list of checkbutton variables."""
        return [var.get() for var in variables if var.get()]


    def _validate_inputs(self):
        """Validate user inputs."""
        # Ensure other widgets are created
        if self.icid_entry is None or self.icid_ver_entry is None:
             logger.error("Execute called before widgets created in VCU_GUI")
             basics.pop_up_message("Error", "GUI not fully initialized.", "error")
             return False

        str_VCU_image_format = self.var_VCUIF.get()
        self.act_params.side = self.var_Side.get()
        
        icid_val = self.icid_entry.get()
        icid_ver_val = self.icid_ver_entry.get()

        lst_part = self._get_selected_check_button(self.lst_variables_activity)
        lst_channel = self._get_selected_check_button(self.lst_variables_channel)

        if not icid_val:
            raise ValueError("ICID cannot be empty.")
        int_slicer = int(icid_val)

        if not icid_ver_val:
            raise ValueError("ICID Version cannot be empty.")
        int_slicer_2 = int(icid_ver_val)

        if not lst_channel:
            raise ValueError("Channel not selected.")
        
        if not lst_part:
            raise ValueError("Activity not selected.")

        # Handle "All" channels selection
        if "All" in lst_channel:
            lst_channel = config.CONFIG_INSTRUMENT["FCI"]["channel_names"]

        # Print parameters for debugging
        logger.info("VCU Configuration Parameters:")
        logger.info(f"Side: {self.var_Side.get()}")
        logger.info(f"Image Format: {str_VCU_image_format}")
        logger.info(f"Activities: {lst_part}")
        logger.info(f"Channels: {lst_channel}")
        logger.info(f"ICID: {icid_val}")
        logger.info(f"ICID Version: {icid_ver_val}")

        return True, str_VCU_image_format, lst_part, lst_channel, int_slicer, int_slicer_2


    def execute(self):
        """Execute the VCU configuration generation process."""
        # Validate inputs first. _validate_inputs raises ValueError on failure.
        # Let the main application handler catch exceptions if needed.
        success, str_VCU_image_format, lst_part, lst_channel, _, _ = self._validate_inputs()
        # The check 'if not success:' might be redundant if _validate_inputs always raises on error
        # or returns False only in non-error cases (like GUI not initialized).
        if not success:
            logger.warning("Input validation did not succeed or GUI not initialized.")
            return

        # Gather parameters
        # Note: _gather_parameters calls _validate_inputs again. This is inefficient.
        # Consider refactoring later to avoid double validation.
        params, gathered_lst_part, gathered_lst_channel, gathered_str_VCU_image_format = self._gather_parameters()

        # Pass all required parameters to generate_outputs
        functions.generate_outputs(
            act_params=params,
            lst_part=gathered_lst_part,
            lst_channel=gathered_lst_channel,
            str_VCU_image_format=gathered_str_VCU_image_format
        )

        logger.info("VCU configuration generated successfully")
        basics.pop_up_message("Success", "VCU configuration generated successfully", "info")

    
    def _gather_parameters(self):
        success, str_VCU_image_format, lst_part, lst_channel, icid_val, icid_ver_val = self._validate_inputs()
        
        # Update activity parameters
        self.act_params.side = self.var_Side.get()
        
        # Set the activity to a single string value (from the list of selected activities)
        # The ActivityParams.activity should be a single string (not a list)
        if lst_part:
            # For VCU activities, use "VCU Update" as the activity type in ActivityParams
            self.act_params.activity = "VCU Update"
            # Store the selected channels in the ActivityParams object
            self.act_params.channels = lst_channel
        else:
            logger.warning("No activities selected")
            self.act_params.activity = None
            self.act_params.channels = []
            
        self.act_params.icid = icid_val
        self.act_params.icid_ver = icid_ver_val
        
        # Log the parameters for better debugging
        logger.info(f"Gathered Activity params: activity={self.act_params.activity}, icid={self.act_params.icid}, icid_ver={self.act_params.icid_ver}")
        logger.info(f"Gathered VCU activities (lst_part): {lst_part}")
        logger.info(f"Gathered channels (lst_channel): {lst_channel}")
        logger.info(f"Gathered VCU image format: {str_VCU_image_format}")
        
        # Return all necessary parameters for generate_outputs
        return self.act_params, lst_part, lst_channel, str_VCU_image_format

    def back(self):
        """Handle back navigation"""
        self.app.back()
