# Standard library imports
import math
from typing import List
import pandas as pd
import os

# Local imports
from src.utils.import_utils import logger, basics, config
from src.utils.conversion_utils import dec2hex, hex2dec
from src.utils.activity_params import ActivityParams

def create_XLDT(hex_data: str, str_path: str) -> None:
    """Create an XLDT (Transfer Layer Data) file.

    Args:
        hex_data: Hex string containing file data
        str_path: Path where to create the XLDT file
    """
    logger.info(f"Create XLDT: Attempting to write to {str_path}")
    if not hex_data:
        logger.error("Create XLDT: hex_data is empty. Cannot create file.")
        return

    try:
        binary_format = bytearray.fromhex(hex_data)
        logger.info(f"Create XLDT: Successfully converted hex_data to bytearray. Length: {len(binary_format)} bytes.")
        if not binary_format:
            logger.warning("Create XLDT: binary_format is empty after conversion. File will be empty.")

        # Ensure the directory exists
        dir_name = os.path.dirname(str_path)
        if not os.path.exists(dir_name):
            logger.info(f"Create XLDT: Output directory {dir_name} does not exist. Attempting to create it.")
            try:
                os.makedirs(dir_name, exist_ok=True)
                logger.info(f"Create XLDT: Successfully created directory {dir_name}.")
            except Exception as e_dir:
                logger.error(f"Create XLDT: Failed to create directory {dir_name}: {e_dir}")
                return # Stop if directory cannot be created

        logger.info(f"Create XLDT: Writing {len(binary_format)} bytes to {str_path}")
        with open(str_path, "w+b") as obj_file:
            bytes_written = obj_file.write(binary_format)
            logger.info(f"Create XLDT: obj_file.write reported {bytes_written} bytes written.")
            # Forcing flush to disk
            obj_file.flush()
            os.fsync(obj_file.fileno())
            logger.info(f"Create XLDT: Flushed data to disk for {str_path}.")

        # Verify file existence and size after writing
        if os.path.exists(str_path):
            file_size = os.path.getsize(str_path)
            logger.info(f"Create XLDT: File {str_path} confirmed to exist. Size on disk: {file_size} bytes.")
            if file_size == 0 and len(binary_format) > 0:
                logger.warning(f"Create XLDT: File {str_path} was created but is empty (0 bytes), despite non-empty binary_format ({len(binary_format)} bytes).")
            elif file_size != len(binary_format):
                 logger.warning(f"Create XLDT: File {str_path} size on disk ({file_size} bytes) does not match expected binary_format size ({len(binary_format)} bytes).")
            else:
                logger.info(f"Create XLDT: File size {file_size} bytes matches expected binary_format size.")
        else:
            logger.error(f"Create XLDT: File {str_path} NOT found after write and close operation.")

    except ValueError as ve:
        logger.error(f"Create XLDT: ValueError during hex to binary conversion for {str_path}: {ve}. Input hex_data (first 100 chars): {{hex_data[:100]}}")
    except IOError as ioe:
        logger.error(f"Create XLDT: IOError during file operation for {str_path}: {ioe}")
    except Exception as e:
        logger.error(f"Create XLDT: An unexpected error occurred for {str_path}: {e}")

def memory_image(
    lst_packets: List[List[str]],
    df_packets: pd.DataFrame,
    Memory_Type: str,
    int_count_max: int
) -> List[List[str]]:
    """Process packet data into memory image format.

    Args:
        lst_packets: List of packet data as binary strings
        df_packets: DataFrame containing packet metadata
        Memory_Type: Type of memory ('Flash' or 'RAM')
        int_count_max: Maximum number of bytes per memory line

    Returns:
        List of memory image lines, each containing [address, count, data]
    """
def load_all_excel_configs_to_cache():
    """Load all Excel config files for FCI, LI, IRS into the cache at program startup."""
    import glob
    import os
    from src.utils.data_cache import cache
    config_dirs = [
        os.path.join("config", "FCI"),
        os.path.join("config", "LI"),
        os.path.join("config", "IRS"),
    ]
    for config_dir in config_dirs:
        excel_files = glob.glob(os.path.join(config_dir, "*.xlsx"))
        for file_path in excel_files:
            try:
                with open(file_path, "rb") as f:
                    cache.add_file(file_path, f.read())
            except Exception as e:
                print(f"Warning: Could not load {file_path} into cache: {e}")


# Track which satellites have already been cleaned in this session
_cleaned_satellites = set()

def generate_outputs(
    act_params: ActivityParams,
    lst_channel: List[str] = [],
    lst_part: List[str] = [],
    lst_tc: List[str] = [],
    str_mem_type: str = "",
    bln_merge: bool = False,
    lst_fee: List[str] = [],
    str_file_for: str = "",
    str_VCU_image_format: str = "",
    lst_par: List[str] = [],
    lst_excl: List[str] = []
) -> None:
    """Generate output files based on activity parameters and configuration.

    Args:
        act_params: ActivityParams object containing core configuration:
            instrument: Instrument type
            activity: Activity type (acts as tool name, e.g. "Repeat Sequence")
            satellites: List of satellites to process
            Plus activity-specific parameters (icid, scan_law_id, etc.)
        lst_channel: List of channels to process
        lst_part: List of parts to process
        lst_tc: List of telecommands
        str_mem_type: Memory type
        bln_merge: Whether to merge outputs
        lst_fee: List of FEE IDs to process
        str_file_for: File format specification
        str_VCU_image_format: VCU image format ('Combined Image' or 'Image per Channel')
        lst_par: List of parameters
        lst_excl: List of exclusions
    """
    # Log basic configuration
    logger.info("Starting the generation of files")
    logger.info(f"Instrument Selected: {act_params.instrument}")
    logger.info(f"Satellites Selected: {' '.join(act_params.satellites)}")
    logger.info(f"Activity Selected: {act_params.activity}")

    # Log instrument-specific parameters
    if act_params.instrument == "LI":
        if act_params.activity != "LME Conf (Patch)" and act_params.activity != "LME Conf (PAF)":
            if act_params.activity != "LI Calibration":
                logger.info(f"Memory Type: {str_mem_type}")
                logger.info(f"Memory Merge: {bln_merge}")

                if act_params.activity != "LOH OPER Conf":
                    logger.info(f"Image Files: {str_file_for}")
                    logger.info(f"TC Selected: {', '.join(lst_tc)}")
                else:
                    logger.info(f"TC Selected: {', '.join(lst_tc)}")

            for str_fee in lst_fee:
                logger.info(f"FEE Selected: {str_fee}")

        else:
            if act_params.activity == "LME Conf (Patch)":
                logger.info(f"Excluded parameters: {lst_excl}")
                logger.info(f"Memory Type: {str_mem_type}")
            else:
                fee_list = ""
                logger.info(f"Parameters Selected: {', '.join(lst_par)}")

                for fee_PAF in lst_fee:
                    if fee_PAF == 0:
                        fee_list += " All_FEEs"
                    else:
                        fee_list += f" FEE_ID-{fee_PAF[-1]}"

                logger.info(f"FEE Selected: {fee_list}")

        logger.info(f"ICID: {act_params.icid}")
        logger.info(f"ICID Version: {act_params.icid_ver}")

    elif act_params.instrument == "FCI":
        # Log configuration identifiers based on activity type
        if act_params.activity == "VCU Update":
            logger.info(f"ICID: {act_params.icid}")
            logger.info(f"ICID Version: {act_params.icid_ver}")
            logger.info(f"VCU Update Selected: {', '.join(lst_part)}")
            logger.info(f"Channel(s) Selected: {', '.join(lst_channel)}")
            logger.info(f"VCU Memory Side Selected: {act_params.side}")
            logger.info(f"Memory Image Type Selected: {str_VCU_image_format}")
        elif act_params.activity == "Mission Scenario (SL+APC+SSL)":
            logger.info(f"Scan Law ID: {act_params.scan_law_id}")
            logger.info(f"Selected Mass Memory Identifier: Slot ID {act_params.mm_slot}")
        elif act_params.activity == "Repeat Sequence":
            logger.info(f"Repeat Sequence ID: {act_params.repeat_sequence_id}")
        elif act_params.activity == "Scan Encoder LUT":
            logger.info(f"Scan Encoder Correction LUT ID: {act_params.scan_encode_correction_lut_id}")
            logger.info(f"Selected SCAE ID: {act_params.side}")

    elif act_params.instrument == "IRS":
        # Log configuration identifiers based on activity type
        if act_params.activity == "Mission Scenario (SL+APC+SSL)":
            logger.info(f"Scan Law ID: {act_params.scan_law_id}")
            logger.info(f"Selected Mass Memory Identifier: Slot ID {act_params.mm_slot}")
        elif act_params.activity == "Activity Table":
            logger.info(f"Activity Table Sel: {act_params.activity_table_slot}")
            logger.info(f"Activity Table ID: {act_params.activity_table_id}")
        elif act_params.activity == "Repeat Sequence":
            logger.info(f"Repeat Sequence ID: {act_params.repeat_sequence_id}")
        elif act_params.activity == "Scan Encoder LUT":
            logger.info(f"Scan Encoder Correction LUT ID: {act_params.scan_encode_correction_lut_id}")
            logger.info(f"Selected SCAE ID: {act_params.side}")
        elif act_params.activity in ("VCU Update","DPU"):
            logger.info("Selected VCU Gui")
        logger.info(f"functions.py: About to call irs_functions_launcher with act_params: {{act_params}}") # New log

    elif act_params.instrument == "UVN":
        # Log parameters specific to Memory Image Generator for UVN
        if act_params.activity == "Memory Image Generator": # Assuming this is the activity name
            if len(lst_par) == 4:
                logger.info(f"Selected ICU: {lst_par[2]}")         # ICU is the third item
                logger.info(f"Selected Software: {lst_par[0]}")    # Software is the first item
                logger.info(f"Selected Memory Device: {lst_par[1]}") # Memory Device/Table ID is the second item
                logger.info(f"Selected PTD table: {lst_par[3]}") # Memory Device/Table ID is the second item

            else:
                logger.warning(f"UVN Memory Image Generator: Expected 3 parameters in lst_par, but got {len(lst_par)}. Logging raw list: {lst_par}")
        else:
            # Generic logging for other UVN activities if any
            logger.info(f"UVN Activity: {act_params.activity}")
            if lst_par:
                logger.info(f"Parameters: {', '.join(lst_par)}")

    # Clean output directories only once per satellite per session
    global _cleaned_satellites
    for satellite in act_params.satellites:
        if satellite not in _cleaned_satellites:
            basics.for_all_files(
                "delete",
                os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
            )
            _cleaned_satellites.add(satellite)

    load_all_excel_configs_to_cache()
    # Handle LI-specific TC adjustment
    if act_params.instrument == "LI":
        if act_params.activity in ["LOH BASIC Conf", "LOH OPER Conf"]:
            if "Asic" in lst_tc:
                lst_tc.remove("Asic")
                lst_tc.append("Asic_Oper")

    # Execute instrument-specific processing
    if act_params.instrument == "LI":
        from src.algorithms.li_functions.LI_launcher import li_function_launcher
        li_function_launcher(
            act_params,
            lst_fee=lst_fee,
            lst_tc=lst_tc,
            str_mem_type=str_mem_type,
            bln_merge=bln_merge,
            str_file_for=str_file_for,
            lst_excl=lst_excl,
            lst_par=lst_par
        )

    elif act_params.instrument == "FCI":
        from src.algorithms.fci_functions.FCI_launcher import fci_functions_launcher
        fci_functions_launcher(act_params, lst_part, lst_channel, str_VCU_image_format)

    elif act_params.instrument == "IRS":
        from src.algorithms.irs_functions.IRS_launcher import irs_functions_launcher
        logger.info(f"functions.py: About to call irs_functions_launcher with act_params: {{act_params}}") # New log
        irs_functions_launcher(act_params)

    elif act_params.instrument == "UVN":
        from src.algorithms.uvn_functions.UVN_launcher import uvn_functions_launcher
        uvn_functions_launcher(act_params_value=act_params, lst_parameters_value=lst_par)
