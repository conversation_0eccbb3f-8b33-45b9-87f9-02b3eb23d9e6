# IRS Scan Law Binary Generation: Debugging & Refactoring Plan

## Purpose
This document tracks the investigation, debugging, and refactoring of the IRS Scan Law (XLDT) binary generation, with a focus on the Unknown_A6 section and its mapping to NetCDF and VSM configuration data.

---

## Checklist & Steps

### 1. Clarify Source of Truth
- [ ] Identify the authoritative source for each binary section (NetCDF, VSM, other configs, or reference binary).
- [ ] Document the expected mapping for each field/section.

### 2. Reverse-Engineer Binary Format
- [ ] Analyze the reference binary file(s) section-by-section.
- [ ] Document the structure, field types, and any scaling/packing rules.
- [ ] Compare with FCI Scan Law logic for similarities/differences.

### 3. Audit Generation Code
- [ ] Review `IRS_SL.py` and related code for how each section is generated.
- [ ] Trace the data flow from input (NetCDF/config) to binary output.
- [ ] Identify any hardcoded values, assumptions, or mismatches.

### 4. Align with FCI Logic (if appropriate)
- [ ] Compare IRS and FCI Scan Law generation logic.
- [ ] Refactor IRS code to follow FCI patterns if they are more robust/clear.

### 5. Implement Comprehensive Comparison Scripts
- [ ] Create scripts to compare each binary section with its input data (field-by-field, with hex and float/int representations).
- [ ] Automate reporting of matches/mismatches.

### 6. Validate with Reference Files
- [ ] Test the updated generation logic against known-good/reference binaries.
- [ ] Document any remaining discrepancies and their root causes.

### 7. Document the Mapping and Logic
- [ ] Write clear documentation for the mapping from NetCDF/config to binary for each section.
- [ ] Include diagrams or tables as needed.

### 8. Refactor and Modularize Code
- [ ] Modularize the generation code for maintainability and testability.
- [ ] Add unit/integration tests for each section.

---

## Ongoing Notes & Updates

- _2024-06-07_: Initial plan created. Investigation confirms systematic mismatch between generated and reference binaries for Unknown_A6 section. Next: try all possible NetCDF mappings and review code logic.

- _Add further updates here as progress is made._

---

## Reference Materials
- Reference binary files
- NetCDF input files
- VSM configuration files
- FCI Scan Law code and documentation

---

## Section Mapping: Authoritative Sources & Expected Data Flow

| Binary Section      | Source of Truth         | Input File(s) / Config      | Notes / Open Questions                |
|--------------------|------------------------|-----------------------------|---------------------------------------|
| XLDT_Header        | IRS_SL_Conf.csv, code   | IRS_SL_Conf.csv             | Static header, built in code          |
| CRC                | Generated (checksum)    | N/A                         | CRC16 of body, calculated in code     |
| Header             | IRS_SL_Conf.csv         | IRS_SL_Conf.csv             | Contains MSDF_ID, Length, scan_law_id |
| LAC_Pointer        | NetCDF, VSM             | SCANLAW NetCDF, VSM Excel   | Variables/fields mapped via VSM sheet |
| Retrace            | (Not implemented)       |                             | Section present but not generated     |
| Rally              | (Not implemented)       |                             | Section present but not generated     |
| FDA (Unknown_A6)   | NetCDF, VSM             | SCANLAW NetCDF, VSM Excel   | Mapping logic under investigation     |
| MPA                | NetCDF, VSM             | SCANLAW NetCDF, VSM Excel   | Complex mapping, see notes            |

### Detailed Notes
- **XLDT_Header, Header, CRC:**
  - Built using static values and parameters from IRS_SL_Conf.csv and the current scan_law_id.
- **LAC_Pointer:**
  - For each LAC, fields are mapped from NetCDF arrays using the VSM Excel sheet (SCANLAW tab). Only non-filler entries are included.
  - Integer fields are packed as hex, floats are scaled (x1000) and packed as float32 hex.
- **FDA (Unknown_A6):**
  - Each entry is built from NetCDF arrays (e.g., dwell_position_alpha, dwell_position_epsilon, fda_mp_pointer_alpha, fda_mp_pointer_epsilon), with mapping/order defined by the VSM sheet.
  - Floats are scaled (x1000) and packed as float32 hex. Integer fields are packed as hex.
  - Only non-filler entries are included. The section is truncated at the last valid entry.
  - The mapping logic is under investigation due to mismatches with reference binaries.
- **MPA:**
  - Similar to FDA, but with additional transformation for some fields (e.g., param1: (value * 2^25 / 360) / 1000).
  - Mapping/order defined by VSM sheet. Only non-filler entries are included.
- **Retrace, Rally:**
  - Sections are present in config but not currently generated (empty in code).

#### Open Questions / Uncertainties
- Is the field order and mapping in the VSM sheet fully correct for all sections?
- Are there any additional transformations or scaling factors required for certain fields (especially in MPA)?
- Are all NetCDF variables used in the correct slicing/indexing context?
- Is the logic for truncating at the last valid entry correct for all use cases?
- Are there any reference binaries with different/legacy formats?

_Update this table and notes as new findings emerge._

---

## Reverse-Engineering Reference Binary: Section-by-Section Analysis

### File: ScanLaw_West_Summer_16385.xldt

#### Initial 64 Bytes (Hex Dump)
```
00000000   00 01 00 07 00 00 13 2A 03 11 00 00 00 04 40 01  .......*......@.
00000010   13 28 03 00 00 14 00 01 00 4D 00 00 00 00 00 00  .(.......M......
00000020   00 00 00 00 00 00 00 00 00 00 04 00 00 14 00 4D  ...............M
00000030   00 98 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ...............
```

#### Decoding & Mapping
- **Bytes 0-1:** `00 01` — Likely XLDT_Header (static, matches code: `0001`)
- **Bytes 2-3:** `00 07` — Possibly section ID or order (matches config order for Header?)
- **Bytes 4-7:** `00 00 13 2A` — Could be a length or pointer (big-endian: 0x132A = 4906)
- **Bytes 8-9:** `03 11` — Possibly MSDF_ID or section identifier
- **Bytes 10-11:** `00 00` — Padding or reserved
- **Bytes 12-15:** `00 04 40 01` — Could be a float or two shorts (needs further mapping)
- **Bytes 16-31:** ... Continue mapping as more context is established ...

#### Structure & Field Types (Preliminary)
- The file starts with a static header (2 bytes), followed by section IDs, lengths, and possibly pointers or data fields.
- Multi-byte fields appear to be big-endian.
- Section boundaries likely align with config order and lengths.
- Further analysis required for each section (Header, LAC_Pointer, FDA, MPA, etc.).

#### Next Steps
- Continue decoding the next 64-128 bytes, mapping each segment to the expected section and field.
- Compare with the code logic and config to confirm field order, types, and scaling.
- Document each section as it is decoded.
- Update this section with diagrams/tables as needed.

#### Next 64 Bytes (Hex Dump)
```
00000040   00 00 05 00 00 14 00 98 00 E3 00 00 00 00 00 00  ........ã......
00000050   00 00 00 00 00 00 00 00 00 00 06 00 00 14 00 E3  ...............ã
00000060   01 32 00 00 00 00 00 00 00 00 00 00 00 00 00 00  .2..............
00000070   00 00 07 00 00 28 00 00 00 00 00 00 00 00 00 00  .....(..........
00000080   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
00000090   00 00 00 00 00 00 00 00 00 00 00 00 00 00 08 00  ................
000000A0   00 28 00 00 00 00 00 00 00 00 00 00 00 00 00 00  .(..............
000000B0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000000C0   00 00 00 00 00 00 00 00 00 00 09 00 00 28 00 00  .............(..
000000D0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000000E0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000000F0   00 00 00 00 00 00 0A 00 00 28 00 00 00 00 00 00  .........(......
```

#### Mapping & Observations
- **Bytes 0x40-0x4F:** Section likely continues with a new section header (`05 00 00 14 00 98 ...`), possibly LAC_Pointer or FDA start.
- **Pattern:** Each section seems to start with a short section ID, followed by a length, then data or padding.
- **Zeros:** Many zero bytes suggest padding or reserved space between sections, or uninitialized data fields.
- **Section IDs:** The incrementing values at the start of each 16-byte block (`05 00`, `06 00`, `07 00`, etc.) likely correspond to section or entry numbers.
- **Lengths:** The `00 14`, `00 28`, etc., may represent section or entry lengths (0x14 = 20, 0x28 = 40 bytes).

#### Updated Structure (Preliminary)
- Section headers appear to be 2 bytes (ID), followed by 2 bytes (length), then data or padding.
- Data fields may be packed after the header, with zeros for unused or filler fields.
- Section boundaries align with the config order and lengths.

#### Next Steps
- Continue decoding further, focusing on the transition from header/pointer sections to FDA/MPA data.
- Attempt to match section IDs and lengths to those in IRS_SL_Conf.csv and code logic.
- Document any confirmed mappings or discrepancies.

#### Next 128 Bytes and Data Section Transition (Hex Dump)
```
00000100   00 28 00 00 00 00 00 00 00 00 00 00 00 00 00 00  .(..............
00000110   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
00000120   00 00 0B 00 00 28 00 00 00 00 00 00 00 00 00 00  .....(..........
00000130   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
00000140   00 00 00 00 00 00 00 00 00 00 00 00 00 00 0C 00  ................
00000150   00 28 00 00 00 00 00 00 00 00 00 00 00 00 00 00  .(..............
00000160   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
00000170   00 00 00 00 00 00 00 00 00 00 0D 00 00 28 00 00  .............(..
00000180   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
00000190   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000001A0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000001B0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000001C0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000001D0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000001E0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00  ................
000001F0   00 00 00 00 00 00 00 00 00 00 00 00 00 00 10 00  ................
```

#### Observations & Section Analysis
- The pattern of section headers (e.g., `0B 00 00 28`, `0C 00 00 28`, etc.) continues, suggesting a table of pointers or entries.
- Each block starts with a 2-byte ID, 2-byte length, then mostly zeros (likely padding or reserved fields).
- The incrementing IDs and fixed lengths match the expected structure for pointer or index tables before the main data sections.
- The transition to non-zero, non-patterned data (starting around offset 0x3E0 and beyond) likely marks the start of the FDA/MPA or main data sections.

#### Updated Structure
- **0x000–0x3E0:** Section headers and pointer/index table, mostly zeros after headers.
- **0x3E0 and beyond:** Start of main data (FDA/MPA), with more varied, non-zero content.

#### Next Steps
- Decode the first entries of the main data section (from 0x3E0 onward), mapping them to expected FDA/MPA fields.
- Compare the data layout and field packing to the code logic and NetCDF input.
- Document any confirmed mappings, discrepancies, or new patterns.

#### Main Data Section (from 0x3E0 onward)
```
000003E0   00 00 01 00 0E 58 00 00 00 00 00 00 00 00 00 0C  .....X..........
000003F0   00 0C 00 0C BB DC FF FE 6A 10 00 05 00 05 00 0C  ....»Ü.þj.......
00000400   83 1A FF FF 16 02 00 01 00 01 00 0C 4C 3B FF FF  ...........L;..
00000410   C2 2C 00 01 00 01 00 0C 17 2B 00 00 6E 8B 00 01  Â,.......+..n..
...
000012D0   00 03 00 00 00 00 45 2F 00 00 41 00 00 00 43 48  ......E/..A...CH
000012E0   00 00 00 00 00 03 00 00 00 00 46 2F 00 00 41 00  ..........F/..A.
000012F0   00 00 43 48 00 00 00 00 00 03 00 00 00 00 46 32  ..CH..........F2
00001300   20 00 41 00 00 00 43 48 00 00 00 00 00 03 00 00   .A...CH........
00001310   00 00 46 80 20 00 41 00 00 00 43 48 00 00 00 00  ..F .A...CH....
00001320   00 03 00 00 00 00 46 87 F0 00 41 00 00 00 43 48  ......Fð.A...CH
00001330   00 00                                            ..
```

#### Observations & Section Analysis
- The main data section contains more varied, non-zero content, with repeating patterns and some recognizable field boundaries.
- The structure suggests a sequence of entries, each with a header and associated data fields.
- Some fields appear to be signed/unsigned integers, others may be packed floats or pointers.
- The repeating patterns (e.g., `00 01 00 0E 58 ...`, `00 0C 00 0C ...`) may correspond to FDA/MPA or other data entries as defined in the VSM and code.
- The presence of recognizable ASCII (e.g., `CH`, `A`, etc.) at the end may indicate section delimiters or metadata.

#### Updated Structure
- **0x3E0–end:** Main data section, likely FDA/MPA entries, with each entry containing multiple fields.
- Entry boundaries and field types require further mapping to NetCDF and VSM definitions.

#### Next Steps
- Map the first few entries in this section to the expected FDA/MPA fields using the code and VSM.
- Compare the binary values to the NetCDF input for confirmation.
- Document any confirmed mappings, discrepancies, or new patterns.

#### Field-by-Field Mapping: First FDA Entry

**Expected FDA Field Order (from VSM):**
1. dwell_position_alpha (float)
2. dwell_position_epsilon (float)
3. fda_mp_pointer_alpha (int/pointer)
4. fda_mp_pointer_epsilon (int/pointer)

**NetCDF Values (scan_law_id=16385, entry 0):**
- dwell_position_alpha: 0.0 (scaled x1000: 0.0, packed float32 hex: 00000000)
- dwell_position_epsilon: 0.0 (scaled x1000: 0.0, packed float32 hex: 00000000)
- fda_mp_pointer_alpha: 12 (int: 12, hex: 0000000c)
- fda_mp_pointer_epsilon: 12 (int: 12, hex: 0000000c)

**Binary Data (offset 0x3E0):**
- 00 00 01 00 0E 58 00 00 00 00 00 00 00 00 00 0C

**Direct Comparison:**
- Expected packed values for the first entry (from NetCDF):
  - 00000000 (alpha, float32)
  - 00000000 (epsilon, float32)
  - 0000000c (pointer alpha, int)
  - 0000000c (pointer epsilon, int)
- Binary data does not match the expected packed values for the first entry. The binary contains unexpected values (e.g., 00 00 01 00, 0E 58 00 00) instead of the expected zeros and pointer values.

**Conclusion:**
- There is a mismatch between the expected NetCDF-derived values and the actual binary data for the first FDA entry.
- The mapping logic or packing order may be incorrect, or there may be an offset or additional header in the binary.
- Further investigation is needed to align the binary generation logic with the NetCDF input and VSM mapping.

---

_This document should be updated as new findings emerge and steps are completed._ 