# Standard library imports
import datetime
import os
from typing import Dict, Any
import pandas as pd
import copy

import io
from src.utils.data_cache import cache
import netCDF4 as nc

# Local imports
from src.utils.import_utils import config
from src.logger_wrapper import logger
from src.utils.netcdf_utils import read_netCDF, get_netcdf_variables_value
from src.utils.excel_utils import Read_Sheet
from src.utils.xml_utils import PAF_Update, PAF_Update_IRS_REPSEQ
from src.utils.activity_params import ActivityParams


def unix_seconds_to_iso(seconds_since_epoch):
    """Convert seconds since 1970-01-01 to ISO format 'YYYY-DDDTHH:MM:SS.sssZ'"""
    base = datetime.datetime(1970, 1, 1)
    dt = base + datetime.timedelta(seconds=float(seconds_since_epoch))
    return dt.strftime("%Y-%jT%H:%M:%S.%f")[:-3] + "Z"


def unix_seconds_to_doy(t_ref_value):
    """Convert t_ref value (seconds since 1970-01-01) to DOY format 'YYYYTDDD'"""
    base = datetime.datetime(1970, 1, 1)
    dt = base + datetime.timedelta(seconds=float(t_ref_value))
    return dt.strftime("%YT%j")


def IRS_SAV_PAF(act_params: ActivityParams, satellite: str, checksum: int = None) -> None:
    """Generate IRS SAV PAF file based on the SAV template."""
    new_dict: Dict[str, Any] = {}
    str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], satellite)

    # Nominal repeat_sequence_ids for LAC_SKIP template
    nominal_ids = [0, 128, 256, 16384, 16512, 16640]
    use_lac_skip = int(act_params.repeat_sequence_id) in nominal_ids
    template_name = "SAV_LAC_SKIP_template.xml" if use_lac_skip else "SAV_PAF_template.xml"
    str_path_template_paf = os.path.join(
        config.CONFIG_VALUES["paf_folder"],
        act_params.instrument,
        template_name
    )

    # Set values specific to the SAV context
    new_dict["ACTTAB_ID"] = act_params.activity_table_id
    new_dict["ACTTAB_checksum"] = get_checksum_for_generated_acttab_tc_stack(act_params, satellite)
    new_dict["REPSEQ_GRID"] = act_params.repeat_sequence_id

    # --- Link REPSEQ fields correctly ---
    # Fetch all relevant arrays
    repeat_sequence_ids = get_netcdf_variables_value('repeat_sequence_id', act_params, 'REPSEQ')
    lac_pointer_index_full = get_netcdf_variables_value('lac_pointer_index', act_params, 'REPSEQ')
    repeat_sequence_lengths = get_netcdf_variables_value('repeat_sequence_length', act_params, 'REPSEQ')

    # Find the index of the selected repeat_sequence_id
    try:
        repeat_sequence_ids_int = [int(x) for x in repeat_sequence_ids]
        idx = repeat_sequence_ids_int.index(int(act_params.repeat_sequence_id))
    except ValueError:
        logger.error(f"repeat_sequence_id {act_params.repeat_sequence_id} not found in REPSEQ file.")
        raise

    # Get the correct LAC list and length
    selected_lac_list = lac_pointer_index_full[idx]
    selected_length = repeat_sequence_lengths[idx]
    if selected_length > len(selected_lac_list):
        logger.error(f"repeat_sequence_length ({selected_length}) is greater than the number of LACs ({len(selected_lac_list)}) for repeat_sequence_id {act_params.repeat_sequence_id}.")
        raise ValueError("repeat_sequence_length is greater than the number of LACs in the selected list.")

    # Only use the first 'selected_length' elements
    for int_step in range(selected_length):
        int_lac = selected_lac_list[int_step]
        if int_lac > 0:
            new_dict[f"REPSEQ_RC{int_step+1}"] = int_lac
            new_dict[f"PART_ID_{int_step+1}"] = int_lac

    # --- Add <date> field for all SAV PAFs ---
    # Find the correct MTS1_IRS_REPSEQ_SAV_*.nc file

    input_folder = config.CONFIG_INSTRUMENT[act_params.instrument]["input_folder"]
    pattern = os.path.join(input_folder, "MTS1_IRS_REPSEQ_SAV_")
    # Filter cache keys for matching files
    files = [k for k in cache.keys() if os.path.basename(k).startswith("MTS1_IRS_REPSEQ_SAV_") and k.endswith('.nc')]
    t_ref_value = None
    if files:
        for file in files:
            file_bytes = cache.get(file)
            if file_bytes is None:
                continue
            with nc.Dataset('inmemory.nc', mode='r', memory=file_bytes) as ds:
                repseq_ids = ds.variables["repeat_sequence_id"][:]
                t_refs = ds.variables["t_ref"][:]
                # Find the index where repeat_sequence_id matches
                for i, repseq_id in enumerate(repseq_ids):
                    if int(repseq_id) == int(act_params.repeat_sequence_id):
                        t_ref_value = t_refs[i]
                        break
                if t_ref_value is not None:
                    break
    if t_ref_value is not None:
        logger.info(f"IRS_SAV_PAF: Found t_ref for repeat_sequence_id {act_params.repeat_sequence_id}: {t_ref_value}")
        new_dict["T_SAV_RS"] = unix_seconds_to_iso(t_ref_value)

        # Add day-of-year format conversion
        base = datetime.datetime(1970, 1, 1)
        dt = base + datetime.timedelta(seconds=float(t_ref_value))
        new_dict["T_SAV_RS_DOY"] = dt.strftime("%Y-%j")  # YYYY-DDD format

        logger.info(f"IRS_SAV_PAF: Converted t_ref to ISO: {new_dict['T_SAV_RS']}")
        logger.info(f"IRS_SAV_PAF: Converted t_ref to DOY: {new_dict['T_SAV_RS_DOY']}")
    else:
        logger.warning(f"Could not find t_ref for repeat_sequence_id {act_params.repeat_sequence_id} in any MTS1_IRS_REPSEQ_SAV_*.nc file.")
        new_dict["T_SAV_RS"] = "1970-001T00:00:00.000Z"  # fallback
        new_dict["T_SAV_RS_DOY"] = "1970-001"  # fallback

    
    
 
    # Get appropriate slicer value
    slicer_1 = new_dict.get("REPSEQ_GRID", getattr(act_params, act_params.get_slicer_name(), 0) or 0)
    
    # Update PAF file using the dedicated IRS REPSEQ function
    PAF_Update_IRS_REPSEQ(
        act_params.instrument,
        str_path_template_paf,
        new_dict,
        str_output_folder,
        satellite,
        str(slicer_1),
        act_params.test_mode
    )


def IRS_IWP_PAF(act_params: ActivityParams, satellite: str) -> None:
    """Generate IRS IWP PAF file based on the IWP template."""
    logger.info(f"Generating IWP PAF for satellite {satellite}, slot {act_params.activity_table_slot}")
    _generate_single_iwp_paf(act_params, satellite)


def _generate_single_iwp_paf(act_params: ActivityParams, satellite: str) -> None:
    """Generate a single IRS IWP PAF file for a specific slot."""
    NUM_LACS = 24  # Maximum number of LACs in IWP PAF
    logger.debug(f"Starting single IWP PAF generation for satellite {satellite}, slot {act_params.activity_table_slot}")

    new_dict: Dict[str, Any] = {}
    str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
    
    # Use IWP template
    str_path_template_paf = os.path.join(
        config.CONFIG_VALUES["paf_folder"],
        act_params.instrument,
        "IWP_PAF_template.xml"
    )

    # --- Activity Table Parameters ---
    new_dict["ACTTAB_ID"] = getattr(act_params, 'repeat_sequence_id', 49200)
    new_dict["ACTTAB_checksum"] = get_checksum_for_generated_acttab_tc_stack(act_params, satellite)
    new_dict["ACTTAB_SLOT"] = getattr(act_params, 'activity_table_slot', 2)

    # --- Repeat Sequence Parameters ---
    if hasattr(act_params, 'repeat_sequence_id') and act_params.repeat_sequence_id:
        new_dict["REPSEQ_GRID"] = act_params.repeat_sequence_id
        
        # Fetch all relevant arrays from REPSEQ NetCDF
        try:
            repeat_sequence_ids = get_netcdf_variables_value('repeat_sequence_id', act_params, 'REPSEQ')
            lac_pointer_index_full = get_netcdf_variables_value('lac_pointer_index', act_params, 'REPSEQ')
            repeat_sequence_lengths = get_netcdf_variables_value('repeat_sequence_length', act_params, 'REPSEQ')
            
            repeat_sequence_ids_int = [int(x) for x in repeat_sequence_ids]
            idx = repeat_sequence_ids_int.index(int(act_params.repeat_sequence_id))
            
            selected_lac_list = lac_pointer_index_full[idx]
            selected_length = repeat_sequence_lengths[idx]

            if selected_length > len(selected_lac_list):
                 logger.warning(f"repeat_sequence_length ({selected_length}) is greater than the number of LACs ({len(selected_lac_list)}) for repeat_sequence_id {act_params.repeat_sequence_id}.")

            if selected_length > NUM_LACS:
                logger.warning(f"The input file contains {selected_length} LAC entries in the REPSEQ. Only the first {NUM_LACS} will be considered.")

            # Populate REPSEQ_RCn and PART_IDn based on the selected repeat sequence
            for int_step in range(min(NUM_LACS, selected_length)):
                int_lac = selected_lac_list[int_step]
                if int_lac > 0:
                    new_dict[f"REPSEQ_RC{int_step+1}"] = int_lac
                    new_dict[f"PART_ID_{int_step+1}"] = int_lac

        except (ValueError, FileNotFoundError, KeyError) as e:
            logger.warning(f"Could not read REPSEQ netCDF to populate IWP PAF. Using defaults. Reason: {e}")
            for int_step in range(NUM_LACS):
                new_dict[f"PART_ID_{int_step+1}"] = 1
                new_dict[f"REPSEQ_RC{int_step+1}"] = 1
    else:
        new_dict["REPSEQ_GRID"] = 49200
        logger.warning("No repeat_sequence_id provided. Using default values for REPSEQ in IWP PAF.")
        for int_step in range(NUM_LACS):
            new_dict[f"PART_ID_{int_step+1}"] = 1
            new_dict[f"REPSEQ_RC{int_step+1}"] = 1

    if hasattr(act_params, 'repeat_sequence_slot'):
        new_dict["REPSEQ_SLOT"] = act_params.repeat_sequence_slot
    else:
        new_dict["REPSEQ_SLOT"] = 2  # Default from template

    # --- Scan Law Parameters ---
    if hasattr(act_params, 'scan_law_id') and act_params.scan_law_id:
        new_dict["SL_ID"] = act_params.scan_law_id
        # Attempt to read scan law length from NetCDF
        try:
            dict_netCDF_scanlaw = read_netCDF(
                act_params=act_params,
                satellite=satellite,
                main_files=["SSCANLAW"], used_files=["SSCANLAW"],
                instrument_conf=config.CONFIG_INSTRUMENT[act_params.instrument]
            )
            if "scan_law_length" in dict_netCDF_scanlaw:
                new_dict["SL_Length"] = dict_netCDF_scanlaw["scan_law_length"]
                logger.info(f"Using SL_Length from netCDF: {new_dict['SL_Length']}")
            else:
                new_dict["SL_Length"] = 1036  # Default from template
                logger.warning("scan_law_length not found in SCANLAW netCDF, using default.")
        except (FileNotFoundError, KeyError) as e:
            new_dict["SL_Length"] = 1036  # Default from template
            logger.warning(f"Could not read SCANLAW netCDF: {e}. Using default SL_Length.")
    else:
        new_dict["SL_Length"] = 1036
        new_dict["SL_ID"] = 49200
        logger.warning("No scan_law_id provided, using defaults for SL in IWP PAF.")

    # --- Other Parameters ---
    new_dict["MS_SLOT"] = getattr(act_params, 'mission_scenario_slot', 4)

    if hasattr(act_params, 'scae_id') and act_params.scae_id:
        new_dict["SCAE_SLOT"] = act_params.scae_id
    elif hasattr(act_params, 'scan_law_id') and act_params.scan_law_id:
        new_dict["SCAE_SLOT"] = act_params.scan_law_id  # Use scan_law_id as fallback
    else:
        new_dict["SCAE_SLOT"] = 2  # Default value from template

    # Defensive: Ensure all PART_ID_x are valid (1-4)
    for i in range(1, 25):
        key = f"PART_ID_{i}"
        if key in new_dict:
            try:
                val = int(new_dict[key])
                if val not in [1, 2, 3, 4]:
                    logger.warning(f"{key} value {val} is not valid, setting to 1")
                    new_dict[key] = 1
            except Exception:
                logger.warning(f"{key} value {new_dict[key]} is not an int, setting to 1")
                new_dict[key] = 1

    # --- Generate PAF ---
    slicer_1 = (getattr(act_params, 'scan_law_id', None) or 
                getattr(act_params, 'activity_table_id', None) or 
                getattr(act_params, 'repeat_sequence_id', None) or 
                49200)
    
    # Add slot to slicer to make filename unique
    slicer_for_filename = f"{slicer_1}_SLOT{new_dict['ACTTAB_SLOT']}"
    logger.info(f"Slicer for IWP PAF: {slicer_for_filename}")
    
    PAF_Update(
        str_path_template_paf,
        new_dict,
        str_output_folder,
        satellite,
        "",  # fee_id
        act_params.instrument,
        slicer_for_filename,
        "",  # Which_MM_ID
        "",  # channel
        [],   # calibrations
        act_params.test_mode,
        "IWP"
    )


def get_checksum_for_generated_acttab_tc_stack(
    act_params: ActivityParams,
    satellite: str
) -> int:
    """Get checksum for generated ACTTAB TC stack."""
    try:
        # Get the checksum from SSF file
        tc_file_pattern = f"MTS{satellite.split(' ')[1]}_ACTTAB_ID{str(act_params.activity_table_id).zfill(4)}_SLOT{str(act_params.activity_table_slot).zfill(1)}_"
        output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
        # Only check files in cache if they are in the output folder (if output files are cached)
        tc_files = [k for k in cache.keys() if os.path.dirname(k) == output_folder and os.path.basename(k).startswith(tc_file_pattern) and k.endswith('.ssf')]
        if not tc_files:
            # Fallback to disk if not found in cache (output files may not be cached)
            import glob
            tc_files = glob.glob(os.path.join(output_folder, tc_file_pattern + '*.ssf'))
            if not tc_files:
                logger.warning(f"No ACTTAB TC stack file found with pattern: {tc_file_pattern}")
                return 0
            tc_file = tc_files[0]
            logger.info(f"Reading checksum from disk: {tc_file}")
            checksum = None
            with open(tc_file, 'r') as f:
                for line in f:
                    if "DSWH0AYX" in line:
                        checksum = int(line.split("|")[5])
                        break
            if checksum is not None:
                logger.info(f"Found ACTTAB checksum: {checksum}")
                return checksum
            else:
                logger.warning("DSWH0AYX line not found in ACTTAB TC stack file")
                return 0
        else:
            tc_file = tc_files[0]
            logger.info(f"Reading checksum from cache: {tc_file}")
            checksum = None
            file_bytes = cache.get(tc_file)
            if file_bytes is not None:
                for line in io.BytesIO(file_bytes).readlines():
                    line_str = line.decode('utf-8')
                    if "DSWH0AYX" in line_str:
                        checksum = int(line_str.split("|")[5])
                        break
            if checksum is not None:
                logger.info(f"Found ACTTAB checksum: {checksum}")
                return checksum
            else:
                logger.warning("DSWH0AYX line not found in ACTTAB TC stack file (cache)")
                return 0
    except Exception as e:
        logger.error(f"Error reading ACTTAB checksum: {e}")
        return 0
