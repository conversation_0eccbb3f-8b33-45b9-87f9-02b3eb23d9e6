"""
LI_functions package initialization.
This package contains functions for handling Lightning Imager (LI) related operations.
"""

# Import sub-modules so they are part of the package when accessed via the package name.
from . import LI_functions
from . import LI_launcher
from . import basic_conf_maps
from . import location_utils

# Consumers should explicitly import what they need from these sub-modules, for example:
# from src.algorithms.li_functions.LI_functions import LI_BASIC_CONF
# from src.algorithms.li_functions.LI_launcher import li_function_launcher
# from src.algorithms.li_functions.basic_conf_maps import some_specific_map
# from src.algorithms.li_functions.location_utils import some_location_util

# Avoid using "from .basic_conf_maps import *" or "from .location_utils import *"
# here to prevent unintended side effects or exacerbating circular import issues.

