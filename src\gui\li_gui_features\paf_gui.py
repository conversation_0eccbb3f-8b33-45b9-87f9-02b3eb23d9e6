from tkinter import StringVar
from tkinter import ttk

from ..frames import <PERSON><PERSON>rame
from ..theme_manager import ThemeManager


class FrameICID(BaseFrame):
    def __init__(self, parent):
        super().__init__(parent)

        self.input_icid = StringVar()
        self.input_icid_ver = StringVar()
        self.lst_variables_paf = []

        self._create_widgets()
        self._grid_widgets()

    def _create_widgets(self):
        self.icid_frame = ttk.LabelFrame(self, text="ICID")
        self.icid_ver_frame = ttk.LabelFrame(self, text="ICID Version")
        self.paf_frame = ttk.LabelFrame(self, text="PAF")

        self.entry_icid = ttk.Entry(self.icid_frame, textvariable=self.input_icid)
        self.entry_icid_ver = ttk.Entry(self.icid_ver_frame, textvariable=self.input_icid_ver)

        self.btn_add_paf = ttk.Button(self.paf_frame, text="Add PAF", command=self._add_paf)

    def _grid_widgets(self):
        self.icid_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        self.icid_ver_frame.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        self.paf_frame.grid(row=2, column=0, padx=5, pady=5, sticky="ew")

        self.entry_icid.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        self.entry_icid_ver.grid(row=1, column=1, padx=5, pady=5, sticky="ew")

    def _create_paf_selection(self, parent):
        paf_content = ttk.Frame(parent)
        paf_content.grid_rowconfigure(0, weight=1)
        paf_content.grid_columnconfigure(0, weight=1)

        for row, (paf_name, var) in enumerate(self.lst_variables_paf):
            paf_cb = ttk.Checkbutton(paf_content, text=paf_name, variable=var)
            paf_cb.grid(row=row, column=0, sticky="w", padx=5, pady=2)
            self.lst_variables_paf.append(var)

        return paf_content

    def _add_paf(self):
        pass

    def create_widgets(self):
        """Create and layout GUI components using grid."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Main frame using grid
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure grid weights for rows and columns in the main frame
        main_frame.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # ICID frame
        self.icid_frame = ttk.LabelFrame(main_frame, text="ICID")
        self.icid_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        # ICID Version frame
        self.icid_ver_frame = ttk.LabelFrame(main_frame, text="ICID Version")
        self.icid_ver_frame.grid(row=1, column=0, padx=5, pady=5, sticky="ew")

        # PAF frame
        self.paf_frame = ttk.LabelFrame(main_frame, text="PAF")
        self.paf_frame.grid(row=2, column=0, padx=5, pady=5, sticky="ew")

        # ICID Entry
        ttk.Entry(self.icid_frame, textvariable=self.input_icid).grid(row=0, column=1, padx=5, pady=5, sticky="ew")

        # ICID Version Entry
        ttk.Entry(self.icid_ver_frame, textvariable=self.input_icid_ver).grid(row=1, column=1, padx=5, pady=5, sticky="ew")

        # Add PAF button
        self.btn_add_paf = ttk.Button(self.paf_frame, text="Add PAF", command=self._add_paf)
        self.btn_add_paf.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        # Configure grid weights for PAF selection
        self.paf_frame.grid_rowconfigure(0, weight=1)
        self.paf_frame.grid_columnconfigure(0, weight=1)
