import os
import pytest
from src.utils.netcdf_utils import read_netCDF
from src.utils.import_utils import config


@pytest.fixture
def excel_test_files():
    base_path = os.path.normpath(os.path.join(config.CONFIG_VALUES['test_files'], "IRS\Input"))
    return {
        "test_file": os.path.normpath(os.path.join(base_path, "MTS1_IRS_ACTAB_CDC_test_Feburary_2024.nc"))
    }


@pytest.fixture(autouse=True)
def test_read_netcdf(excel_test_files):
    # Assuming a test netCDF file exists at a known path
    data = read_netCDF([excel_test_files["test_file"]], ['some_variable'], {}, 0, 0, 'IRS', 'MTG-I 1')
    assert data is not None
    assert 'some_variable' in data
