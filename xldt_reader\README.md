# XLDT Reader Package

A comprehensive XLDT (Transfer Layer Data) file reader for MTG satellite operations.

## 📁 Directory Structure

```
xldt_reader/
├── README.md                    # This file
├── XLDT_Reader_Summary.md       # Detailed implementation summary
├── xldt_reader_standalone.py    # Standalone XLDT reader (recommended)
├── xldt_cli_standalone.py       # Command-line interface
├── xldt_gui.py                  # Graphical user interface (GUI)
├── launch_gui.bat               # GUI launcher for Windows
├── demo.py                      # Interactive demonstration script
│
├── src/                         # Integrated versions
│   ├── xldt_reader.py          # Main XLDT reader (for CDC-S integration)
│   └── xldt_cli.py             # CLI for integrated version
│
├── docs/                        # Documentation
│   └── XLDT_Reader_Guide.md    # Comprehensive user guide
│
├── examples/                    # Usage examples
│   └── xldt_reader_example.py  # Example scripts
│
├── tests/                       # Test suite
│   └── test_xldt_reader.py     # Unit tests
│
├── config/                      # Configuration files
│   ├── IRS/
│   │   └── IRS_SL_Conf.csv     # IRS instrument configuration
│   └── FCI/
│       └── FCI_SL_Conf.csv     # FCI instrument configuration
│
└── inputs/                      # Sample XLDT files
    ├── ScanLaw_Central_Summer_16384.xldt
    ├── ScanLaw_Central_Winter_0.xldt
    ├── ScanLaw_East_Summer_16386.xldt
    ├── ScanLaw_West_Summer_16385.xldt
    └── ScanLaw_West_Winter_1.xldt
```

## 🚀 Quick Start

### 1. GUI Application (Easiest)

Launch the graphical user interface for easy file analysis:

```bash
# Start the GUI
python xldt_gui.py

# Or use the launcher (Windows)
launch_gui.bat
```

**GUI Features:**
- 📁 Easy file browsing and selection
- ⚙️ Quick configuration switching (IRS/FCI)
- 📊 Multi-tab results display (Summary, Sections, Raw Data, JSON)
- 🔍 CRC validation with visual feedback
- 📤 Export capabilities (text, JSON)
- 🔄 Batch processing for multiple files
- 📋 Sample file creation

### 2. Basic Usage (Command Line)
```bash
# Navigate to xldt_reader directory
cd xldt_reader

# Read any XLDT file
python xldt_reader_standalone.py inputs/ScanLaw_Central_Summer_16384.xldt

# Use with configuration for detailed parsing
python xldt_cli_standalone.py inputs/ScanLaw_Central_Summer_16384.xldt --config config/IRS/IRS_SL_Conf.csv

# Get detailed analysis
python xldt_cli_standalone.py inputs/ScanLaw_Central_Summer_16384.xldt --config config/IRS/IRS_SL_Conf.csv --format detailed
```

### 3. Create and Test Sample Files
```bash
# Create a sample XLDT file
python xldt_cli_standalone.py --create-sample test_sample.bin

# Read it back
python xldt_cli_standalone.py test_sample.bin --config config/IRS/IRS_SL_Conf.csv --format detailed
```

### 4. Python API Usage
```python
from xldt_reader_standalone import XLDTReader

# Initialize with configuration
reader = XLDTReader(config_path="config/IRS/IRS_SL_Conf.csv")

# Read XLDT file
data = reader.read_xldt_file("inputs/ScanLaw_Central_Summer_16384.xldt")

# Access parsed data
print(f"Format ID: {data['header'].format_id}")
print(f"MM Slot: {data['header'].mm_slot}")
print(f"Sections: {list(data['sections'].keys())}")
```

## 📊 XLDT File Structure

XLDT files contain satellite scan law data with this structure:

```
┌─────────────────┐
│   XLDT Header   │  4 bytes: Format ID + MM Slot
├─────────────────┤
│      CRC        │  2 bytes: Checksum
├─────────────────┤
│     Header      │  4 bytes: MSDF_ID + metadata
├─────────────────┤
│  LAC_Pointer    │  20 bytes: LAC pointer data
├─────────────────┤
│    Retrace      │  40 bytes: Retrace information
├─────────────────┤
│     Rally       │  40 bytes: Rally data
├─────────────────┤
│      FDA        │  1680 bytes: FDA section
├─────────────────┤
│      MPA        │  4160 bytes: MPA section
└─────────────────┘
```

## 🔧 Available Tools

### Standalone Tools (Recommended)
- **`xldt_gui.py`** - Graphical user interface (easiest to use)
- **`xldt_reader_standalone.py`** - Core reader, no dependencies
- **`xldt_cli_standalone.py`** - Command-line interface
- **`demo.py`** - Interactive demonstration script

### Integrated Tools (For CDC-S codebase)
- **`src/xldt_reader.py`** - Integrated with CDC-S imports
- **`src/xldt_cli.py`** - CLI for integrated version

## 📖 Documentation

- **`README.md`** - This quick start guide
- **`XLDT_Reader_Summary.md`** - Detailed implementation summary
- **`docs/XLDT_Reader_Guide.md`** - Comprehensive user guide

## 🧪 Testing

```bash
# Run tests
cd tests
python test_xldt_reader.py

# Test with real files
python ../xldt_cli_standalone.py ../inputs/ScanLaw_Central_Summer_16384.xldt --config ../config/IRS/IRS_SL_Conf.csv --validate-crc
```

## ⚙️ Configuration

The reader supports two instrument configurations:

- **IRS (Infrared Sounder)**: `config/IRS/IRS_SL_Conf.csv`
- **FCI (Flexible Combined Imager)**: `config/FCI/FCI_SL_Conf.csv`

Configuration files define:
- Section names and order
- Section lengths in bytes
- MSDF IDs for each section

## 📁 Sample Files

The `inputs/` directory contains real XLDT files:
- **Central scan laws**: Summer and Winter variants
- **East/West scan laws**: Different geographical configurations
- **Multiple MM slots**: Different satellite configurations

## 🔍 Output Formats

### Summary Format
```
XLDT File Summary
==================================================
File: ScanLaw_Central_Summer_16384.xldt
Size: 6000 bytes
Format ID: 1
MM Slot: 16384
CRC Status: VALID

Sections (7):
------------------------------
  CRC: 2 bytes
  Header: 4 bytes
  LAC_Pointer: 20 bytes
  ...
```

### Detailed Format
```
XLDT File Detailed Analysis
==================================================
File Information:
  file_path: inputs/ScanLaw_Central_Summer_16384.xldt
  file_size: 6000
  hex_length: 12000

XLDT Header:
  Format ID: 1 (0x0001)
  MM Slot: 16384 (0x4000)

Sections (7):
----------------------------------------
CRC:
  Length: 2 bytes
  Order: 2.0
  Data: A1B2

Header:
  Length: 4 bytes
  Order: 3.0
  MSDF ID: 0.0
  Data: 00123456
...
```

### JSON Format
```json
{
  "file_info": {
    "file_path": "inputs/ScanLaw_Central_Summer_16384.xldt",
    "file_size": 6000,
    "hex_length": 12000
  },
  "header": {
    "format_id": 1,
    "mm_slot": 16384
  },
  "sections": {
    "CRC": {
      "length": 2,
      "order": 2.0,
      "data_hex": "A1B2"
    }
  }
}
```

## 🎯 Use Cases

1. **Scan Law Analysis**: Parse scan law files for satellite operations
2. **Data Validation**: Verify CRC checksums and file integrity
3. **Batch Processing**: Analyze multiple XLDT files
4. **Integration**: Add XLDT reading to existing workflows
5. **Debugging**: Inspect binary file structure and contents

## 🔗 Integration with CDC-S

The XLDT reader integrates seamlessly with your CDC-S codebase:
- Uses existing configuration files
- Compatible with `create_XLDT()` function
- Follows CDC-S coding patterns
- Supports both IRS and FCI instruments

## 📞 Support

For questions or issues:
1. Check the documentation in `docs/`
2. Review examples in `examples/`
3. Run tests to verify functionality
4. Use verbose mode (`-v`) for debugging

## 🏁 Next Steps

1. **Test with your files**: Try the reader with your specific XLDT files
2. **Integrate into workflows**: Add XLDT reading to your processing pipelines
3. **Extend functionality**: Customize for your specific needs
4. **Batch processing**: Use CLI for analyzing multiple files

The XLDT reader is ready to help you analyze satellite scan law data!
