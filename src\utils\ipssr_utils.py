"""IPSSR file downloading and management utilities."""

# Standard library imports
import json
import ssl
from typing import List, Dict, Callable, Any # Added Callable, Any
from urllib import request
from datetime import datetime
import shutil # Added import
import stat # Added import for file permissions

# Local imports
from src.utils.import_utils import logger, basics, os, config
from src.utils.activity_params import ActivityParams

# Global variable to store last download time
_last_download_time: str = "Never"

def _handle_rmtree_error(func: Callable[..., Any], path: str, exc_info: Any) -> None:
    """Error handler for shutil.rmtree.

    If the error is due to a read-only file, it attempts to change its permissions
    and then re-attempt the deletion.
    """
    # Check if the error is an access error (PermissionError)
    # exc_info is a tuple (type, value, traceback)
    exc_type, exc_value, _ = exc_info
    logger.warning(f"Error removing {path} during rmtree: {exc_value} (Function: {func.__name__})")
    if exc_type is PermissionError and not os.access(path, os.W_OK):
        # Try to make the file writable
        logger.info(f"Attempting to change permissions for {path} to writable.")
        try:
            os.chmod(path, stat.S_IWUSR | stat.S_IWGRP | stat.S_IWOTH) # Add write permission for user/group/others
            func(path) # Retry the operation (e.g., os.remove or os.rmdir)
            logger.info(f"Successfully removed {path} after changing permissions.")
        except Exception as e:
            logger.error(f"Still failed to remove {path} after attempting to change permissions: {e}")
            # Re-raise the original exception if we can't handle it
            # Or, if you want to suppress it, just pass
            # For now, let's log and let rmtree continue if it can, or fail if it must.
            # If func was os.remove, and it failed again, rmtree will raise the error from the failed func call.
    # If it's not a permission error we can handle, or if changing permissions didn't work,
    # rmtree will typically raise the original error from the failed func call.

def get_last_download_time() -> str:
    """Get the last IPSSR download time.
    
    Returns:
        str: Formatted date/time of last download or 'Never' if no download
    """
    return _last_download_time

def get_satellite_ids() -> Dict[str, List[str]]:
    """Get all satellite IDs for both MTI and MTS families.
    
    Returns:
        Dict with satellite families as keys and list of IDs as values
    """
    return {
        "MTI": ["MTI1"], # "MTI2", "MTI3", "MTI4"],  # For LI and FCI
        "MTS": ["MTS1"]  # "MTS2"]   # For IRS and UVN
    }


def save_from_IPSSR(
    satellite: str,
    str_bundle: str,
    str_path_output: str,
    str_IPSSR_url: str
) -> bool:
    """Download netcdf files from IPSSR with information about the instrument.
    
    Args:
        satellite: Satellite identifier
        str_bundle: Bundle name to download
        str_path_output: Output path for downloaded files
        str_IPSSR_url: Base URL for IPSSR server
        
    Returns:
        bool: True if download successful, False otherwise
        
    Note:
        The information is wrapped in Bundles.
        Each bundle consists of a list of Shares.
    """
    str_url = f"{str_IPSSR_url}/api/bundles/{str_bundle}"
    obj_context = ssl._create_unverified_context()

    try:
        json_data = json.load(request.urlopen(str_url, context=obj_context))
        logger.info(f"Reading Bundle {str_bundle}")
    except Exception as error:
        logger.error(repr(error))
        logger.error(f"Requested Bundle {str_bundle} or requested url {str_url} does not exist")
        return False

    for str_share in json_data["shares"]:
        logger.info(f"Downloading share {str_share}")
        str_url = f"{str_IPSSR_url}/api/resources/{str_share}"
        obj_response = request.urlopen(str_url, context=obj_context)
        str_filename = obj_response.info().get_filename()

        if satellite not in str_filename:
            str_filename = f"{satellite}_{str_share}_{str_filename}"
        else:
            str_filename = f"{str_share}_{str_filename}"

        str_full_path = os.path.join(str_path_output, str_filename)
        with open(str_full_path, "w+b") as file_file:
            file_file.write(obj_response.read())
        logger.info(f"Saving IPSSR share {str_share} file {str_full_path}")

    global _last_download_time
    _last_download_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return True


def download_ipssr_for_instrument(instrument_name: str, base_input_path: str) -> None:
    """Download IPSSR files for a specific instrument across all its applicable satellites.
    Existing files for the instrument will be deleted before new download.

    Args:
        instrument_name: The name of the instrument (e.g., "FCI", "LI", "IRS", "UVN").
        base_input_path: The base path where the instrument's IPSSR files should be stored.
                         A subdirectory for the instrument will be created here.
    """
    logger.info(f"Downloading IPSSR info for instrument: {instrument_name}")
    instrument_path = os.path.join(base_input_path, instrument_name)

    # Delete existing instrument directory if it exists
    if os.path.exists(instrument_path):
        logger.info(f"Deleting existing directory: {instrument_path}")
        try:
            shutil.rmtree(instrument_path, onerror=_handle_rmtree_error)
        except PermissionError as e:
            logger.error(
                f"Failed to delete directory {instrument_path} due to a PermissionError: {e}. "
                "This can happen if files within the directory are in use by another program. "
                "Please ensure no files in this directory are open and try again."
            )
            # Optionally, decide if to return or re-raise.
            # For now, we'll let it attempt to proceed to os.makedirs.
        except Exception as e:
            # Catch other potential errors from rmtree (e.g., if _handle_rmtree_error re-raises something else)
            logger.error(f"An unexpected error occurred while deleting directory {instrument_path}: {e}")
            # Decide whether to return or re-raise
    
    # Ensure the directory exists for downloading files.
    # Check if path exists and is not a directory (e.g. a file with same name)
    if os.path.exists(instrument_path) and not os.path.isdir(instrument_path):
        logger.error(f"Path {instrument_path} exists but is not a directory. Cannot download IPSSR data for {instrument_name}.")
        return # Stop if the path is unsuitable
    
    os.makedirs(instrument_path, exist_ok=True) # exist_ok=True handles if dir exists or was partially deleted

    satellite_ids_map = get_satellite_ids()
    ipssr_url = config.CONFIG_VALUES["ipssr_url"]

    if instrument_name in ["FCI", "LI"]:
        satellite_family_ids = satellite_ids_map["MTI"]
    elif instrument_name in ["IRS", "UVN"]:
        satellite_family_ids = satellite_ids_map["MTS"]
    else:
        logger.error(f"Unknown instrument: {instrument_name}. Cannot download IPSSR data.")
        return

    for sat_id in satellite_family_ids:
        if instrument_name == "LI":
            li_bundle_prefix = f"{sat_id}_LI"
            for fee_id in range(1, 5):  # FEE IDs 1-4
                save_from_IPSSR(
                    sat_id,
                    f"{li_bundle_prefix}_OC{fee_id}",
                    instrument_path,
                    ipssr_url
                )
        else:
            bundle_name = f"{sat_id}_{instrument_name}"
            save_from_IPSSR(
                sat_id,
                bundle_name,
                instrument_path,
                ipssr_url
            )
    logger.info(f"Finished downloading IPSSR info for instrument: {instrument_name}")
