#!/usr/bin/env python3
"""
Test the updated JSON export with NetCDF data.
"""

import os
import sys
import json
from xldt_reader_standalone import X<PERSON><PERSON><PERSON>er

def test_json_export():
    """Test JSON export with NetCDF data."""
    
    print("🔍 Testing JSON Export with NetCDF Data")
    print("=" * 50)
    
    xldt_file = "inputs/ScanLaw_East_Summer_16386.xldt"
    config_path = "../config/IRS/IRS_SL_Conf.csv"
    
    if not os.path.exists(xldt_file):
        print(f"❌ XLDT file not found: {xldt_file}")
        return
    
    try:
        reader = XLDTReader(config_path=config_path)
        parsed_data = reader.read_xldt_file(xldt_file)
        
        # Export to dictionary
        json_data = reader.export_to_dict(parsed_data)
        
        # Check FDA section
        if 'FDA' in json_data['sections']:
            fda_section = json_data['sections']['FDA']
            print(f"✅ FDA section found in JSON export")
            print(f"   Data source: {fda_section.get('data_source', 'Unknown')}")
            print(f"   Scan law ID: {fda_section.get('scan_law_id', 'Unknown')}")
            
            # Check FDA pointer data
            if 'fda_mp_pointer_alpha' in fda_section:
                alpha_data = fda_section['fda_mp_pointer_alpha']
                print(f"   FDA Alpha found: {alpha_data['count']} values")
                print(f"   First 10 values: {alpha_data['values'][:10]}")
                
                # Check if it matches expected pattern
                expected = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]
                actual = [int(x) for x in alpha_data['values'][:10]]
                if actual == expected:
                    print("   ✅ FDA Alpha matches expected pattern!")
                else:
                    print(f"   ❌ FDA Alpha mismatch. Expected: {expected}, Got: {actual}")
            
            if 'fda_mp_pointer_epsilon' in fda_section:
                epsilon_data = fda_section['fda_mp_pointer_epsilon']
                print(f"   FDA Epsilon found: {epsilon_data['count']} values")
                print(f"   First 10 values: {epsilon_data['values'][:10]}")
        
        # Save JSON to file for inspection
        output_file = "test_json_output.json"
        with open(output_file, 'w') as f:
            json.dump(json_data, f, indent=2, separators=(',', ': '))
        
        print(f"\n💾 JSON exported to: {output_file}")
        
        # Show a snippet of the FDA section
        if 'FDA' in json_data['sections'] and 'fda_mp_pointer_alpha' in json_data['sections']['FDA']:
            print(f"\n📄 FDA Section Preview:")
            fda_alpha = json_data['sections']['FDA']['fda_mp_pointer_alpha']
            print(f'  "fda_mp_pointer_alpha": {{')
            print(f'    "name": "{fda_alpha["name"]}",')
            print(f'    "long_name": "{fda_alpha["long_name"]}",')
            print(f'    "data_type": "{fda_alpha["data_type"]}",')
            print(f'    "values": [{", ".join(map(str, fda_alpha["values"][:20]))}...]')
            print(f'  }}')
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_json_export()
