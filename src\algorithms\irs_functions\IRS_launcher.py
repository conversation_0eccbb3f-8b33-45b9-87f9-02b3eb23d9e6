# Standard library imports
import os
from typing import List
import glob
import copy
# Local imports
from src.utils.import_utils import config, basics
from src.utils.activity_params import ActivityParams
from src.utils.excel_utils import Read_Sheet
from src.utils.netcdf_utils import get_netcdf_variables_value
from src.logger_wrapper import logger
from .IRS_PAF import IRS_SAV_PAF, IRS_IWP_PAF
from .IRS_SELUT import IRS_SELUT
from .IRS_ACT_TAB import IRS_ACT_TAB
from .IRS_SL import IRS_SL
from .IRS_VCU_MEM_NUMOFF import IRS_VCU_MEM_NUMOFF
from .IRS_VCU_MEM_PIXMAP import IRS_VCU_MEM_PIXMAP


def irs_functions_launcher(act_params: ActivityParams, lst_part: List[str] = None,
                         lst_band: List[str] = None, str_VCU_image_format: str = None) -> None:
    """Launch appropriate IRS functions based on activity parameters."""
    logger.debug(f"IRS_launcher: Starting function launch for activity '{act_params.activity}' with satellites: {act_params.satellites}")
    for satellite in act_params.satellites:
        if "Mission Scenario (SL)" in act_params.activity:
            IRS_SL(act_params, satellite)
        elif "Activity Table" in act_params.activity:
            IRS_ACT_TAB(act_params, satellite)
        elif "Repeat Sequence" in act_params.activity:
            basics.pop_up_message("Not Implemented", "Repeat Sequence: Not having template yet", "debug")
        elif "Scan Encoder LUT" in act_params.activity:
            IRS_SELUT(act_params, satellite)
        elif "VCU Update" in act_params.activity and lst_part and lst_band:
            if "Numerical Offset" in lst_part:
                IRS_VCU_MEM_Launcher(
                    act_params,
                    lst_band,
                    str_VCU_image_format,
                    "VCU"
                )
            if "Pixel Mapping" in lst_part:
                IRS_VCU_MEM_Launcher(
                    act_params,
                    lst_band,
                    str_VCU_image_format,
                    "PIX"
                )
        elif "DPU" in act_params.activity:
            basics.pop_up_message("Not Implemented", "DPU: Nothing implemented yet", "debug")
        elif "SAV" in act_params.activity:
            repseq_ids = determine_repseq_ids_from_acttab_id(act_params, satellite)
            if not repseq_ids:
                logger.error(f"No repeat_sequence_ids found for activity_table_id {act_params.activity_table_id}")
                continue
            act_params.activity_table_slot = 2
            IRS_ACT_TAB(act_params, satellite)
            for repseq_id in repseq_ids:
                act_params.repeat_sequence_id = repseq_id
                IRS_SAV_PAF(act_params, satellite)
        elif "Scan Scenario" in act_params.activity:
            if determine_acttab_id_from_repseq_id(act_params, satellite):
                # Defensive: Set required slot values for IRS_SL
                act_params.activity_table_slot = 1
                act_params.mm_slot = 1
                if act_params.scan_law_id is None:
                    logger.error("scan_law_id is None before calling IRS_SL; please check input.")
                    continue
                IRS_SL(act_params, satellite)
                IRS_SELUT(act_params, satellite)
                for slot in [1, 2]:
                    act_params.activity_table_slot = slot
                    IRS_ACT_TAB(act_params, satellite)
                    IRS_IWP_PAF(act_params, satellite)
            else:
                logger.error("Failed to determine activity table ID from repeat sequence ID for IWP")

    # Handle output commit
    if not act_params.test_mode:
        _handle_output_commit(act_params.satellites)

def IRS_VCU_MEM_Launcher(
    act_params: ActivityParams,
    lst_bands: List[str],
    str_memory: str,
    WhichUpdate: str
) -> None:
    """Handles IRS VCU memory updates
    
    Args:
        act_params: Activity parameters containing configuration and operation details
        lst_bands: List of bands to process
        str_memory: Memory type specification ('Combined Image' or 'Image per Band')
        WhichUpdate: Type of update to perform ('VCU' or 'PIX')
    """
    logger.debug("IRS_VCU_MEM_Launcher")
    global glob_NOF_StartAdd
    global glob_NOF_BlkLen

    # Specify here the files used by the specific section of code
    lst_used_files = ["INSTCONF", "DETCONF"]

    Mem_Map = Read_Sheet(
        os.path.join(
            config.CONFIG_VALUES["config_folder"],
            act_params.instrument,
            "IRS_VCU_CONF_Config.xlsx",
        ),
        "Map",
    )

    # Read the configuration file for the instrument
    dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
    
    for satellite in act_params.satellites:
        glob_NOF_StartAdd = {}
        glob_NOF_BlkLen = {}

        for File in lst_used_files:
            df_VSM = Read_Sheet(dict_config["vsm_path"], File)

        logger.debug(f"Started IRS VCU Memory Image File creation for {satellite}")

        if str_memory == "Combined Image":
            if WhichUpdate == "VCU":
                    IRS_VCU_MEM_NUMOFF(
                        act_params,
                        satellite,
                        lst_used_files,
                        Mem_Map,
                        df_VSM,
                        lst_bands,
                        100  # n_band
                    )
            else:
                    IRS_VCU_MEM_PIXMAP(
                        act_params,
                        satellite,
                        lst_used_files,
                        Mem_Map,
                        df_VSM,
                        lst_bands,
                        200  # n_band
                    )

        elif str_memory == "Image per Band":
            n_band = 1

            for band in lst_bands:
                if WhichUpdate == "VCU":
                    IRS_VCU_MEM_NUMOFF(
                        act_params,
                        satellite,
                        lst_used_files,
                        Mem_Map,
                        df_VSM,
                        [band],
                        n_band + 100  # n_band
                    )
                else:
                    IRS_VCU_MEM_PIXMAP(
                        act_params,
                        satellite,
                        lst_used_files,
                        Mem_Map,
                        df_VSM,
                        [band],
                        n_band + 200  # n_band
                    )
                n_band += 1

    logger.debug("IRS VCU Memory Image File Creation Completed")

def _handle_output_commit(satellites: List[str]) -> None:
    """Handle committing output files to gitlab."""
    bln_commit = basics.pop_up_message(
        "Output Outcome",
        "Execution Completed\nDo you wish to commit the generated output?",
        "askyesno",
    )
    
    if bln_commit:
        for satellite in satellites:
            # Create gitlab for the specific satellite
            str_gitlab_project = (
                config.CONFIG_VALUES[f"gitlab_irs"]
                + satellite[-1]
            )

            # Get files to commit
            lst_files_to_commit = basics.files_in_dir(
                os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
            )

            # Add full paths
            lst_files_to_commit = [
                os.path.join(config.CONFIG_VALUES["output_folder"], satellite, f)
                for f in lst_files_to_commit
            ]

            # Commit files
            basics.commit_to_gitlab(str_gitlab_project, lst_files_to_commit)

        basics.pop_up_message(
            "The_End", 
            "Commit Performed Successfully", 
            "debug"
        )


def determine_acttab_id_from_repseq_id(
    act_params: ActivityParams,
    satellite: str
) -> bool:
    """Determine activity table ID from repeat sequence ID vectors.

    Returns:
        bool: True if mapping was successful, False otherwise
    """
    # Get the full vectors from the REPSEQ file
    repeat_sequence_ids = get_netcdf_variables_value(
        variable_id='repeat_sequence_id',
        act_params=act_params,
        main_file_pattern='REPSEQ'
    )
    activity_table_ids = get_netcdf_variables_value(
        variable_id='activity_table_id',
        act_params=act_params,
        main_file_pattern='REPSEQ'
    )

    if repeat_sequence_ids is None or activity_table_ids is None:
        logger.warning("Could not find required vectors in REPSEQ netCDF data")
        return False

    try:
        # Ensure we have lists to work with
        repeat_sequence_list = repeat_sequence_ids.tolist() if hasattr(repeat_sequence_ids, 'tolist') else list(repeat_sequence_ids)
        all_ids_str = ", ".join(map(str, repeat_sequence_list))

        # Find the index where repeat_sequence_id matches our input
        target_id = int(act_params.repeat_sequence_id)
        logger.info(f"Searching for repeat_sequence_id: {target_id} in list: [{all_ids_str}]")
        idx = repeat_sequence_list.index(target_id)
        act_params.repeat_sequence_index = idx

        # Get the corresponding activity_table_id
        activity_table_id = activity_table_ids[idx]

        logger.info(f"Mapped repeat_sequence_id {target_id} to activity_table_id {activity_table_id}")
        act_params.activity_table_id = activity_table_id
        return True

    except (ValueError, TypeError) as e:
        logger.warning(f"Could not map repeat_sequence_id {act_params.repeat_sequence_id}: {e}")
        return False

def determine_repseq_ids_from_acttab_id(act_params: ActivityParams, satellite: str):
    """Return all repeat_sequence_ids matching the given activity_table_id from REPSEQ NetCDF."""
    repseq_ids = get_netcdf_variables_value('repeat_sequence_id', act_params, 'REPSEQ')
    acttab_ids = get_netcdf_variables_value('activity_table_id', act_params, 'REPSEQ')
    if repseq_ids is None or acttab_ids is None:
        logger.warning("Could not find required vectors in REPSEQ netCDF data")
        return []
    try:
        repseq_ids = list(repseq_ids)
        acttab_ids = list(acttab_ids)
        return [int(repseq) for repseq, acttab in zip(repseq_ids, acttab_ids) if int(acttab) == int(act_params.activity_table_id)]
    except Exception as e:
        logger.warning(f"Error mapping activity_table_id to repeat_sequence_ids: {e}")
        return []