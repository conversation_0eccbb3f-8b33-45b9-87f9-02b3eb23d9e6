import math
import struct
from src.logger_wrapper import logger

def convert_dec_to_bit(val_dec=0.0, len_bit=16, type="integer", flag_hex=False):
    """Function to convert from pdt table to bitstring or hexstring
    :param val_dec: Value of the input decimal value to be converted to bit/hexstring, defaults to 0.0
    :type val_dec: float, optional
    :param len_bit: bit-length of the input decimal value, defaults to 16. Value will be overriden by 32/64 for type='float'/'double' respectively.
    :type len_bit: int, optional
    :param type: type of the input decimal value, defaults to 'integer'
    :type type: enum['integer','unsigned integer','enumerated','signed integer','float','double'], optional
    :param flag_hex: Function will return hexstring if True, else bitstring, defaults to False
    :type flag_hex: bool, optional
    :return: bitstring or hexstring (depending on flag_hex)
    :rtype: str
    """
    if type in ["unsigned integer", "integer", "enumerated"]:
        # if(val_dec<0 or 2**len_bit-1<val_dec):
        if val_dec < 0:
            raise Exception(
                'Cannot convert to integer of length "len_bit":'
                + str(len_bit)
                + ' "val_dec":'
                + str(val_dec)
            )
        elif len_bit < math.log(int(val_dec) + 1) / math.log(2):
            raise Exception(
                'Cannot convert to integer of length "len_bit":'
                + str(len_bit)
                + ' "val_dec":'
                + str(val_dec)
            )
        dat_bit = format(int(val_dec), "b").zfill(len_bit)
        flag_hex_ = False
    elif type in ["signed integer"]:
        # if(val_dec<-2**(len_bit-1) or 2**(len_bit-1)-1<val_dec):
        if int(val_dec) == 0:
            dat_bit = "0" + format(0, "b").zfill(len_bit - 1)
        elif (val_dec < 0 and math.log(abs(val_dec)) / math.log(2) > (len_bit - 1)) or (
            val_dec >= 0 and (len_bit - 1) < math.log(int(val_dec) + 1) / math.log(2)
        ):
            raise Exception(
                'Cannot convert to signed integer of length "len_bit":'
                + str(len_bit)
                + ' "val_dec":'
                + str(val_dec)
            )
        elif val_dec >= 0:
            dat_bit = "0" + format(int(val_dec), "b").zfill(len_bit - 1)
        else:
            dat_bit = "1" + format(int(val_dec) + 2 ** (len_bit - 1), "b").zfill(
                len_bit - 1
            )
        flag_hex_ = False
    elif type in ["float"]:
        len_bit = 32
        # dat_bit=struct.pack('>f',val_dec).encode('hex').zfill(32//4)
        dat_bit = hex(struct.unpack(">I", struct.pack(">f", val_dec))[0])[2:].zfill(
            32 // 4
        )
        flag_hex_ = True
    elif type in ["double"]:
        len_bit = 64
        # dat_bit=struct.pack('>d',val_dec).encode('hex').zfill(64//4)
        dat_bit = hex(struct.unpack(">Q", struct.pack(">d", val_dec))[0])[2:].zfill(
            64 // 4
        )
        flag_hex_ = True
    elif type in ["boolean"]:
        if val_dec:
            val_dec_ = 1
        elif val_dec is False:
            val_dec_ = 0
        else:
            raise Exception(
                'Invalid argument value "val_dec"='
                + str(val_dec)
                + ' for "type"="boolean"'
            )
        dat_bit = format(int(val_dec_), "b").zfill(len_bit)
        flag_hex_ = False
    else:
        raise Exception('Unsupported "type":' + str(type))

    if flag_hex and not flag_hex_:
        if len_bit % 8 != 0:
            raise Exception('Cannot convert to hex for "len_bit":' + str(len_bit))
        dat_bit = format(int(dat_bit, 2), "x").zfill(len_bit // 4)
        if len(dat_bit) != len_bit // 4:
            raise Exception(
                'Ouput "dat_bit":'
                + str(dat_bit)
                + ' has not a length of "len_bit/4":'
                + str(len_bit // 4)
            )
    elif not flag_hex and flag_hex_:
        dat_bit = format(int(dat_bit, 16), "b").zfill(len_bit)
        if len(dat_bit) != len_bit:
            raise Exception(
                'Ouput "dat_bit":'
                + str(dat_bit)
                + ' has not a length of "len_bit":'
                + str(len_bit)
            )

    return dat_bit


def convert_bit_to_dec(dat_bit="", type="integer", flag_hex=False):
    """
    Function to convert from bit to pdt table
    :param dat_bit: ASCII bitstring or hexstring to be interpreted
    (NB: bit/hex strings are not expected to have prepended '0x'/'0b'),
    defaults to ''
    :type dat_bit: str, optional
    :param type: type of the input bit/hex-string to be interpreted as decimal
    , defaults to 'integer'
    :type type: enum['integer','unsigned integer','enumerated','signed integer'
    ,'float','double'], optional
    :param flag_hex: If True argument dat_bit is an hexstring, else it is a bitstring,
    defaults to False
    :type flag_hex: bool, optional
    :return: Decimal value interpreted from bit/hex-string input
    :rtype: boolean, integer, float, double or string

    """

    if type in ["boolean"]:
        if flag_hex:
            val_dec = int(dat_bit, 16)
        else:
            val_dec = int(dat_bit, 2)
        if val_dec == 0:
            val_dec = False
        elif val_dec == 1:
            val_dec = True
        else:
            raise Exception(
                "Cannot convert to <" + str(type) + '> from "dat_bit":' + str(dat_bit)
            )

    elif type in ["unsigned integer", "integer", "enumerated"]:
        if flag_hex:
            val_dec = int(dat_bit, 16)
        else:
            val_dec = int(dat_bit, 2)

    elif type in ["signed integer"]:
        if flag_hex:
            val_dec = int(dat_bit, 16)
            len_bit = len(dat_bit) * 4
        else:
            val_dec = int(dat_bit, 2)
            len_bit = len(dat_bit)

        if val_dec >= 2 ** (len_bit - 1):
            val_dec = val_dec - 2**len_bit

    elif type in ["float"]:
        if not flag_hex:
            if len(dat_bit) != 32:
                raise Exception(
                    "Cannot convert to <"
                    + str(type)
                    + '> from "dat_bit":'
                    + str(dat_bit)
                )
            dat_bit = format(int(dat_bit, 2), "x").zfill(len(dat_bit) // 4)
        else:
            if len(dat_bit) != 32 / 4:
                raise Exception(
                    "Cannot convert to <"
                    + str(type)
                    + '> from "dat_bit":'
                    + str(dat_bit)
                )

        # val_dec=struct.unpack('>f',dat_bit.decode('hex'))[0]
        val_dec = struct.unpack(">f", bytes.fromhex(dat_bit))[0]
        # val_dec=float("{:.7e}".format(val_dec))

        if math.isnan(val_dec):
            val_dec = "NaN"

    elif type in ["double"]:
        if not flag_hex:
            if len(dat_bit) != 64:
                raise Exception(
                    "Cannot convert to <"
                    + str(type)
                    + '> from "dat_bit":'
                    + str(dat_bit)
                )
            dat_bit = format(int(dat_bit, 2), "x").zfill(len(dat_bit) // 4)
        else:
            if len(dat_bit) != 64 / 4:
                raise Exception(
                    "Cannot convert to <"
                    + str(type)
                    + '> from "dat_bit":'
                    + str(dat_bit)
                )

        # val_dec=struct.unpack('>d',dat_bit.decode('hex'))[0]
        val_dec = struct.unpack(">d", bytes.fromhex(dat_bit))[0]
        # val_dec=float("{:.15e}".format(val_dec))

        if math.isnan(val_dec):
            val_dec = "NaN"

    else:
        raise Exception('Unsupported "type":' + str(type))
        logger.error('[WARNING]: Conversion from bit to dec not yet supported for type:'+str(type))
        # return '<'+str(type)+'>'

    return val_dec
