# Standard library imports
import os

import pytest
# Local imports
from src.algorithms.uvn_functions.converter.memory_image_converter import \
    MemoryImageConverter
from src.algorithms.uvn_functions.converter.netcdf_Converter import \
    NetcdfConverter
from tests.helpers import assert_img_files, assert_obj, load_input_files
from tests.utils.test_uvn_utils import Helper


@pytest.fixture(scope="class")
def suspend_capture(pytestconfig):
    """Use to make user interactions in the test or order stops for modify configurations file, HW..."""

    class suspend_guard:
        def __init__(self):
            self.capmanager = pytestconfig.pluginmanager.getplugin("capturemanager")

        def __enter__(self):
            self.capmanager.suspend_global_capture(in_=True)

        def __exit__(self, _1, _2, _3):
            self.capmanager.resume_global_capture()

    yield suspend_guard()


@pytest.fixture()
def load_UVN_test_files(request):

    # Set global  variables for testing
    global satellite
    global instrument
    satellite = "MTG-S 1"
    instrument = "UVN"

    # Load input files
    load_input_files(instrument, test_files_folder="tests/tests_assets")

    # Setup test files directory
    test_files = Helper.get_test_directory()
    request.cls.test_files = test_files

    # Get the first netcdf reference file  used for testing
    netcdf_conv = NetcdfConverter()
    file_path_ncd_10_ref = os.path.join(test_files, "Input", "ptd_3_7_1_0.nc")
    print(f"[DEBUG] Looking for file: {file_path_ncd_10_ref}")
    print(f"[DEBUG] Absolute path: {os.path.abspath(file_path_ncd_10_ref)}")
    print(f"[DEBUG] File exists: {os.path.exists(file_path_ncd_10_ref)}")
    dict_ptd_ncd_10_ref = netcdf_conv.convert_netcdf_to_dict(
        filename_ncd=file_path_ncd_10_ref
    )
    request.cls.dict_ptd_ncd_10_ref = dict_ptd_ncd_10_ref

    # Get the second netcdf reference file  used for testing
    file_path_ncd_20_ref = os.path.join(test_files, "Input", "ptd_3_7_2_0.nc")
    dict_ptd_ncd_20_ref = netcdf_conv.convert_netcdf_to_dict(
        filename_ncd=file_path_ncd_20_ref
    )
    request.cls.dict_ptd_ncd_20_ref = dict_ptd_ncd_20_ref

    # clean up
    Helper.clear_directory(os.path.join(test_files, "Output"))

    yield

    # clean up
    Helper.clear_directory(os.path.join(test_files, "Output"))


@pytest.mark.usefixtures("load_UVN_test_files")
class TestMemoryImageTypesNetcdfIntegration:
    def test_img_memory_SDRAM_ICU_A_ASW(self):
        """Test  excel file conversion to memorY image SDRAM ICU_A ASW
        with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # setup test files location
        file_path_img_10_ref = os.path.join(
            self.test_files, "input", "ptd_3_7_1_0_S4AARAM_ref.img"
        )
        file_path_img_10_tst = os.path.join(
            self.test_files, "output", "ptd_3_7_1_0_S4AARAM_test.img"
        )

        # Generate sdram memory image
        mem_img = MemoryImageConverter("SDRAM")

        dict_ptd_img_10_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_10_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_10_ref,
            filename_img=file_path_img_10_tst,
            icu_id="ICU_A",
            software_id="ASW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_10_ref, dict_ptd_img_10_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_10_ref], [file_path_img_10_tst])

    def test_img_memory_SDRAM_ICU_A_SSW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        file_path_img_13_ref = os.path.join(
            self.test_files, "input", "ptd_3_7_1_0_S4ASRAM_ref.img"
        )
        file_path_img_13_tst = os.path.join(
            self.test_files, "output", "ptd_3_7_1_0_S4ASRAM_test.img"
        )

        # Generate sdram memory image
        mem_img = MemoryImageConverter("SDRAM")
        dict_ptd_img_13_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_13_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_10_ref,
            filename_img=file_path_img_13_tst,
            icu_id="ICU_A",
            software_id="SSW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_10_ref, dict_ptd_img_13_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_13_ref], [file_path_img_13_tst])

    def test_img_memory_SDRAM_ICU_B_ASW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        file_path_img_10_ref = os.path.join(
            self.test_files, "input", "ptd_3_7_1_0_S4AARAM_ref.img"
        )
        file_path_img_10_tst = os.path.join(
            self.test_files, "output", "ptd_3_7_1_0_S4AARAM_test.img"
        )

        # Execute method
        mem_img = MemoryImageConverter("SDRAM")
        dict_ptd_img_10_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_10_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_10_ref,
            filename_img=file_path_img_10_tst,
            icu_id="ICU_A",
            software_id="ASW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_10_ref, dict_ptd_img_10_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_10_ref], [file_path_img_10_tst])

    def test_img_memory_SDRAM_ICU_B_SSW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # Setup test files
        file_path_img_13_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_1_0_S4ASRAM_ref.img"
        )
        file_path_img_13_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_1_0_S4ASRAM_test.img"
        )

        # Generate sdram memory image
        mem_img = MemoryImageConverter("SDRAM")
        dict_ptd_img_13_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_13_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_10_ref,
            filename_img=file_path_img_13_tst,
            icu_id="ICU_A",
            software_id="SSW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_10_ref, dict_ptd_img_13_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_13_ref], [file_path_img_13_tst])

    def test_img_mem_EEPROM_A_ICU_A_ASW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0
        Check that CRC of PTD is 0x3d2a as announced in GS4-ADSO-UVN-NO-1000723064 (PFM SW SRN)
        """

        # Setup test files
        file_path_img_11_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_1_0_S4AAEEPA_ref.img"
        )
        file_path_img_11_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_1_0_S4AAEEPA_test.img"
        )

        # Generate EPROM memory image
        mem_img = MemoryImageConverter("EEPROM_A")
        dict_ptd_img_11_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_11_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_10_ref,
            filename_img=file_path_img_11_tst,
            icu_id="ICU_A",
            software_id="ASW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_10_ref, dict_ptd_img_11_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_11_ref], [file_path_img_11_tst])

    def test_img_mem_EEPROM_A_ICU_A_SSW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # Setup test files
        file_path_img_14_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_1_0_S4ASEEPA_ref.img"
        )
        file_path_img_14_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_1_0_S4ASEEPA_test.img"
        )

        # Generate EEPROM_A memory image
        mem_img = MemoryImageConverter("EEPROM_A")
        dict_ptd_img_14_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_14_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_10_ref,
            filename_img=file_path_img_14_tst,
            icu_id="ICU_A",
            software_id="SSW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_10_ref, dict_ptd_img_14_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_14_ref], [file_path_img_14_tst])

    def test_img_mem_EEPROM_A_ICU_B_ASW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # Setup test files
        file_path_img_21_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_2_0_S4BAEEPA_ref.img"
        )
        file_path_img_21_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_2_0_S4BAEEPA_test.img"
        )

        # Generate EEPROM_A memory image
        mem_img = MemoryImageConverter("EEPROM_A")
        dict_ptd_img_21_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_21_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_20_ref,
            filename_img=file_path_img_21_tst,
            icu_id="ICU_B",
            software_id="ASW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_20_ref, dict_ptd_img_21_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_21_ref], [file_path_img_21_tst])

    def test_img_mem_EEPROM_A_ICU_B_SSW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # Setup test files
        file_path_img_24_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_2_0_S4BSEEPA_ref.img"
        )
        file_path_img_24_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_2_0_S4BSEEPA_test.img"
        )

        # Generate  memory image
        mem_img = MemoryImageConverter("EEPROM_A")
        dict_ptd_img_24_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_24_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_20_ref,
            filename_img=file_path_img_24_tst,
            icu_id="ICU_B",
            software_id="SSW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_20_ref, dict_ptd_img_24_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_24_ref], [file_path_img_24_tst])

    def test_img_mem_EEPROM_B_ICU_A_SSW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # Setup test files
        file_path_img_15_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_1_0_S4ASEEPB_ref.img"
        )
        file_path_img_15_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_1_0_S4ASEEPB_test.img"
        )

        # Generate  memory image
        mem_img = MemoryImageConverter("EEPROM_B")
        dict_ptd_img_15_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_15_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_10_ref,
            filename_img=file_path_img_15_tst,
            icu_id="ICU_A",
            software_id="SSW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_10_ref, dict_ptd_img_15_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_15_ref], [file_path_img_15_tst])

    def test_img_mem_EEPROM_B_ICU_A_ASW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # Setup test files
        file_path_img_12_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_1_0_S4AAEEPB_ref.img"
        )
        file_path_img_12_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_1_0_S4AAEEPB_test.img"
        )

        # Generate  memory image
        mem_img = MemoryImageConverter("EEPROM_B")
        dict_ptd_img_12_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_12_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_10_ref,
            filename_img=file_path_img_12_tst,
            icu_id="ICU_A",
            software_id="ASW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_10_ref, dict_ptd_img_12_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_12_ref], [file_path_img_12_tst])

    def test_img_mem_EEPROM_B_ICU_B_ASW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # Setup test files
        file_path_img_22_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_2_0_S4BAEEPB_ref.img"
        )
        file_path_img_22_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_2_0_S4BAEEPB_test.img"
        )

        # Generate  memory image
        mem_img = MemoryImageConverter("EEPROM_B")
        dict_ptd_img_22_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_22_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_20_ref,
            filename_img=file_path_img_22_tst,
            icu_id="ICU_B",
            software_id="ASW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_20_ref, dict_ptd_img_22_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_22_ref], [file_path_img_22_tst])

    def test_img_mem_EEPROM_B_ICU_B_SSW(self):
        """Test with files delivered with ASW_3_4_2_0_PTD_3_7_1_0"""

        # Setup test files
        file_path_img_25_ref = os.path.join(
            self.test_files, "Input", "ptd_3_7_2_0_S4BSEEPB_ref.img"
        )
        file_path_img_25_tst = os.path.join(
            self.test_files, "Output", "ptd_3_7_2_0_S4BSEEPB_test.img"
        )

        # Generate  memory image
        mem_img = MemoryImageConverter("EEPROM_B")
        dict_ptd_img_25_ref = mem_img.convert_image_to_dict(
            filename_img=file_path_img_25_ref
        )
        mem_img.convert_dict_to_image(
            dict_ptd=self.dict_ptd_ncd_20_ref,
            filename_img=file_path_img_25_tst,
            icu_id="ICU_B",
            software_id="SSW",
        )

        # Compare resulting dictionaries against reference:
        assert assert_obj(self.dict_ptd_ncd_20_ref, dict_ptd_img_25_ref)

        # Compare resulting files (xls, NetCDF, memory image) against reference files:
        assert assert_img_files([file_path_img_25_ref], [file_path_img_25_tst])
