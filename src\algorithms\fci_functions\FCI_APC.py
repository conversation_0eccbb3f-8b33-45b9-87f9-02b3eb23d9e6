# Standard library imports
import datetime
import os
import pandas as pd
from typing import Dict, Any

# Local imports
from src.utils.import_utils import config, basics
from src.utils.conversion_utils import dec2hex, hex2dec, float_to_hex
from src.logger_wrapper import logger
from src.utils.excel_utils import Read_Sheet
from src.utils.netcdf_utils import read_netCDF
from src.functions import create_XLDT
from src.utils.activity_params import ActivityParams

def FCI_APC(act_params: ActivityParams, satellite: str) -> None:
    """Generate FCI APC binary file.
    
    Args:
        act_params: Activity parameters containing APC configuration:
            - instrument: Must be 'FCI'
            - scan_law_id: Scan Law identifier
            - mm_slot: Mass Memory slot ID
        satellite: Satellite identifier (e.g., 'MTG-I1')
    """
    # Load Configuration
    dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
    df_APC_Config = pd.read_csv(
        os.path.join(
            config.CONFIG_VALUES["config_folder"], act_params.instrument, "FCI_APC_Conf.csv"
        ),
        keep_default_na=False,
        index_col="Section",
    )
    
    # Initialize variables
    used_files = ["APCTAB"]
    main_files = ["APCTAB"]
    Binary_Section_Hex = {}
    Section_Pos = {}
    Body_Size = 0
    XLDT_Body_Hex = ""

    # Create output file name
    str_file_name = os.path.join(
        config.CONFIG_VALUES["output_folder"],
        satellite,
        f"OPIT_NA_MS_OPE+MTI{satellite[-1]}+FCI_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_APC-MM{act_params.mm_slot}-APCID{str(act_params.scan_law_id).zfill(5)}.bin",
    )

    # Get the dictionary of values from the netCDF files
    logger.info("Reading netCDF files for FCI APCTAB")

    # Read netCDF file using updated signature
    dict_netCDF = read_netCDF(
        act_params=act_params,
        satellite=satellite,
        main_files=main_files,
        used_files=used_files,
        instrument_conf=dict_config
    )

    # For each Section, generate the HEX string
    for File in used_files:
        logger.info("Reading VSM file for FCI APC")
        File_Tab = Read_Sheet(dict_config["vsm_path"], File)

        logger.info("Generating the FCI APC binary file")

        # Find only variables needed for the specific Section
        for str_sections in df_APC_Config.index:
            df_file_tab_proc = File_Tab[
                File_Tab["XLDT_Section"].str.contains(str_sections)
            ]
            if df_file_tab_proc.empty and str_sections != "XLDT_Header":
                logger.error(
                    f"Section name {str_sections} has not been found in the VSM configuration file for this activity."
                )
                basics.pop_up_message(
                    "Ouch!", "An error Occurred please check the logs.", "error"
                )
                raise RuntimeError()

            Section_Pos[str_sections] = df_APC_Config.loc[str_sections]["Order"]
            df_file_tab_proc.set_index("Name", inplace=True)

            # Generate the Section HEX string
            Binary_Section_Hex[Section_Pos[str_sections]] = FCI_APC_Section_Build(
                df_file_tab_proc, str_sections, df_APC_Config, act_params.mm_slot, dict_netCDF
            )

            # Calculate the size of the XLDT Body
            if str_sections != "XLDT_Header":
                Body_Size = (
                    int(len(Binary_Section_Hex[Section_Pos[str_sections]]) / 2)
                    + Body_Size
                )

    Body_Size_Hex = dec2hex(Body_Size).zfill(8)

    # Add XLDT Body Length to XLDT Header
    Binary_Section_Hex[Section_Pos["XLDT_Header"]] = (
        Binary_Section_Hex[Section_Pos["XLDT_Header"]] + Body_Size_Hex
    )

    # Put together the different Sections of the XLDT Body in order in one Hex String
    for int_part in range(1, df_APC_Config["Order"].max() + 1):
        str_section = df_APC_Config[df_APC_Config["Order"] == int_part].index[0]
        if str_section != "XLDT_Header":
            XLDT_Body_Hex += Binary_Section_Hex[int_part]

    # Put everything together in one Hex String (assumes XLDT Header, Body order)
    XLDT_Hex = Binary_Section_Hex[Section_Pos["XLDT_Header"]] + XLDT_Body_Hex

    # Create XLDT Binary file for Scan Law
    create_XLDT(XLDT_Hex, str_file_name)

def FCI_APC_Section_Build(
    df_file_tab_proc: pd.DataFrame,
    str_sections: str,
    df_APC_Config: pd.DataFrame,
    int_MM_Slot: int,
    dict_netCDF: Dict[str, Any]
) -> str:
    """Build binary file content of APC for each section in hex format.
    
    Args:
        df_file_tab_proc: Processed file table for this section
        str_sections: Section name ("XLDT_Header", "Header", etc.)
        df_APC_Config: APC configuration dataframe
        int_MM_Slot: Mass Memory slot ID
        dict_netCDF: Dictionary containing netCDF file data
        
    Returns:
        String containing hexadecimal data for this section
        
    Raises:
        RuntimeError: If section length verification fails
    """
    # XLDT Header - without body length
    if str_sections == "XLDT_Header":
        str_hex = f"000200{dec2hex(int_MM_Slot).zfill(2)}"

    # XLDT Body Header
    elif str_sections == "Header":
        # Initialize Variables
        lst_hex = []
        Header_str = {}

        for variable in df_file_tab_proc.index:
            Header_var = dict_netCDF[variable]

            Header_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])
            Header_str_Size = int(df_file_tab_proc.loc[variable, "XLDT_Length"])

            # Check the header dimension
            if len(Header_var.shape) > 0:
                Header_var_Whole = ""
                for array_byte in Header_var:
                    if pd.api.types.is_integer_dtype(Header_var.dtype):
                        Header_var_Hex = dec2hex(array_byte).zfill(2)
                    elif pd.api.types.is_float_dtype(Header_var.dtype):
                        Header_var_Hex = float_to_hex(array_byte).zfill(2)
                    Header_var_Whole = Header_var_Whole + Header_var_Hex
                Header_str[Header_str_Pos] = Header_var_Whole.zfill(
                    int(Header_str_Size * 2)
                )
            else:
                if pd.api.types.is_integer_dtype(Header_var.dtype):
                    Header_str[Header_str_Pos] = dec2hex(Header_var).zfill(
                        int(Header_str_Size * 2)
                    )
                elif pd.api.types.is_float_dtype(Header_var.dtype):
                    Header_str[Header_str_Pos] = float_to_hex(
                        Header_var
                    ).zfill(int(Header_str_Size * 2))

        for int_pos in range(1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1):
            # Checksum not included
            lst_hex.append(Header_str[int_pos])

        # Calculate checksum
        str_checksum_dec = hex2dec(basics.checksum("".join(lst_hex), "CRC16"))

        # Add checksum to Hexadecimal string
        lst_hex.append(dec2hex(str_checksum_dec).zfill(4))

        # Join list to obtain hexadecimal string
        str_hex = "".join(lst_hex)

        # Length verification
        Supposed_Length = int(df_APC_Config.loc["Header"]["Length"])
        Actual_Length = int(len(str_hex) / 2)

        if Supposed_Length != Actual_Length:
            logger.error(
                f"The calculated Header length {Actual_Length} differs from the expected length {Supposed_Length}."
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

    # XLDT FDC - All as many as there are in one Hex string
    elif str_sections == "FDC":
        # Initialize Variables
        lst_hex = []
        FDA_str = {}
        lst_hex_body = []
        lst_expected_len_hex = []

        FDA_Sections_num = df_file_tab_proc["XLDT_Section_Order"].max() - 1

        for variable in df_file_tab_proc.index:
            FDA_var = dict_netCDF[variable]
            FDA_var_Dim = len(FDA_var.shape)
            FDA_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])
            FDA_str_Size = int(df_file_tab_proc.loc[variable, "XLDT_Length"])

            if FDA_var_Dim == 1:
                lst_FDA_var_whole = []
                for array_byte in FDA_var:
                    if pd.api.types.is_integer_dtype(FDA_var.dtype):
                        FDA_var_Hex = dec2hex(array_byte).zfill(2)
                    elif pd.api.types.is_float_dtype(FDA_var.dtype):
                        FDA_var_Hex = float_to_hex(array_byte).zfill(2)

                    lst_FDA_var_whole.append(FDA_var_Hex)

                # Create join string
                FDA_var_Whole = "".join(lst_FDA_var_whole)

                FDA_str[FDA_str_Pos] = FDA_var_Whole.zfill(int(FDA_str_Size * 2))

            elif FDA_var_Dim == 0:
                if pd.api.types.is_integer_dtype(FDA_var.dtype):
                    FDA_str[FDA_str_Pos] = dec2hex(FDA_var).zfill(
                        int(FDA_str_Size * 2)
                    )
                elif pd.api.types.is_float_dtype(FDA_var.dtype):
                    FDA_str[FDA_str_Pos] = float_to_hex(FDA_var).zfill(
                        int(FDA_str_Size * 2)
                    )

        for pos in range(1, int(FDA_Sections_num) + 1):
            # Checksum not included
            lst_hex.append(FDA_str[pos])

        # Calculate checksum
        str_checksum_dec = hex2dec(basics.checksum("".join(lst_hex), "CRC16"))

        # Add checksum to hexadecimal string
        lst_hex.append(dec2hex(str_checksum_dec).zfill(4))

        # FDC Body
        FDA_Body_Name = df_file_tab_proc[
            df_file_tab_proc["XLDT_Section_Order"] == int(FDA_Sections_num + 1)
        ].index[0]

        # Obtain actual number of expected body parts to avoid adding the ones that are not used (netCDF always filled in to max dimension)
        FDA_str_Size = int(df_file_tab_proc.loc[FDA_Body_Name, "XLDT_Length"])
        expected_len_array = dict_netCDF["fdc_part_length"]

        for array_byte in expected_len_array:
            lst_expected_len_hex.append(dec2hex(array_byte).zfill(2))

        expected_len_dec = hex2dec("".join(lst_expected_len_hex))
        number_of_Pos = int(
            (expected_len_dec - len("".join(lst_hex)) / 2) / FDA_str_Size
        )

        for Pos in range(number_of_Pos):
            lst_FDA_var_whole = []
            FDA_var = dict_netCDF[FDA_Body_Name][Pos]

            for array_byte in FDA_var:
                if pd.api.types.is_integer_dtype(FDA_var.dtype):
                    FDA_var_Hex = dec2hex(array_byte).zfill(2)
                elif pd.api.types.is_float_dtype(FDA_var.dtype):
                    FDA_var_Hex = float_to_hex(array_byte).zfill(2)

                lst_FDA_var_whole.append(FDA_var_Hex)

            lst_hex_body.append("".join(lst_FDA_var_whole))

        lst_hex.append("".join(lst_hex_body))

        # Join hexadecimal string
        str_hex = "".join(lst_hex)

        # Length verification
        Max_Length = int(df_APC_Config.loc["FDC"]["Length"])
        Actual_Length = int(len(str_hex) / 2)

        if Actual_Length > Max_Length:
            logger.error(
                "The size of the FDC Section of the APC is larger than the maximum allowed. The generated binary should not be trusted."
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        # Check if actual length and expected one are the same
        if Actual_Length != expected_len_dec:
            logger.error(
                "The size of the FDC Section of the APC is larger than the expected fdc_part_length. The generated binary should not be trusted."
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

    # XLDT LAC Pointers - All as many as there are in one Hex string
    elif str_sections == "LAC_Pointer":
        # Initialize Variables
        lst_hex = []
        number_of_LACs = len(dict_netCDF[df_file_tab_proc.index[0]])

        for LAC in range(number_of_LACs):
            # Reset variables
            LAC_str = {}
            lst_LAC_hex = []

            for variable in df_file_tab_proc.index:
                LAC_var = dict_netCDF[variable][LAC]
                LAC_var_Dim = len(LAC_var.shape)
                LAC_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])

                LAC_str_Size = int(df_file_tab_proc.loc[variable, "XLDT_Length"])

                if LAC_var_Dim == 1:
                    LAC_var_Whole = ""
                    for array_byte in LAC_var:
                        if pd.api.types.is_integer_dtype(LAC_var.dtype):
                            LAC_var_Hex = dec2hex(array_byte).zfill(2)
                        elif pd.api.types.is_float_dtype(LAC_var.dtype):
                            LAC_var_Hex = float_to_hex(array_byte).zfill(2)
                        LAC_var_Whole += LAC_var_Hex
                    LAC_str[LAC_str_Pos] = LAC_var_Whole.zfill(int(LAC_str_Size * 2))
                else:
                    if pd.api.types.is_integer_dtype(LAC_var.dtype):
                        LAC_str[LAC_str_Pos] = dec2hex(LAC_var).zfill(
                            int(LAC_str_Size * 2)
                        )
                    elif pd.api.types.is_float_dtype(LAC_var.dtype):
                        LAC_str[LAC_str_Pos] = float_to_hex(LAC_var).zfill(
                            int(LAC_str_Size * 2)
                        )

            for int_pos in range(
                1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1
            ):
                # Checksum not included
                lst_LAC_hex.append(LAC_str[int_pos])

            # Calculate checksum
            str_checksum_dec = hex2dec(
                basics.checksum("".join(lst_LAC_hex), "CRC16")
            )

            # Add checksum to variable
            lst_LAC_hex.append(dec2hex(str_checksum_dec).zfill(4))

            # Create join string
            str_LAC_hex = "".join(lst_LAC_hex)

            # Length verification
            Supposed_Length = int(df_APC_Config.loc["LAC_Pointer"]["Length"])
            Actual_Length = int(len(str_LAC_hex) / 2)

            if Supposed_Length != Actual_Length:
                logger.error(
                    f"The calculated LAC Pointer {LAC + 1} length {Actual_Length} differs from the expected length {Supposed_Length}."
                )
                basics.pop_up_message(
                    "Ouch!", "An error Occurred please check the logs.", "error"
                )
                raise RuntimeError()

            lst_hex.append(str_LAC_hex)

        # Join hexadecimal string
        str_hex = "".join(lst_hex)

    # XLDT Retraces - All as many as there are in one Hex string
    elif str_sections == "Retrace":
        # Initialize Variables
        lst_hex = []

        partids = dict_netCDF["trans_part_id"]
        usefulpartids = [i for i in partids if i > 56 and i < 73]
        int_retraces = len(usefulpartids)

        for int_retraces in range(int_retraces):
            # Retrace Header
            Retrace_str = {}
            lst_retrace_hex = []
            lst_hex_body = []
            lst_expected_len_hex_tr = []

            Retrace_Sections_num = df_file_tab_proc["XLDT_Section_Order"].max() - 1

            for variable in df_file_tab_proc.index:
                Retrace_var = dict_netCDF[variable][int_retraces]
                Retrace_var_Dim = len(Retrace_var.shape)
                Retrace_str_Pos = int(
                    df_file_tab_proc.loc[variable, "XLDT_Section_Order"]
                )
                Retrace_str_Size = int(df_file_tab_proc.loc[variable, "XLDT_Length"])

                if Retrace_var_Dim == 1:
                    lst_retrace_var_whole = []
                    for array_byte in Retrace_var:
                        if pd.api.types.is_integer_dtype(Retrace_var.dtype):
                            Retrace_var_Hex = dec2hex(array_byte).zfill(2)
                        elif pd.api.types.is_float_dtype(Retrace_var.dtype):
                            Retrace_var_Hex = float_to_hex(array_byte).zfill(
                                2
                            )

                        lst_retrace_var_whole.append(Retrace_var_Hex)

                    # Join the list into final string
                    Retrace_var_Whole = "".join(lst_retrace_var_whole)

                    Retrace_str[Retrace_str_Pos] = Retrace_var_Whole.zfill(
                        int(Retrace_str_Size * 2)
                    )
                elif Retrace_var_Dim == 0:
                    if pd.api.types.is_integer_dtype(Retrace_var.dtype):
                        Retrace_str[Retrace_str_Pos] = dec2hex(
                            Retrace_var
                        ).zfill(int(Retrace_str_Size * 2))
                    elif pd.api.types.is_float_dtype(Retrace_var.dtype):
                        Retrace_str[Retrace_str_Pos] = float_to_hex(
                            Retrace_var
                        ).zfill(int(Retrace_str_Size * 2))

            for pos in range(1, int(Retrace_Sections_num) + 1):
                # Checksum not included
                lst_retrace_hex.append(Retrace_str[pos])

            # Calculate checksum
            str_checksum_dec = hex2dec(
                basics.checksum("".join(lst_retrace_hex), "CRC16")
            )

            lst_retrace_hex.append(dec2hex(str_checksum_dec).zfill(4))

            # Retrace Body
            Retrace_Body_Name = df_file_tab_proc[
                df_file_tab_proc["XLDT_Section_Order"] == int(Retrace_Sections_num + 1)
            ].index[0]

            Retrace_str_Size = int(
                df_file_tab_proc.loc[Retrace_Body_Name, "XLDT_Length"]
            )
            expected_len_array_tr = dict_netCDF["trans_part_length"][int_retraces]

            for array_byte in expected_len_array_tr:
                lst_expected_len_hex_tr.append(dec2hex(array_byte).zfill(2))

            expected_len_dec = hex2dec("".join(lst_expected_len_hex_tr))
            number_of_Pos = int(
                (expected_len_dec - len("".join(lst_retrace_hex)) / 2)
                / Retrace_str_Size
            )

            for Pos in range(number_of_Pos):
                lst_retrace_var_whole = []
                Retrace_var = dict_netCDF[Retrace_Body_Name][int_retraces][Pos]

                for array_byte in Retrace_var:
                    if pd.api.types.is_integer_dtype(Retrace_var.dtype):
                        Retrace_var_Hex = dec2hex(array_byte).zfill(2)
                    elif pd.api.types.is_float_dtype(Retrace_var.dtype):
                        Retrace_var_Hex = float_to_hex(array_byte).zfill(2)

                    lst_retrace_var_whole.append(Retrace_var_Hex)

                lst_hex_body.append("".join(lst_retrace_var_whole))

            lst_retrace_hex.append("".join(lst_hex_body))

            # Create final string
            str_retrace_hex = "".join(lst_retrace_hex)

            # Length verification
            Max_Length = int(df_APC_Config.loc["Retrace"]["Length"])
            Actual_Length = int(len(str_retrace_hex) / 2)

            if Actual_Length > Max_Length:
                logger.error(
                    f"The size of the Retrace Section {int_retraces + 1} of the APC is larger than the maximum allowed. The generated binary should not be trusted."
                )
                basics.pop_up_message(
                    "Ouch!", "An error Occurred please check the logs.", "error"
                )
                raise RuntimeError()

            # Check if actual length and expected one are the same
            if Actual_Length != expected_len_dec:
                logger.error(
                    f"The size of the Retrace Section {int_retraces + 1} of the APC is different than the expected trans_part_length. The generated binary should not be trusted."
                )
                basics.pop_up_message(
                    "Ouch!", "An error Occurred please check the logs.", "error"
                )
                raise RuntimeError()

            lst_hex.append(str_retrace_hex)

        # Join hexadecimal string
        str_hex = "".join(lst_hex)

    return str_hex
