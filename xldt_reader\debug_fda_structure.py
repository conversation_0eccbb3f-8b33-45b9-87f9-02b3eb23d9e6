#!/usr/bin/env python3
"""
Debug the FDA section structure to understand the correct field mapping.
"""

import os
from xldt_reader_standalone import <PERSON><PERSON><PERSON><PERSON><PERSON>

def debug_fda_structure():
    """Debug FDA section to understand the correct structure."""
    
    config_path = "../config/IRS/IRS_SL_Conf.csv"
    xldt_file = "inputs/ScanLaw_Central_Summer_16384.xldt"
    
    if not os.path.exists(xldt_file):
        print(f"❌ XLDT file not found: {xldt_file}")
        return
    
    print("🔍 Debugging FDA Section Structure")
    print("=" * 50)
    
    try:
        reader = XLDTReader(config_path=config_path)
        parsed_data = reader.read_xldt_file(xldt_file)
        
        # Check all sections for the pattern
        pattern = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]

        for section_name, section_data in parsed_data['sections'].items():
            if hasattr(section_data, 'data'):
                print(f"\n🔍 {section_name} Section:")
                print(f"  Length: {section_data.length} bytes")

                bytes_data = section_data.data
                hex_data = bytes_data.hex().upper()
                print(f"  First 50 hex chars: {hex_data[:50]}")

                # Look for the pattern
                found_pattern = False
                for i in range(len(bytes_data) - len(pattern) + 1):
                    window = [bytes_data[i+j] for j in range(len(pattern))]
                    if window == pattern:
                        print(f"  ✅ Found pattern [11, 5, 1, 1, 1, 1, 1, 1, 7, 3] at byte offset {i}")
                        found_pattern = True

                        # Show more context around the pattern
                        start = max(0, i-10)
                        end = min(len(bytes_data), i+len(pattern)+10)
                        context = [bytes_data[j] for j in range(start, end)]
                        print(f"  Context: {context}")
                        break

                if not found_pattern:
                    print(f"  ❌ Pattern not found in {section_name}")

                # For FDA section, show detailed analysis
                if section_name == 'FDA':
                    print(f"\n  📊 FDA Detailed Analysis:")
                    print(f"  First 30 bytes: {[bytes_data[i] for i in range(min(30, len(bytes_data)))]}")

                    # Check for any non-zero values that might indicate the start of real data
                    non_zero_positions = [i for i, b in enumerate(bytes_data) if b != 0]
                    print(f"  First 20 non-zero positions: {non_zero_positions[:20]}")
                    print(f"  Values at those positions: {[bytes_data[i] for i in non_zero_positions[:20]]}")

        # Also check other XLDT files
        print(f"\n🔍 Checking other XLDT files:")
        xldt_files = [
            "inputs/ScanLaw_Central_Winter_0.xldt",
            "inputs/ScanLaw_East_Summer_16386.xldt",
            "inputs/ScanLaw_West_Summer_16385.xldt",
            "inputs/ScanLaw_West_Winter_1.xldt"
        ]

        for xldt_file in xldt_files:
            if os.path.exists(xldt_file):
                print(f"\n  📁 {xldt_file}:")
                try:
                    other_data = reader.read_xldt_file(xldt_file)
                    if 'FDA' in other_data['sections']:
                        fda_bytes = other_data['sections']['FDA'].data
                        # Quick check for pattern
                        for i in range(len(fda_bytes) - len(pattern) + 1):
                            window = [fda_bytes[i+j] for j in range(len(pattern))]
                            if window == pattern:
                                print(f"    ✅ Found pattern at offset {i}")
                                break
                        else:
                            print(f"    ❌ Pattern not found")
                except Exception as e:
                    print(f"    ❌ Error reading: {e}")
            else:
                print(f"    ❌ File not found: {xldt_file}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_fda_structure()
