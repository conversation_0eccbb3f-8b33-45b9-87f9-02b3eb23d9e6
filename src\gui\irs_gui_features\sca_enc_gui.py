import tkinter as tk
from tkinter import ttk
from typing import List, Optional
from src.utils.import_utils import basics, logger
from src import functions
from ..frames import BaseFrame
from ..custom_widgets import PrimaryCard, SecondaryCard
from src.utils.activity_params import ActivityParams
from ..theme_manager import ThemeManager
from src.utils.netcdf_utils import get_netcdf_variables_value

class SCA_ENC_GUI(BaseFrame):
    """Frame for IRS Scan Encoder configuration"""
    def __init__(self, parent: tk.Widget, act_params: ActivityParams, *args, **kwargs):
        """Initialize the Scan Encoder GUI frame."""
        self.act_params = act_params
        self.var_Side = tk.StringVar()
        self.input_scan_corr = None
        super().__init__(parent, *args, **kwargs)

    def create_widgets(self):
        """Create and layout GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Main frame using grid
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew") # Use grid

        # Configure main layout rows/columns
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)

    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"IRS Scan Encoder Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew") # Use grid

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5) # Use grid

    def _create_body(self, parent):
        """Creates the body section with input cards, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)

        # Configure grid to center content
        body_frame.columnconfigure(0, weight=1) # Left spacer
        body_frame.columnconfigure(1, weight=0) # Content column
        body_frame.columnconfigure(2, weight=1) # Right spacer
        body_frame.rowconfigure(0, weight=1) # Top spacer
        body_frame.rowconfigure(1, weight=0) # SCAE Card
        body_frame.rowconfigure(2, weight=0) # LUT Card
        body_frame.rowconfigure(3, weight=1) # Bottom spacer        # SCAE ID Card - Removed fixed sizes
        scae_card = PrimaryCard(body_frame, title="SCAE Configuration", padding=5)
        scae_card.grid(row=1, column=1, pady=10, sticky="ew") # Center column

        scae_content = scae_card.get_content_frame()
        frame_scae = ttk.Frame(scae_content, style="PrimaryCard.TFrame")
        frame_scae.pack(anchor="center", pady=5, padx=5)
        ttk.Label(frame_scae, text="SCAE ID:", style="PrimaryCard.TLabel").pack(side=tk.LEFT, padx=5)

        # Get SCAE ID values from netCDF files
        try:
            scae_id_values = list(get_netcdf_variables_value('scae_id', self.act_params, "SCANENC"))
            scae_id_values = [str(val) for val in scae_id_values]  # Convert to strings
            scae_id_values.sort()
        except Exception as e:
            logger.warning(f"Could not load SCAE IDs from netCDF: {e}")
            scae_id_values = ["0", "1", "2"]  # Default values

        scae_combo = ttk.Combobox(
            frame_scae,
            textvariable=self.var_Side,
            values=scae_id_values,
            width=5,
            state="readonly",
            style="PrimaryCard.TCombobox"
        )
        scae_combo.pack(side=tk.LEFT, padx=5)
        if scae_id_values:
            scae_combo.current(0)  # Default to first value        
            # Scan Encoder LUT ID Card - Removed fixed sizes
        lut_card = SecondaryCard(body_frame, title="LUT Configuration", padding=5)
        lut_card.grid(row=2, column=1, pady=10, sticky="ew") # Center column

        lut_content = lut_card.get_content_frame()
        frame_scan_id = ttk.Frame(lut_content, style="SecondaryCard.TFrame")
        frame_scan_id.pack(anchor="center", pady=5, padx=5)
        ttk.Label(frame_scan_id, text="LUT Index:", style="SecondaryCard.TLabel").pack(side=tk.LEFT, padx=5)

        # Get LUT Index values from netCDF files
        try:
            lut_index_values = list(get_netcdf_variables_value('scae_lut_index', self.act_params, "SCANENC"))
            lut_index_values = [str(val) for val in lut_index_values]  # Convert to strings
            lut_index_values.sort()
        except Exception as e:
            logger.warning(f"Could not load LUT Index values from netCDF: {e}")
            lut_index_values = ["1", "2", "3", "4", "5"]  # Default values

        self.input_scan_corr = ttk.Combobox(
            frame_scan_id,
            values=lut_index_values,
            width=10,
            state="readonly",
            style="SecondaryCard.TCombobox"
        )
        self.input_scan_corr.pack(side=tk.LEFT, padx=5)
        if lut_index_values:
            self.input_scan_corr.current(len(lut_index_values) - 1)  # Default to last value

    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=2)

        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5)

        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )


    def execute(self) -> None:
        """Handle execution of scan encoder configuration"""
        # Ensure widgets are created before accessing them
        if self.input_scan_corr is None:
            logger.error("Execute called before widgets created in IRS SCA_ENC_GUI")
            basics.pop_up_message("Error", "GUI not fully initialized.", "error")
            return

        # Validate and update SCAE ID
        side_val = self.var_Side.get()
        if not side_val:
            basics.pop_up_message("Error", "SCAE ID not selected", "error")
            return
        self.act_params.side = side_val        # Validate and update Scan Encoder LUT ID
        scan_corr = self.input_scan_corr.get()
        if not scan_corr:
            basics.pop_up_message("Error", "LUT Index not selected", "error")
            return

        # Validate that the value is numeric
        try:
            lut_id_int = int(scan_corr)
        except ValueError:
            basics.pop_up_message("Error", "LUT Index must be a numeric value", "error")
            return

        self.act_params.scan_encode_correction_lut_id = lut_id_int

        logger.info(f"Generating Scan Encoder Config - SCAE ID: {self.act_params.side}, LUT ID: {self.act_params.scan_encode_correction_lut_id}")
        functions.generate_outputs(act_params=self.act_params)
        basics.pop_up_message("Success", "Outputs generated successfully.", "info")


    def back(self) -> None:
        """Handle back navigation"""
        self.app.back()
