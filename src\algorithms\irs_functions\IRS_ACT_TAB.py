# Standard libraries imports
from typing import Dict, Any, List, Tuple, Optional, Union
import os
import datetime
import struct
from dataclasses import dataclass
# Local imports
from src.utils.import_utils import basics, floppy, config
from src.utils.conversion_utils import hex2dec
from src.logger_wrapper import logger
from src.utils.activity_params import ActivityParams
from src.algorithms.irs_functions.IRS_PAF import get_doy_date_for_filename
from src.utils.conversion_utils import iso_checksum_int
from src.utils.netcdf_utils import read_netCDF
# Constants
MAX_ACTIVITIES_PER_TC = 100
MAX_LAC_COUNT = 16
MIN_LAC_COUNT = 1
ACTIVITY_TYPE_MAP = {
    0: "MOVE_SCA",
    1: "MOVE_CCM",
    2: "MOVE_COM",
    3: "CHECK_SCA",
    4: "CHECK_COM",
}
@dataclass
class LACData:
    """Data structure to hold LAC (Local Area Coverage) information."""
    lac_id_isp: int
    lac_id: int
    nb_dwell: int
    first_dwell_id: int
    nb_activity: int
    activity_types: List[int]
    activity_parameters: List[int]
    time_offsets: List[int]
def IRS_ACT_TAB(act_params: ActivityParams, satellite: str) -> None:
    """Launch IRS Activity Table processing.
    Args:
        act_params: Activity parameters object
        satellite: Satellite identifier string
    """
    dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
    # Create TC filename and path
    if hasattr(act_params, 'activity') and 'SAV' in str(act_params.activity).upper():
        act_params.activity_table_slot = 2  # Always use slot 2 for SAV
        date_str = get_doy_date_for_filename(act_params)
        str_full_path = os.path.join(
            config.CONFIG_VALUES["output_folder"],
            satellite,
            f"MTS1+IRS_SAV_ACTTAB-{date_str}-ID{act_params.activity_table_id}.ssf",
        )
        print(f"[DEBUG] IRS_ACT_TAB: Will write SAV ACTTAB to: {str_full_path}")
    else:
        str_full_path = os.path.join(
            config.CONFIG_VALUES["output_folder"],
            satellite,
            f"MTS{satellite.split(' ')[1]}_ACTTAB_ID{str(act_params.activity_table_id).zfill(4)}_SLOT{str(act_params.activity_table_slot).zfill(1)}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.ssf",
        )
        print(f"[DEBUG] IRS_ACT_TAB: Will write ACTTAB to: {str_full_path}")
    # Read and process netCDF data
    # For SAV scenarios, we might need additional data sources
    if hasattr(act_params, 'activity') and 'SAV' in str(act_params.activity).upper():
        # SAV scenarios may need REPSEQ data for proper LAC configuration
        dict_netCDF = read_netCDF(
            act_params,
            satellite,
            main_files=["ACTTAB"],
            used_files=["ACTTAB", "REPSEQ"],  # Include REPSEQ for SAV scenarios
            instrument_conf=dict_config,
        )
    else:
        dict_netCDF = read_netCDF(
            act_params,
            satellite,
            main_files=["ACTTAB"],
            used_files=["ACTTAB"],
            instrument_conf=dict_config,
        )
    print(f"[DEBUG] IRS_ACT_TAB: Calling create_Stack_ACT_TAB with output path: {str_full_path}")
    # Pass SAV flag to create_Stack_ACT_TAB for special handling
    is_sav = hasattr(act_params, 'activity') and 'SAV' in str(act_params.activity).upper()
    create_Stack_ACT_TAB(
        dict_netCDF,
        satellite,
        str_full_path,
        int(act_params.activity_table_id),
        int(act_params.activity_table_slot),
        is_sav=is_sav,
        act_params=act_params
    )

def create_Stack_ACT_TAB(
    dict_netCDF: Dict[str, Any],
    satellite: str,
    str_full_path: str,
    act_tab_id: int,
    act_tab_slot: int = 1,
    is_sav: bool = False,
    act_params: ActivityParams = None
) -> None:
    """Create the activity table TC stack.
    Manual to check structure EGOS-MCS-S2K-ICD-0002
    For the Activity Table the info is in MTG-SSF-CISW-ICD-0095
    Args:
        dict_netCDF: Dictionary containing netCDF data
        satellite: Satellite identifier
        str_full_path: Full output file path
        act_tab_id: Activity table ID
        act_tab_slot: Activity table slot (1 or 2)
        is_sav: Whether this is a SAV (Sun Avoidance) scenario
        act_params: Activity parameters (needed for SAV scenarios)
    Raises:
        ValueError: If number of LAC defined is out of valid range [1-16]
    """
    # Extract info from NetCDF
    int_act_tab_id = int(dict_netCDF["activity_table_id"])

    # SAV-specific activity table ID handling
    if is_sav:
        # For SAV scenarios, use the provided activity_table_id or default to 7
        int_act_tab_id = act_tab_id if act_tab_id != 0 else 7
        logger.info(f"SAV scenario: Using activity_table_id {int_act_tab_id}")

        # Check if this is a LAC_SKIP scenario (nominal repeat_sequence_ids)
        if act_params and hasattr(act_params, 'repeat_sequence_id'):
            nominal_ids = [0, 128, 256, 16384, 16512, 16640]
            is_lac_skip = int(act_params.repeat_sequence_id) in nominal_ids
            if is_lac_skip:
                logger.info(f"SAV LAC_SKIP scenario detected for repeat_sequence_id {act_params.repeat_sequence_id}")
    else:
        # Regular activity table logic
        int_act_tab_id = 7 if int_act_tab_id == 0 else int_act_tab_id

    int_num_lac = int(dict_netCDF["nb_lac"])
    logger.info(f"Number of LAC defined: {int_num_lac}")
    lst_act_type = dict_netCDF["activity_type"]
    lst_act_param = dict_netCDF["activity_parameter"]
    lst_time_offset = dict_netCDF["activity_time_offset"]
    # Validate LAC count
    if not MIN_LAC_COUNT <= int_num_lac <= MAX_LAC_COUNT:
        raise ValueError(f"Number of LAC defined {int_num_lac} is out of range [{MIN_LAC_COUNT}-{MAX_LAC_COUNT}]")

    # SAV-specific validation and logging
    if is_sav:
        logger.info(f"SAV Activity Table Generation:")
        logger.info(f"  - Activity Table ID: {int_act_tab_id}")
        logger.info(f"  - Activity Table Slot: {act_tab_slot}")
        logger.info(f"  - Number of LACs: {int_num_lac}")
        if act_params and hasattr(act_params, 'repeat_sequence_id'):
            logger.info(f"  - Repeat Sequence ID: {act_params.repeat_sequence_id}")
        logger.info(f"  - Output Path: {str_full_path}")
    # Initialize TC Stack and checksum tracking
    tc_stack = initialize_tc_stack(satellite)
    checksum_bytes = bytearray()

    # Add activity table slot to checksum (as per notebook Binary ACTTAB.ipynb)
    checksum_bytes.append(int(act_tab_slot))

    # Get activity table name based on slot
    str_act_tab = get_activity_table_name(act_tab_slot)
    # Add activity table initialization commands
    add_activity_table_init_commands(tc_stack, str_act_tab, int_act_tab_id, int_num_lac, act_tab_slot, checksum_bytes)
    # Process each LAC
    for int_step in range(int_num_lac):
        lac_data = LACData(
            lac_id_isp=dict_netCDF['lac_id_isp'][int_step],
            lac_id=dict_netCDF['lac_id'][int_step],
            nb_dwell=dict_netCDF['nb_dwell'][int_step],
            first_dwell_id=dict_netCDF['first_dwell_id'][int_step],
            nb_activity=dict_netCDF['nb_activity'][int_step],
            activity_types=lst_act_type[int_step],
            activity_parameters=lst_act_param[int_step],
            time_offsets=lst_time_offset[int_step]
        )
        process_lac(tc_stack, str_act_tab, int_act_tab_id, lac_data, checksum_bytes, int_step)

    # Add validation commands with checksum
    add_validation_commands(tc_stack, str_act_tab, int_act_tab_id, checksum_bytes)

    # Create TC Stack Object and export
    obj_TC_Stack = floppy.MCS(tc_stack)
    obj_TC_Stack.export_TC_stack(str_full_path)

def initialize_tc_stack(satellite: str) -> List[str]:
    # 1685705431 is the generating time (could be replaced with actual timestamp)
    return [f"2|OPIT|1685705431|0|WD-MS{satellite[-1]}|0000000000|"]

def get_activity_table_name(act_tab_slot: int) -> str:
    return "ACTIVI_TA_1" if int(act_tab_slot) == 1 else "ACTIVI_TA_2"

def add_activity_table_init_commands(
    tc_stack: List[str],
    str_act_tab: str,
    int_act_tab_id: int,
    int_num_lac: int,
    act_tab_sel: int,
    checksum_bytes: bytearray
) -> None:
    """Add the activity table initialization commands to the TC stack.
    Args:
        tc_stack: The telecommand stack list to append to
        str_act_tab: Human-readable activity table name
        int_act_tab_id: Activity table ID
        int_num_lac: Number of LAC definitions
        checksum_bytes: Bytearray for checksum calculation
    """
    # Add IWP Activity Table Initialize TC (222,1)
    tc_stack.append("C|DSWC005S|1|1|0|0|0|0|0||0|0|0|3|0|1|0|0|||1||||0|1|11|")
    # Activity Table Selection
    tc_stack.append(f"DSWH09SX|0|4|2|1|{str_act_tab}|1|")
    # Note: Activity table selection is NOT included in checksum (per notebook)

    # Activity Table ID
    tc_stack.append(f"DSWH09TX|0|0|2|0|{int_act_tab_id}|1|")
    checksum_bytes.extend(struct.pack('>H', int(int_act_tab_id)))
    # Number of LAC definitions
    tc_stack.append(f"DSWH09YX|0|0|2|0|{int_num_lac}|1|")
    checksum_bytes.append(int(int_num_lac))



def process_lac(
    tc_stack: List[str],
    str_act_tab: str,
    int_act_tab_id: int,
    lac_data: LACData,
    checksum_bytes: bytearray,
    int_step: int
) -> None:
    """Process a single LAC and add its commands to the TC stack.
    Args:
        tc_stack: The telecommand stack list to append to
        str_act_tab: Human-readable activity table name
        int_act_tab_id: Activity table ID
        lac_data: LACData object containing LAC information
        checksum_bytes: Bytearray for checksum calculation
    """
    add_lac_initialization_commands(
        tc_stack, 
        str_act_tab, 
        int_act_tab_id, 
        lac_data, 
        checksum_bytes,
        int_step
    )

    # Process activities in chunks if necessary
    remaining_activities = lac_data.nb_activity
    activity_index = 0
    while remaining_activities > 0:
        # Determine how many activities to process in this batch
        batch_size = min(remaining_activities, MAX_ACTIVITIES_PER_TC)
        # For subsequent batches after the first one, add continuation commands
        if activity_index > 0:
            add_activity_continuation_commands(
                tc_stack,
                str_act_tab,
                int_act_tab_id,
                lac_data.lac_id_isp,
                activity_index,
                batch_size
            )
        # Process each activity in this batch
        for i in range(batch_size):
            current_index = activity_index + i
            add_activity_commands(
                tc_stack,
                lac_data.activity_types[current_index],
                lac_data.activity_parameters[current_index],
                lac_data.time_offsets[current_index],
                checksum_bytes
            )
        # Update counters
        activity_index += batch_size
        remaining_activities -= batch_size


def add_lac_initialization_commands(
    tc_stack: List[str],
    str_act_tab: str,
    int_act_tab_id: int,
    lac_data: LACData,
    checksum_bytes: bytearray,
    int_step: int
) -> None:
    """Add LAC initialization commands to the TC stack.
    Args:
        tc_stack: The telecommand stack list to append to
        str_act_tab: Human-readable activity table name
        int_act_tab_id: Activity table ID
        lac_data: LACData object containing LAC information
        checksum_bytes: Bytearray for checksum calculation
    """
    # Calculate number of parameters for the TC
    if lac_data.nb_activity > MAX_ACTIVITIES_PER_TC:
        int_num_parameter = 308  # 8 + 3 * 100
        int_repetition = MAX_ACTIVITIES_PER_TC
    else:
        int_repetition = lac_data.nb_activity
        int_num_parameter = 8 + 3 * int_repetition
    # Add IWP Activity Table LAC Initialize TC (222,2)
    tc_stack.append(
        f"C|DSWC005T|1|1|0|0|0|0|0||0|0|0|{int_num_parameter}|0|1|0|0|||1||||0|1|11|"
    )
    # Add parameters
    tc_stack.append(f"DSWH0A1X|0|4|2|1|{str_act_tab}|1|")
    tc_stack.append(f"DSWH0A4X|0|0|2|0|{int_act_tab_id}|1|")
    tc_stack.append(f"DSWH0AJX|0|0|2|0|{int_step + 1}|1|")
    # LAC ID
    tc_stack.append(f"DSWH0AKX|0|0|2|0|{lac_data.lac_id}|1|")
    checksum_bytes.append(int(lac_data.lac_id))
    # Number of Dwells
    tc_stack.append(f"DSWH0ALX|0|0|2|0|{lac_data.nb_dwell}|1|")
    checksum_bytes.append(int(lac_data.nb_dwell))
    # First Dwell ID
    tc_stack.append(f"DSWH0AMX|0|0|2|0|{lac_data.first_dwell_id}|1|")
    checksum_bytes.extend(struct.pack('>H', int(lac_data.first_dwell_id)))
    # Number of Activities
    tc_stack.append(f"DSWH0ANX|0|0|2|0|{lac_data.nb_activity}|1|")
    checksum_bytes.extend(struct.pack('>H', int(lac_data.nb_activity)))
    # Add parameter for number of repeated activities
    tc_stack.append(f"DSWH0AOX|0|0|2|0|{int_repetition}|1|")


def add_activity_continuation_commands(
    tc_stack: List[str],
    str_act_tab: str,
    int_act_tab_id: int,
    lac_id_isp: int,
    activity_index: int,
    batch_size: int
) -> None:
    """Add commands for continuing activity definitions beyond the first 100.
    Args:
        tc_stack: The telecommand stack list to append to
        str_act_tab: Human-readable activity table name
        int_act_tab_id: Activity table ID
        lac_id_isp: LAC ID ISP value
        activity_index: Current activity index
        batch_size: Number of activities in this batch
    """
    # Calculate the number of parameters for DSWC005U
    int_num_parameter = 5 + 3 * batch_size
    # Add IWP Activity Table activity continuation TC (222,3)
    tc_stack.append(
        f"C|DSWC005U|1|1|0|0|0|0|0||0|0|0|{int_num_parameter}|0|1|0|0|||1||||0|1|11|"
    )
    # Add parameters
    tc_stack.append(f"DSWH0ASX|0|4|2|1|{str_act_tab}|1|")
    tc_stack.append(f"DSWH0ATX|0|0|2|0|{int_act_tab_id}|1|")
    tc_stack.append(f"DSWH0AUX|0|0|2|0|{lac_id_isp}|1|")
    tc_stack.append(f"DSWH0AVX|0|0|2|0|{activity_index + 1}|1|")  # Start index is 1-based
    tc_stack.append(f"DSWH0AOX|0|0|2|0|{batch_size}|0|")


def add_activity_commands(
    tc_stack: List[str],
    activity_type: int,
    activity_param: int,
    time_offset: int,
    checksum_bytes: bytearray
) -> None:
    """Add commands for a single activity.
    Args:
        tc_stack: The telecommand stack list to append to
        activity_type: Type of activity
        activity_param: Activity parameter
        time_offset: Time offset in milliseconds
        checksum_bytes: Bytearray for checksum calculation
    """
    # Get human-readable activity type
    str_act_type = ACTIVITY_TYPE_MAP[activity_type]
    # Add activity type parameter
    tc_stack.append(f"DSWH0APX|0|4|2|1|{str_act_type}|1|")
    checksum_bytes.append(activity_type)
    # Add activity parameter
    tc_stack.append(f"DSWH0AQX|0|0|2|0|{activity_param}|1|")
    checksum_bytes.append(int(activity_param))
    # Add time offset parameter
    tc_stack.append(f"DSWH0ARX|0|0|2|0|{time_offset}|1|")
    checksum_bytes.extend(struct.pack('>I', int(time_offset))[1:]) # Only 3 bytes are used for checksum
    
    
def add_validation_commands(
    tc_stack: List[str],
    str_act_tab: str,
    int_act_tab_id: int,
    checksum_bytes: bytearray
) -> None:
    """Add validation commands with checksum to the TC stack.
    Args:
        tc_stack: The telecommand stack list to append to
        str_act_tab: Human-readable activity table name
        int_act_tab_id: Activity table ID
        checksum_bytes: Bytearray for checksum calculation
    """
    # Add IWP Activity Table Validate TC (222,4)
    tc_stack.append("C|DSWC005V|1|1|0|0|0|0|0||0|0|0|3|0|1|0|0|||1||||0|1|11|")
    # Add parameters
    tc_stack.append(f"DSWH0AWX|0|4|2|1|{str_act_tab}|1|")
    tc_stack.append(f"DSWH0AXX|0|0|2|0|{int_act_tab_id}|1|")

    # Calculate checksum using ISO algorithm (as per ECSS-E-70-41A Annex A.2)
    # This is the standard checksum for ACTTAB validation
    int_checksum = iso_checksum_int(bytes(checksum_bytes))
    logger.info(f"ISO checksum calculated: {int_checksum}")

    tc_stack.append(f"DSWH0AYX|0|0|2|0|{int_checksum}|1|")
