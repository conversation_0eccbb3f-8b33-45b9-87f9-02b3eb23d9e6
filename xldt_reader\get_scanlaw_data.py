#!/usr/bin/env python3
"""
Get the specific scan law data from NetCDF file for scan law ID 16384.
"""

import os
import sys
import numpy as np

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

try:
    import netCDF4 as nc
except ImportError:
    print("❌ netCDF4 not available")
    sys.exit(1)

def get_scanlaw_data():
    """Get scan law data for ID 16384."""
    
    netcdf_file = "../assets/Input_Files/IRS/MTS1_IRS_SCANLAW_0000_v2.nc"
    
    if not os.path.exists(netcdf_file):
        print(f"❌ NetCDF file not found: {netcdf_file}")
        return
    
    print("🔍 Getting Scan Law Data for ID 16384")
    print("=" * 50)
    
    try:
        with nc.Dataset(netcdf_file, 'r') as dataset:
            # Get scan law IDs
            scan_law_ids = dataset.variables['scan_law_id'][:]
            print(f"Available scan law IDs: {scan_law_ids}")
            
            # Find index for scan law ID 16384
            target_id = 16384
            try:
                target_index = np.where(scan_law_ids == target_id)[0][0]
                print(f"Found scan law ID {target_id} at index {target_index}")
            except IndexError:
                print(f"❌ Scan law ID {target_id} not found")
                return
            
            # Get FDA pointer data for this scan law
            fda_alpha = dataset.variables['fda_mp_pointer_alpha'][target_index, :]
            fda_epsilon = dataset.variables['fda_mp_pointer_epsilon'][target_index, :]
            
            print(f"\n📊 FDA Data for Scan Law ID {target_id}:")
            print(f"fda_mp_pointer_alpha shape: {fda_alpha.shape}")
            print(f"fda_mp_pointer_epsilon shape: {fda_epsilon.shape}")
            
            # Convert to regular arrays if masked
            if hasattr(fda_alpha, 'data'):
                fda_alpha = fda_alpha.data
            if hasattr(fda_epsilon, 'data'):
                fda_epsilon = fda_epsilon.data
            
            print(f"\n🔍 fda_mp_pointer_alpha:")
            print(f"First 50 values: {fda_alpha[:50]}")
            
            # Look for filler values (255)
            filler_positions_alpha = np.where(fda_alpha == 255)[0]
            if len(filler_positions_alpha) > 0:
                valid_length_alpha = filler_positions_alpha[0]
                print(f"Valid data length (before filler): {valid_length_alpha}")
                valid_alpha = fda_alpha[:valid_length_alpha]
                print(f"Valid fda_mp_pointer_alpha: {valid_alpha}")
            else:
                print(f"No filler values found in alpha")
                valid_alpha = fda_alpha
            
            print(f"\n🔍 fda_mp_pointer_epsilon:")
            print(f"First 50 values: {fda_epsilon[:50]}")
            
            # Look for filler values (255)
            filler_positions_epsilon = np.where(fda_epsilon == 255)[0]
            if len(filler_positions_epsilon) > 0:
                valid_length_epsilon = filler_positions_epsilon[0]
                print(f"Valid data length (before filler): {valid_length_epsilon}")
                valid_epsilon = fda_epsilon[:valid_length_epsilon]
                print(f"Valid fda_mp_pointer_epsilon: {valid_epsilon}")
            else:
                print(f"No filler values found in epsilon")
                valid_epsilon = fda_epsilon
            
            # Format as expected by user
            print(f"\n✅ Expected CDC Format:")
            print(f"fda_mp_pointer_alpha = {list(valid_alpha)}")
            print(f"fda_mp_pointer_epsilon = {list(valid_epsilon)}")
            
            # Save to file for easy copying
            with open("scanlaw_16384_data.txt", "w") as f:
                f.write(f"fda_mp_pointer_alpha = {list(valid_alpha)}\n")
                f.write(f"fda_mp_pointer_epsilon = {list(valid_epsilon)}\n")
            
            print(f"\n💾 Data saved to scanlaw_16384_data.txt")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    get_scanlaw_data()
