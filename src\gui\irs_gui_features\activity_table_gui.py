import tkinter as tk
from tkinter import ttk
from src.utils.import_utils import basics, logger
from src import functions
from ..frames import BaseFrame
from ..custom_widgets import PrimaryCard
from src.utils.activity_params import ActivityParams
from ..theme_manager import ThemeManager
from src.utils.netcdf_utils import get_netcdf_variables_value


class ACTIVITY_TABLE_GUI(BaseFrame):
    """Frame for IRS Activity Table configuration"""
    def __init__(self, parent: tk.Widget, act_params: ActivityParams, *args, **kwargs):
        """Initialize the Activity Table GUI frame."""
        self.act_params = act_params
        # Initialize widget variables
        self.var_table_slot = tk.StringVar()
        self.var_table_id = tk.StringVar()
        super().__init__(parent, *args, **kwargs)


    def create_widgets(self):
        """Create and layout GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Main frame using grid
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew") # Use grid
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Configure main layout rows/columns
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)


    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"IRS Activity Table Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew") # Use grid

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5) # Use grid


    def _create_body(self, parent):
        """Creates the body section with the input card, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)        # Configure grid to center content with appropriately sized card
        body_frame.columnconfigure(0, weight=1) # Left spacer
        body_frame.columnconfigure(1, weight=0) # Content column - fixed size, not expanding
        body_frame.columnconfigure(2, weight=1) # Right spacer
        body_frame.rowconfigure(0, weight=1) # Top spacer
        body_frame.rowconfigure(1, weight=0) # Card row
        body_frame.rowconfigure(2, weight=1) # Bottom spacer        # Activity Table Card - Scaled fonts and spacing, controlled width
        activity_table_card = PrimaryCard(body_frame, title="Activity Table Configuration", padding=0)
        activity_table_card.grid(row=1, column=1, pady=3, sticky="", ipadx=15, ipady=7) # Reduced ipadx, removed sticky="ew"

        card_content = activity_table_card.get_content_frame()        # Create frame for table selection inside the card
        frame_tab = ttk.Frame(card_content, style="PrimaryCard.TFrame")
        frame_tab.pack(fill="x", padx=10, pady=8) # Scaled 30%: padx 8->10, pady 6->8
        
        # Table slot row
        frame_slot = ttk.Frame(frame_tab, style="PrimaryCard.TFrame")
        frame_slot.pack(fill="x", pady=(0, 10)) # Scaled 30%: pady 8->10        
        ttk.Label(frame_slot, text="Activity Table Slot:", style="PrimaryCard.Large.TLabel").pack(side=tk.LEFT, padx=(0, 10))        
        slot_combo = ttk.Combobox(
            frame_slot,
            textvariable=self.var_table_slot,
            values=["1", "2"],
            width=7,  # Scaled 30%: width 5->7
            state="readonly",
            style="PrimaryCard.Large.TCombobox"
        )
        self.var_table_slot.set("1")
        slot_combo.pack(side=tk.LEFT)        # Table ID row
        frame_id = ttk.Frame(frame_tab, style="PrimaryCard.TFrame")
        frame_id.pack(fill="x")

        ttk.Label(frame_id, text="Activity Table ID:", style="PrimaryCard.Large.TLabel").pack(side=tk.LEFT, padx=(0, 10))

        #Get and sort the activity id value in the netcdf files
        activity_id_values= list(get_netcdf_variables_value('activity_table_id',self.act_params, "ACTTAB"))
        activity_id_values.sort()        
        id_combo = ttk.Combobox(
            frame_id,
            textvariable=self.var_table_id,
            values=activity_id_values,
            width=7,  # Scaled 30%: width 5->7
            state="readonly",
            style="PrimaryCard.Large.TCombobox"
        )
        id_combo.current(len(activity_id_values)-1)
        id_combo.pack(side=tk.LEFT)


    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=2)

        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5)

        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )


    def execute(self) -> None:
        """Handle execution of activity table generation with validation."""
        table_slot_val = self.var_table_slot.get()
        table_id_val = self.var_table_id.get()

        # Validation (Should not fail with readonly comboboxes with defaults)
        if not table_slot_val:
            logger.error("Activity Table Slot not selected in IRS ACTIVITY_TABLE_GUI")
            basics.pop_up_message("Error", "Activity Table Slot not selected.", "error")
            return
        if not table_id_val:
            logger.error("Activity Table ID not selected in IRS ACTIVITY_TABLE_GUI")
            basics.pop_up_message("Error", "Activity Table ID not selected.", "error")
            return

        # Update activity params
        self.act_params.activity_table_slot = table_slot_val
        self.act_params.activity_table_id = table_id_val

        logger.info(f"Generating Activity Table {self.act_params.activity_table_id} for slot {self.act_params.activity_table_slot} on satellites {self.act_params.satellites}")
        functions.generate_outputs(act_params=self.act_params)
        basics.pop_up_message("Success", "Activity Table configuration generated successfully.", "info")


    def back(self) -> None:
        """Handle back navigation"""
        self.app.back()
