"""
NetCDF utility functions for reading and processing netCDF files.
"""
import netCDF4 as nc
import io
from src.utils.data_cache import cache
from src.utils.activity_params import ActivityParams
from pathlib import Path
from src.utils.import_utils import basics, os,config
from src.logger_wrapper import logger

# Import numpy explicitly to handle the bool_ type correctly
import numpy as np

# Add a warning filter to suppress the binary incompatibility warning
import warnings
warnings.filterwarnings("ignore", message="numpy.ndarray size changed")

# Helper function to safely retrieve variable data without triggering np.bool warnings
def get_var_data(variable):
    """
    Safely retrieve data from a netCDF variable without triggering np.bool deprecation warnings.

    Args:
        variable: A netCDF variable object

    Returns:
        The variable data as a numpy array
    """
    # Suppress numpy bool deprecation warnings specifically
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*np.bool.*")

        try:
            # Enable automatic masking and scaling
            variable.set_auto_maskandscale(True)
            # Use getitem with a slice to avoid np.bool warning
            data = variable.__getitem__(slice(None))
            # Return the data array
            return data.data if hasattr(data, 'data') else data
        except Exception as e:
            logger.warning(f"Could not safely convert data: {str(e)}")
            # Fallback to raw data if conversion fails
            raw_data = variable.__getitem__(slice(None))
            return raw_data.data if hasattr(raw_data, 'data') else raw_data

def find_instrument_file(
    path: str,
    instrument: str,
    satellite: str,
    file_type: str,
    fee_id: str = "",
) -> str:
    """
    Finds the required instrument file in the given path.

    Args:
        path (str): The path to search for the file.
        instrument (str): The instrument type (e.g., FCI, LI, IRS, UVN).
        satellite (str): The satellite name.
        file_type (str): The file type.
        fee_id (str, optional): The fee ID. Defaults to "".

    Returns:
        str: The file name without extension.
    """
    # Get Satellite Family
    if instrument == "LI" or instrument == "FCI":
        sat_family = "MTI"
    else:
        sat_family = "MTS"

    # Create the expected filename
    expected_filename = f"{sat_family}{satellite[-1]}_{instrument}_{file_type}"
    logger.debug(f"Expected filename: {expected_filename}")
    
    # In case of LI we have 4 optical channels
    if instrument == "LI":
        expected_filename += f"_OC{fee_id}"

    # logger.debug(f"Searching for file: {expected_filename}")
    # Loop through all the files and directories in the path
    for obj_path in os.scandir(path):
        if not obj_path.is_file() or expected_filename not in obj_path.name:
            logger.debug(f"Skipping {obj_path.name}")
            continue
        # There should be only one fitting file
        return Path(obj_path).stem

    # Return False if not found
    basics.pop_up_message("File not found with this id", "The specified file could not be located.", "error")
    return "File Not Found"

def load_netCDF_file(file_path: str) -> dict:
    """
    Loads a netCDF file and returns its data as a dictionary.

    Args:
        file_path (str): The path to the netCDF file.

    Returns:
        dict: A dictionary containing the file's data.
    """
    file_bytes = cache.get(file_path)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {file_path}")
    with nc.Dataset('inmemory.nc', mode='r', memory=file_bytes) as obj_netfile:
        file_dict_all = {}
        for names in obj_netfile.variables.keys():
            if obj_netfile.variables[names].ndim != 0:
                var = obj_netfile.variables[names]
                file_dict_all[names] = get_var_data(var)
        return file_dict_all

def extract_data_from_file(
    file_data: dict, slice_index: int, skip_parameters: list
) -> dict:
    """
    Extracts data from a file dictionary based on a slice index and skips certain parameters.

    Args:
        file_data (dict): The file data dictionary.
        slice_index (int): The slice index.
        skip_parameters (list): A list of parameters to skip.

    Returns:
        dict: A dictionary containing the extracted data.
    """
    extracted_data = {}

    for param in file_data.keys():
        if param not in skip_parameters:
            # Dictionary with all the parameters from the file for the selected nrec
            extracted_data[param] = file_data[param][slice_index]

    return extracted_data

def find_nrec_from_input(
    file_path: str, id_param: str, slicer_1: str, slice_1: int, slicer_2: str = "", slice_2: int = 0
) -> tuple:
    """   Finds the nrec number from a file based on input parameters.  """
    logger.info(f"Finding nrec from input: {file_path}, {id_param}, {slicer_1}, {slice_1}, {slicer_2}, {slice_2}")
    file_bytes = cache.get(file_path)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {file_path}")
    with nc.Dataset('inmemory.nc', mode='r', memory=file_bytes) as obj_file:
        selector_data = get_var_data(obj_file.variables[slicer_1])
        logger.info(f"Available values for slicer '{slicer_1}' in file '{Path(file_path).name}': {selector_data}")
        slice_index_selector = []
        for idx, i in enumerate(selector_data):
            if int(i) == int(slice_1):
                slice_index_selector.append(idx)
        str_log = slicer_1
        if slicer_2 != "":
            selector_2_data = get_var_data(obj_file.variables[slicer_2])
            selector_2_list = list(selector_2_data)
            slice_index_selector_2 = [
                i for i, value in enumerate(selector_2_list) if value == slice_2
            ]
            slice_index = set(slice_index_selector) & set(slice_index_selector_2)
            str_log = f"{slicer_1} / {slicer_2} couple"
        else:
            slice_index = set(slice_index_selector)
        if len(slice_index) > 1:
            logger.error(
                f"More than one correspondence found for the selected {str_log}, ambiguous input"
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()
        elif len(slice_index) < 1:
            logger.error(f"No correspondence found for the selected {str_log}, wrong input")
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()
        else:
            slice_index = list(slice_index)[0]
            logger.info(
                f"Input found for the selected {str_log}, nrec value= {slice_index}"
            )
            logger.info(f"id_param: {id_param}")
            if id_param == "":
                common_id_value = ""
            else:
                id_data = get_var_data(obj_file.variables[id_param])
                common_id_value = id_data[slice_index]
                logger.info(f"Related {id_param} value= {common_id_value}")
        return slice_index, common_id_value

def find_nrec_from_common(file_path: str, common_id_value: str, id_param: str) -> int:
    """
    Finds the nrec number from a file based on a common ID value.

    Args:
        file_path (str): The path to the file.
        common_id_value (str): The common ID value.
        id_param (str): The ID parameter.

    Returns:
        int: The slice index.
    """
    file_bytes = cache.get(file_path)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {file_path}")
    with nc.Dataset('inmemory.nc', mode='r', memory=file_bytes) as file_load:
        selector_data = get_var_data(file_load.variables[id_param])
        id_param_selector = [i for i, value in enumerate(list(selector_data)) if value == common_id_value]
        if len(id_param_selector) > 1:
            logger.error(f"More than one correspondence found for the determined {id_param}, ambiguous input")
            basics.pop_up_message("Ouch!", "An error Occurred please check the logs.", "error")
            raise RuntimeError()
        elif len(id_param_selector) < 1:
            logger.error(f"No correspondence found for the determined {id_param}, wrong input")
            basics.pop_up_message("Ouch!", "An error Occurred please check the logs.", "error")
            raise RuntimeError()
        else:
            id_param_selector = list(id_param_selector)[0]
            logger.info(f"Input found for the determined {id_param}, nrec value= {id_param_selector}")
        return id_param_selector

def read_netCDF(
    act_params: ActivityParams,
    satellite: str,
    main_files: list,
    used_files: list,
    instrument_conf: dict,
    fee_id: str = ""
) -> dict:
    """
    Reads netCDF files and extracts data based on the provided configuration.

    Args:
        act_params: ActivityParams instance containing activity details
        satellite: Satellite identifier (may differ from act_params.satellites[0])
        main_files (list): A list of main files.
        used_files (list): A list of used files.
        instrument_conf (dict): The instrument configuration dictionary.
        fee_id (str, optional): The fee ID. Defaults to "".

    Returns:
        dict: A dictionary containing the extracted data.
    """

    # Initialize Variables
    slice_index_dict = {}
    slice_param_dict = {}


    # Loop through all required files in the input files and extract required data
    for file in main_files:
        slicers = instrument_conf["slicer_dict"][file].strip("][").split(";")
        # Search for the correct file named .nc
        file_name = find_instrument_file(
            instrument_conf["input_folder"],
            act_params.instrument,
            satellite,
            file,
            fee_id,
        )

        logger.info(f"Reading {file} file: {file_name}.nc")
        logger.info(f"Using slicers: {slicers}")

        file_path = os.path.join(instrument_conf["input_folder"], f"{file_name}.nc")

        # Special handling for INSTCONF which uses both icid and icid_ver
        if file == "INSTCONF" and len(slicers) == 2:
            # logger.debug(f"Using INSTCONF slicers with ICID={act_params.icid} and Version={act_params.icid_ver}")
            slice_index, common_id_value = find_nrec_from_input(
                file_path,
                instrument_conf["common_dict"][file],
                "icid",  # First slicer
                act_params.icid,
                "icid_ver",  # Second slicer
                act_params.icid_ver
            )
        else:
            # Normal single-slicer case
            # Get the slicer name and its value from activity_params
            slicer_name = slicers[0]
            # Retrieve the corresponding value from act_params
            if hasattr(act_params, slicer_name):
                slicer_value = getattr(act_params, slicer_name)
            else:
                logger.error(f"Slicer '{slicer_name}' not found in activity parameters")
                basics.pop_up_message("Slicer Error", f"Required slicer '{slicer_name}' not found in activity parameters", "error")
                raise AttributeError(f"Required slicer '{slicer_name}' not found in activity parameters")

            logger.info(f"Using slicer {slicer_name} with value {slicer_value}")
            slice_index, common_id_value = find_nrec_from_input(
                file_path,
                instrument_conf["common_dict"][file],
                slicer_name,
                slicer_value
            )

        slice_index_dict[file] = slice_index
        slice_param_dict[file] = slicers[0]
        logger.info(f"slice_index_dict: {slice_index_dict}")
        logger.info(f"slice_param_dict: {slice_param_dict}")

    # Process any additional required files
    for file in used_files:
        if file not in main_files:
            file_name = find_instrument_file(
                instrument_conf["input_folder"],
                act_params.instrument,
                satellite,
                file,
                fee_id,
            )

            id_selector = find_nrec_from_common(
                os.path.join(instrument_conf["input_folder"], f"{file_name}.nc"),
                common_id_value,
                instrument_conf["common_dict"][file],
            )
            slice_index_dict[file] = id_selector
            slice_param_dict[file] = instrument_conf["common_dict"][file]

    file_dict = netcdf2dict(
        used_files,
        slice_index_dict,
        slice_param_dict,
        instrument_conf["input_folder"],
        fee_id,
        act_params,
        satellite,
    )

    return file_dict

def netcdf2dict(
    files: list,
    slice_index_dict: dict,
    slice_param_dict: dict,
    folder: str,
    fee_id: str,
    act_params: ActivityParams,
    satellite: str,
) -> dict:
    """
    Loads netCDF files and extracts data based on the provided configuration.

    Args:
        files (list): A list of files.
        slice_index_dict (dict): A dictionary containing slice indices.
        slice_param_dict (dict): A dictionary containing slice parameters.
        folder (str): The input folder.
        fee_id (str): The fee ID.
        act_params: ActivityParams instance containing activity details.
        satellite: Satellite identifier (may differ from act_params.satellites[0])

    Returns:
        dict: A dictionary containing the extracted data.
    """
    # Initialize Variables
    file_dict = {}
    net_files = []
    slicers = []
    slice_indices = []
    skip_parameters = [
        "optical_channel",
        "measurement_mode",
        "creation_date",
        "ew_filename",
        "ns_filename",
        "reference_scan_law_file",
    ]
    # Not optimal at the moment but will make the generalization, faster and will be taken out soon
    for file in files:
        slice_indices.append(slice_index_dict[file])
        slicers.append(slice_param_dict[file])
        file_name = find_instrument_file(
            folder,
            act_params.instrument,
            satellite,
            file,
            fee_id,
        )

        # Add netCDF filename to list
        net_files.append(os.path.join(folder, f"{file_name}.nc"))

    for i in range(len(net_files)):
        file_dict_all = {}
        file_bytes = cache.get(net_files[i])
        if file_bytes is None:
            raise FileNotFoundError(f"File not found in cache: {net_files[i]}")
        with nc.Dataset('inmemory.nc', mode='r', memory=file_bytes) as obj_netfile:
            for names in obj_netfile.variables.keys():
                if obj_netfile.variables[names].ndim != 0:
                    if len(obj_netfile.variables[names]) == len(
                        obj_netfile.variables[slicers[i]]
                    ):
                        var = obj_netfile.variables[names]
                        file_dict_all[names] = get_var_data(var)
            for param in file_dict_all.keys():
                if param not in skip_parameters:
                    file_dict[param] = file_dict_all[param][slice_indices[i]]
    return file_dict


def get_netcdf_variables_value(
    variable_id: str, act_params: ActivityParams, main_file_pattern: str
):
    """Get the value of a netcdf variable based on the variables  id

    Args:
        variable_id (str): variable id to lookup in the netcdf file
        act_params (ActivityParams): The Activity parameter list
        main_file_pattern (str): File name pattern of the netcdf file

    Returns:
        list: Value of the variable id present in the netcdf file
    """

    instrument_conf = config.CONFIG_INSTRUMENT[act_params.instrument]

    # Get the netcdf file name
    netcdf_file_name = find_instrument_file(
        instrument_conf["input_folder"],
        act_params.instrument,
        act_params.satellites[0],
        main_file_pattern,
    )

    # Read netcdf file present in the input file
    logger.info(f"Reading {main_file_pattern} file: {netcdf_file_name}.nc")
    netcdf_file_path = os.path.join(
        instrument_conf["input_folder"], f"{netcdf_file_name}.nc"
    )
    file_bytes = cache.get(netcdf_file_path)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {netcdf_file_path}")
    with nc.Dataset('inmemory.nc', mode='r', memory=file_bytes) as netcdf_data_set:
        selected_variables_values = get_var_data(netcdf_data_set.variables[variable_id])
        logger.info(
            f"Available values for variable id : '{variable_id}' in file '{Path(netcdf_file_path).name}' are: {selected_variables_values}"
        )
        return selected_variables_values
