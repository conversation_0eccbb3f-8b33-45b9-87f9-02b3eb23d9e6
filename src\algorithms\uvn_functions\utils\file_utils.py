""" This class contains the implementation of the file operations to get the input and also set the output file.
"""
import datetime
import glob
import os

from src.algorithms.uvn_functions.utils.ptd_config_file_parser import \
    PTDConfigFileParser
from src.utils.import_utils import config

"""
Utility functions to obtain the location of the input files and also to get the location of the output files.
"""


class FileUtils:
    @staticmethod
    def get_input_file_name(instrument_name, base_input_folder=None):
        """Function to get the location of the input file."""
        input_folder_to_use = (
            base_input_folder
            if base_input_folder is not None
            else config.CONFIG_VALUES["input_folder"]
        )
        directory = os.path.join(input_folder_to_use, instrument_name)
        os.makedirs(directory, exist_ok=True)
        ptd_parser = PTDConfigFileParser()
        matching_files = glob.glob(
            os.path.join(
                directory, ptd_parser.get_ptd_property("uvn_input_file_pattern")
            )
        )

        if len(matching_files) == 0:
            return None
        else:
            return os.path.join(directory, matching_files[0])


    @staticmethod
    def set_output_fileName(satellite, act_params, lst_par, file_type):
        """Function to set the location of the output file."""
        return os.path.join(
            config.CONFIG_VALUES["output_folder"],
            satellite,
            f"MTS-{satellite[6]}_{act_params.instrument}_{lst_par[2]}_{lst_par[0]}_{lst_par[1]}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.IMG",
        )
