"""
Utility functions for number conversions and data transformations.
"""
import struct


def dec2hex(dec):
    # Check if number is positive or negative
    dec=int(dec)
    if dec >= 0:
        hexa = "{:X}".format(dec)
    else:
        hexa = hex((dec + (1 << 32)) % (1 << 32))[2:]

    return hexa


def hex2dec(hex_string):
    # To avoid that a only numerical value is consider a dec change to string
    if not isinstance(hex_string, str):
        hex_string = str(hex_string)

    return int(hex_string, 16)


def bin2dec(binary):
    return int(binary, 2)


def float_to_hex(f):
    return hex(struct.unpack(">I", struct.pack(">f", f))[0])[2:]


def dec2bin(dec):
    return "{0:b}".format(dec)