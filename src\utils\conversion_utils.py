"""
Utility functions for number conversions and data transformations.
"""
import struct
import datetime


def dec2hex(dec):
    # Check if number is positive or negative
    dec=int(dec)
    if dec >= 0:
        hexa = "{:X}".format(dec)
    else:
        hexa = hex((dec + (1 << 32)) % (1 << 32))[2:]

    return hexa


def hex2dec(hex_string):
    # To avoid that a only numerical value is consider a dec change to string
    if not isinstance(hex_string, str):
        hex_string = str(hex_string)

    return int(hex_string, 16)


def bin2dec(binary):
    return int(binary, 2)


def float_to_hex(f):
    return hex(struct.unpack(">I", struct.pack(">f", f))[0])[2:]


def dec2bin(dec):
    return "{0:b}".format(dec)


def unix_seconds_to_doy(t_ref_value):
    """Convert t_ref value (seconds since 1970-01-01) to DOY format 'YYYYTDDD'

    Args:
        t_ref_value: Unix timestamp (seconds since 1970-01-01)

    Returns:
        String in format YYYYTDDD (e.g., "2024T123")
    """
    base = datetime.datetime(1970, 1, 1)
    dt = base + datetime.timedelta(seconds=float(t_ref_value))
    return dt.strftime("%YT%j")


def iso_checksum(data: bytes) -> bytes:
    """Calculate a 16-bit ISO checksum as per Annex A.2 of [ECSS-E-70-41A].

    This is the checksum algorithm used for ACTTAB validation.

    Args:
        data: Bytes to calculate checksum for

    Returns:
        2-byte checksum as bytes object (CK1, CK2)
    """
    c0 = 0
    c1 = 0
    for byte in data:
        c0 = (c0 + byte) % 255
        c1 = (c1 + c0) % 255
    ck1 = -(c0 + c1) % 255 or 255
    ck2 = c1 or 255
    return bytes([ck1, ck2])


def iso_checksum_int(data: bytes) -> int:
    """Calculate ISO checksum and return as integer.

    Args:
        data: Bytes to calculate checksum for

    Returns:
        Checksum as 16-bit integer (big-endian)
    """
    checksum_bytes = iso_checksum(data)
    return int.from_bytes(checksum_bytes, 'big')