"""
Standalone XLDT Command Line Interface
======================================

Command-line tool for reading and analyzing XLDT files.
This is a standalone version that doesn't depend on the main codebase.
"""

import argparse
import json
import os
import sys
from xldt_reader_standalone import X<PERSON><PERSON>eader, create_xldt_file


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="XLDT File Reader - Parse and analyze XLDT binary files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Read XLDT file with configuration
  python xldt_cli_standalone.py input.bin --config config/IRS/IRS_SL_Conf.csv
  
  # Read XLDT file without configuration (generic parsing)
  python xldt_cli_standalone.py input.bin
  
  # Export to JSON
  python xldt_cli_standalone.py input.bin --output output.json --format json
  
  # Validate CRC
  python xldt_cli_standalone.py input.bin --validate-crc
  
  # Create sample XLDT file
  python xldt_cli_standalone.py --create-sample sample.bin
        """
    )
    
    parser.add_argument(
        "input_file",
        nargs="?",
        help="Path to the XLDT binary file to read"
    )
    
    parser.add_argument(
        "--config", "-c",
        help="Path to configuration CSV file (e.g., IRS_SL_Conf.csv, FCI_SL_Conf.csv)"
    )
    
    parser.add_argument(
        "--output", "-o",
        help="Output file path (default: print to stdout)"
    )
    
    parser.add_argument(
        "--format", "-f",
        choices=["json", "summary", "detailed"],
        default="summary",
        help="Output format (default: summary)"
    )
    
    parser.add_argument(
        "--validate-crc",
        action="store_true",
        help="Validate CRC checksum"
    )
    
    parser.add_argument(
        "--create-sample",
        help="Create a sample XLDT file at the specified path"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    # Configure logging
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Handle sample creation
        if args.create_sample:
            create_sample_xldt(args.create_sample)
            return
        
        # Validate input file
        if not args.input_file:
            parser.error("input_file is required unless using --create-sample")
        
        # Initialize reader
        reader = XLDTReader(config_path=args.config)
        
        # Read XLDT file
        parsed_data = reader.read_xldt_file(args.input_file)
        
        # Validate CRC if requested
        if args.validate_crc and 'CRC' in parsed_data['sections']:
            crc_valid = reader.validate_crc(
                parsed_data['raw_hex'],
                parsed_data['sections']['CRC']
            )
            parsed_data['crc_valid'] = crc_valid
        
        # Format output
        if args.format == "json":
            output = json.dumps(reader.export_to_dict(parsed_data), indent=2)
        elif args.format == "detailed":
            output = format_detailed_output(parsed_data)
        else:  # summary
            output = format_summary_output(parsed_data)
        
        # Write output
        if args.output:
            with open(args.output, 'w') as f:
                f.write(output)
            print(f"Output written to: {args.output}")
        else:
            print(output)
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


def create_sample_xldt(file_path: str):
    """Create a sample XLDT file."""
    print(f"Creating sample XLDT file: {file_path}")
    
    # Sample XLDT data with multiple sections
    # XLDT Header: 0001 (format) + 0007 (MM slot 7)
    # CRC: ABCD (dummy CRC)
    # Header section: 4 bytes (00123456)
    # Sample data sections
    sample_hex = "00010007ABCD00123456DEADBEEFCAFEBABE12345678"
    
    create_xldt_file(sample_hex, file_path)
    print(f"Sample XLDT file created: {file_path}")
    
    # Show what was created
    reader = XLDTReader()
    data = reader.read_xldt_file(file_path)
    
    print(f"File size: {data['file_info']['file_size']} bytes")
    print(f"Format ID: {data['header'].format_id}")
    print(f"MM Slot: {data['header'].mm_slot}")
    print(f"Raw hex: {data['raw_hex']}")


def format_summary_output(parsed_data):
    """Format summary output."""
    lines = []
    lines.append("XLDT File Summary")
    lines.append("=" * 50)
    
    # File info
    file_info = parsed_data.get('file_info', {})
    lines.append(f"File: {os.path.basename(file_info.get('file_path', 'Unknown'))}")
    lines.append(f"Size: {file_info.get('file_size', 0)} bytes")
    
    # Header info
    header = parsed_data.get('header')
    if header:
        lines.append(f"Format ID: {header.format_id}")
        lines.append(f"MM Slot: {header.mm_slot}")
    
    # CRC validation
    if 'crc_valid' in parsed_data:
        status = "VALID" if parsed_data['crc_valid'] else "INVALID"
        lines.append(f"CRC Status: {status}")
    
    # Sections summary
    sections = parsed_data.get('sections', {})
    lines.append(f"\nSections ({len(sections)}):")
    lines.append("-" * 30)
    
    for name, section in sections.items():
        if hasattr(section, 'length'):
            lines.append(f"  {name}: {section.length} bytes")
        elif isinstance(section, dict) and 'remaining_bytes' in section:
            lines.append(f"  Remaining data: {section['remaining_bytes']} bytes")
        else:
            lines.append(f"  {name}: {type(section).__name__}")
    
    return "\n".join(lines)


def format_detailed_output(parsed_data):
    """Format detailed output."""
    lines = []
    lines.append("XLDT File Detailed Analysis")
    lines.append("=" * 50)
    
    # File info
    file_info = parsed_data.get('file_info', {})
    lines.append("File Information:")
    for key, value in file_info.items():
        lines.append(f"  {key}: {value}")
    
    # Header
    header = parsed_data.get('header')
    if header:
        lines.append("\nXLDT Header:")
        lines.append(f"  Format ID: {header.format_id} (0x{header.format_id:04X})")
        lines.append(f"  MM Slot: {header.mm_slot} (0x{header.mm_slot:04X})")
        if header.body_length:
            lines.append(f"  Body Length: {header.body_length}")
    
    # CRC validation
    if 'crc_valid' in parsed_data:
        status = "VALID" if parsed_data['crc_valid'] else "INVALID"
        lines.append(f"\nCRC Validation: {status}")
    
    # Sections
    sections = parsed_data.get('sections', {})
    lines.append(f"\nSections ({len(sections)}):")
    lines.append("-" * 40)
    
    for name, section in sections.items():
        lines.append(f"\n{name}:")
        if hasattr(section, 'length'):
            lines.append(f"  Length: {section.length} bytes")
            lines.append(f"  Order: {section.order}")
            if section.msdf_id is not None:
                lines.append(f"  MSDF ID: {section.msdf_id}")
            
            # Show first few bytes of data
            hex_data = section.data.hex().upper()
            if len(hex_data) > 32:
                lines.append(f"  Data: {hex_data[:32]}... ({len(hex_data)} hex chars)")
            else:
                lines.append(f"  Data: {hex_data}")
        elif isinstance(section, dict):
            for key, value in section.items():
                lines.append(f"  {key}: {value}")
        else:
            lines.append(f"  Type: {type(section).__name__}")
    
    # Raw hex data (truncated)
    raw_hex = parsed_data.get('raw_hex', '')
    lines.append(f"\nRaw Hex Data:")
    if len(raw_hex) > 64:
        lines.append(f"  {raw_hex[:64]}... ({len(raw_hex)} chars total)")
    else:
        lines.append(f"  {raw_hex}")
    
    return "\n".join(lines)


if __name__ == "__main__":
    main()
