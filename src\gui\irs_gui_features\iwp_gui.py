import tkinter as tk
from tkinter import ttk
from src.utils.import_utils import basics
from src import functions
from ..frames import BaseFrame
from ..theme_manager import ThemeManager
from ..custom_widgets import PrimaryCard, SecondaryCard, AccentCard, SuccessCard
from src.logger_wrapper import logger
from src.utils.netcdf_utils import get_netcdf_variables_value

class IWP_GUI(BaseFrame):
    """Frame for IRS IWP (Instrument Work Plan) configuration"""      
    def __init__(self, parent, act_params, *args, **kwargs):
        self.act_params = act_params
        # Initialize widget variables to None, they will be created in _create_body
        self.input_apc_id = None
        self.input_scan_law_id = None
        self.input_repeat_sequence_id = None
        self.input_scae_id = None
        self.input_lut_index = None
        super().__init__(parent, *args, **kwargs)

    def create_widgets(self):
        """Create IWP GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Use grid for the main_frame within self (BaseFrame already configures row/col 0)
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure rows/columns for main_frame's internal layout
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body (expands)
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1) # Allow content to expand horizontally

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)

    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1) # Allow label/separator to fill

        ttk.Label(
            title_frame,
            text=f"IRS IWP (Instrument Work Plan) Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew") # Use grid

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5) # Use grid

    def _create_body(self, parent):
        """Creates the body section with input cards, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)        # Configure body_frame grid for 2-column layout
        body_frame.columnconfigure(0, weight=1) # Left column - equal weight
        body_frame.columnconfigure(1, weight=1) # Right column - equal weight
        body_frame.rowconfigure(0, weight=1) # Top spacer
        body_frame.rowconfigure(1, weight=0) # Row 1
        body_frame.rowconfigure(2, weight=0) # Row 2
        body_frame.rowconfigure(3, weight=0) # Row 3
        body_frame.rowconfigure(4, weight=1) # Bottom spacer        # APC ID Card - Left column, Row 1 (disabled)
        apc_card = AccentCard(body_frame, title="APC Configuration (Disabled)", padding=5)
        apc_card.grid(row=1, column=0, pady=10, padx=(0, 5), sticky="ew")
        apc_content = apc_card.get_content_frame()
        frame_apc = ttk.Frame(apc_content, style="AccentCard.TFrame")
        frame_apc.pack(anchor="center", pady=5, padx=5)
        ttk.Label(frame_apc, text="APC ID:", style="AccentCard.TLabel").pack(side=tk.LEFT, padx=5)

        self.input_apc_id = ttk.Combobox(
            frame_apc,
            width=8,
            values=["N/A"],
            state="disabled",  # Disabled as requested
            style="AccentCard.TCombobox"
        )
        self.input_apc_id.pack(side=tk.LEFT, padx=5)
        self.input_apc_id.set("N/A")        # Scan Law ID Card - Left column, Row 2
        scan_law_card = PrimaryCard(body_frame, title="Scan Law Configuration", padding=5)
        scan_law_card.grid(row=2, column=0, pady=10, padx=(0, 5), sticky="ew")
        scan_law_content = scan_law_card.get_content_frame()
        frame_scan_law = ttk.Frame(scan_law_content, style="PrimaryCard.TFrame")
        frame_scan_law.pack(anchor="center", pady=5, padx=5)
        ttk.Label(frame_scan_law, text="Scan Law ID:", style="PrimaryCard.TLabel").pack(side=tk.LEFT, padx=5)

        # Get scan law ID values from netCDF files
        try:
            scan_law_id_values = list(get_netcdf_variables_value('scan_law_id', self.act_params, "SCANLAW"))
            scan_law_id_values.sort()
        except Exception as e:
            logger.warning(f"Could not load scan law IDs: {e}")
            scan_law_id_values = ["1", "2", "3", "4", "5"] # Default values        
        
        
        self.input_scan_law_id = ttk.Combobox(
            frame_scan_law,
            width=8,
            values=scan_law_id_values,
            state="readonly",
            style="PrimaryCard.TCombobox"
        )        
        self.input_scan_law_id.pack(side=tk.LEFT, padx=5)
        if scan_law_id_values:
            self.input_scan_law_id.current(0)        # Repeat Sequence ID Card - Right column, Row 2
        repeat_sequence_card = SuccessCard(body_frame, title="Repeat Sequence Configuration", padding=5)
        repeat_sequence_card.grid(row=1, column=1, pady=10, padx=(5, 0), sticky="ew")
        repeat_sequence_content = repeat_sequence_card.get_content_frame()
        frame_repeat_seq = ttk.Frame(repeat_sequence_content, style="SuccessCard.TFrame")
        frame_repeat_seq.pack(anchor="center", pady=5, padx=5)
        ttk.Label(frame_repeat_seq, text="Repeat Sequence ID:", style="SuccessCard.TLabel").pack(side=tk.LEFT, padx=5)

        # Get repeat sequence ID values from netCDF files
        try:
            repeat_sequence_id_values = list(get_netcdf_variables_value('repeat_sequence_id', self.act_params, "REPSEQ"))
            repeat_sequence_id_values.sort()
        except Exception as e:
            logger.warning(f"Could not load repeat sequence IDs: {e}")
            repeat_sequence_id_values = ["1", "2", "3", "4", "5"] # Default values        
        self.input_repeat_sequence_id = ttk.Combobox(
            frame_repeat_seq,
            width=8,
            values=repeat_sequence_id_values,
            state="readonly",
            style="SuccessCard.TCombobox"
        )        
        self.input_repeat_sequence_id.pack(side=tk.LEFT, padx=5)
        if repeat_sequence_id_values:
            self.input_repeat_sequence_id.current(0)        # SCAE ID Card - Left column, Row 4
        scae_card = PrimaryCard(body_frame, title="SCAE Configuration", padding=5)
        scae_card.grid(row=2, column=0, pady=10, padx=(0, 5), sticky="ew")
        
        scae_content = scae_card.get_content_frame()
        frame_scae = ttk.Frame(scae_content, style="PrimaryCard.TFrame")
        frame_scae.pack(anchor="center", pady=5, padx=5)
        ttk.Label(frame_scae, text="SCAE ID:", style="PrimaryCard.TLabel").pack(side=tk.LEFT, padx=5)

        # Get SCAE ID values from netCDF files
        try:
            scae_id_values = list(get_netcdf_variables_value('scae_id', self.act_params, "SCANENC"))
            scae_id_values = [str(val) for val in scae_id_values]  # Convert to strings
            scae_id_values.sort()
        except Exception as e:
            logger.warning(f"Could not load SCAE IDs from netCDF: {e}")
            scae_id_values = ["0", "1", "2"]  # Default values

        self.input_scae_id = ttk.Combobox(
            frame_scae,
            width=8,
            values=scae_id_values,
            state="readonly",
            style="PrimaryCard.TCombobox"
        )
        self.input_scae_id.pack(side=tk.LEFT, padx=5)
        if scae_id_values:
            self.input_scae_id.current(0)  # Default to first value        # LUT Index Card - Right column, Row 3
        lut_card = SecondaryCard(body_frame, title="LUT Configuration", padding=5)
        lut_card.grid(row=2, column=1, pady=10, padx=(5, 0), sticky="ew")
        
        lut_content = lut_card.get_content_frame()
        frame_lut = ttk.Frame(lut_content, style="SecondaryCard.TFrame")
        frame_lut.pack(anchor="center", pady=5, padx=5)
        ttk.Label(frame_lut, text="LUT Index:", style="SecondaryCard.TLabel").pack(side=tk.LEFT, padx=5)

        # Get LUT Index values from netCDF files
        try:
            lut_index_values = list(get_netcdf_variables_value('scae_lut_index', self.act_params, "SCANENC"))
            lut_index_values = [str(val) for val in lut_index_values]  # Convert to strings
            lut_index_values.sort()
        except Exception as e:
            logger.warning(f"Could not load LUT Index values from netCDF: {e}")
            lut_index_values = ["1", "2", "3", "4", "5"]  # Default values

        self.input_lut_index = ttk.Combobox(
            frame_lut,
            width=8,
            values=lut_index_values,
            state="readonly",
            style="SecondaryCard.TCombobox"
        )
        self.input_lut_index.pack(side=tk.LEFT, padx=5)
        if lut_index_values:
            self.input_lut_index.current(len(lut_index_values) - 1)  # Default to last value

    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=2)

        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5)        
        ThemeManager.create_action_buttons(
            parent=controls_frame, # Pass the frame where buttons should be packed/gridded
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )

    def execute(self):
        """Handle execution of the IWP configuration with validation."""          
        # Ensure widgets are created before accessing them
        if (self.input_scan_law_id is None or 
            self.input_repeat_sequence_id is None or
            self.input_scae_id is None or
            self.input_lut_index is None):
            logger.error("Execute called before widgets created in IWP_GUI")
            basics.pop_up_message("Error", "GUI not fully initialized.", "error")
            return        
        scan_law_id_val = self.input_scan_law_id.get()
        repeat_sequence_id_val = self.input_repeat_sequence_id.get()
        scae_id_val = self.input_scae_id.get()
        lut_index_val = self.input_lut_index.get()
        # APC ID is disabled, so we don't validate it

        if not scan_law_id_val:
            basics.pop_up_message("Error", "Scan Law ID cannot be empty.", "error")
            return
        
        if not repeat_sequence_id_val:
            basics.pop_up_message("Error", "Repeat Sequence ID cannot be empty.", "error")
            return

        if not scae_id_val:
            basics.pop_up_message("Error", "SCAE ID cannot be empty.", "error")
            return

        if not lut_index_val:
            basics.pop_up_message("Error", "LUT Index cannot be empty.", "error")
            return        # Validate that values are numeric (activity_table_slot_val is already "1" or "2")
        try:
            int(scan_law_id_val)
            int(repeat_sequence_id_val)
            int(scae_id_val)
            int(lut_index_val)
        except ValueError:
            basics.pop_up_message("Error", "IDs must be numeric values.", "error")
            return        # Set the parameters in act_params
        self.act_params.scan_law_id = scan_law_id_val
        self.act_params.activity_table_id = repeat_sequence_id_val  # Map from repeat sequence
        self.act_params.repeat_sequence_id = repeat_sequence_id_val
        self.act_params.side = scae_id_val
        self.act_params.scan_encode_correction_lut_id = int(lut_index_val)
        # APC ID is not set since it's disabled

        # Generate IWP outputs
        try:
            functions.generate_outputs(act_params=self.act_params)
            basics.pop_up_message("Success", "IWP (Instrument Work Plan) outputs generated successfully.", "info")
        except Exception as e:
            logger.error(f"Error generating IWP outputs: {e}")
            basics.pop_up_message("Error", f"Failed to generate IWP outputs: {str(e)}", "error")

    def back(self):
        """Handle back navigation"""
        if self.app is not None and hasattr(self.app, "back"):
            self.app.back()
        else:
            logger.error("No app instance with 'back' method available in IWP_GUI")
            basics.pop_up_message("Navigation Error", "No previous screen to return to.", "warning")
