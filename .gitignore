# Ignore log files
*.log
logs/
*.gitkeep

# Ignore Python cache files
__pycache__/
*.py[cod]
*$py.class
logs/cdc.log
logs/cdc_error.log
config/config.ini

# vscode settings
.vscode/settings.json
.vscode/tasks.json
.vscode/*

# venv
*venv/*
*venv\*
venv.bat

# *tests/tests_assets/*
*assets/Output/

# Ignore Jupyter Notebook checkpoints
*tests/tests_assets/*/Previous_Runs*
.ipynb_checkpoints
*scanlaw_extracted*