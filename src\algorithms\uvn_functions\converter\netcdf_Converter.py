"""This class aims to convert  netcdf files to ptd  dictionary and vice versa.
"""

import netCDF4 as nc
import io
from src.utils.data_cache import cache

from .file_format_converter import FileFormatConverterInterface


class NetcdfConverter(FileFormatConverterInterface):
    """
    This is a subclass of the FileFormatConverterInterface and override the methods
    file_to_dict(self,filename_img='',memory_id='SDRAM')-> dict and
    dict_to_file(self,dict_ptd,output_file_path)
    """

    def __init__(self):
        """Instantiate a NetcdfConverter object

        Parameters
        ----------

        """

        super().__init__()

    def convert_netcdf_to_dict(self, filename_ncd=""):
        """Function to convert the content of a NetCDF file (containing groups, 1 group per PTD table) into a dictionary containing PTD tables

        :param filename_ncd: NetCDF file path, defaults to ''
        :type filename_ncd: str
        :return: dictionary containing the PTD tables read from the NetCDF file
        :rtype: dictionary
        """
        import tempfile, os
        # Read NetCDF file from cache
        file_bytes = cache.get(filename_ncd)
        if file_bytes is None:
            raise FileNotFoundError(f"File not found in cache: {filename_ncd}")
        # Write bytes to a temporary file for netCDF4 compatibility
        tmp = tempfile.NamedTemporaryFile(delete=False, suffix=".nc")
        try:
            tmp.write(file_bytes)
            tmp.close()
            nc_file = nc.Dataset(tmp.name, mode="r", format="NETCDF4")
            dict_ptd = {}
            for k_ptd in nc_file.groups:
                # Extract columns name and type from PTD layout info:
                dict_col = {}
                for var_str in self.ptd_layout[k_ptd]["def"].split(","):
                    var_name = var_str.split(":")[0]
                    var_type = var_str.split(":")[1].split("[")[0]
                    dict_col[var_name] = var_type

                dict_ptd[k_ptd] = {}
                for k_col in nc_file[k_ptd].variables.keys():
                    if dict_col[k_col] == "Float32":
                        # enforce fixed precision to prevent rounding errors
                        dict_ptd[k_ptd][k_col] = [
                            float(self.var_format.format(var))
                            for var in nc_file[k_ptd].variables[k_col][:].data
                        ]
                    else:
                        dict_ptd[k_ptd][k_col] = (
                            nc_file[k_ptd].variables[k_col][:].data.tolist()
                        )
            nc_file.close()
        finally:
            try:
                os.remove(tmp.name)
            except Exception as e:
                pass
        return dict_ptd


    def convert_dict_to_netcdf(self, dict_ptd={}, filename_ncd=""):
        """Function to convert the content of a dictionary containing PTD tables into an NetCDF file

        :param dict_ptd: Dictionary containing PTD tables, defaults to {}
        :type dict_ptd: dict, optional
        :param filename_ncd: Path of the NetCDF file to be created, defaults to ''
        :type filename_ncd: str, optional
        """

        nc_file = nc.Dataset(filename_ncd, "w", format="NETCDF4")

        for k_ptd in dict_ptd:

            N_col = len(dict_ptd[k_ptd])
            N_row = None

            # k_ptd=k_ptd.replace('-','_')
            ptd_group = nc_file.createGroup(k_ptd)
            ptd_group.setncattr("long_name", k_ptd)

            # Extract columns name and type from PTD layout info:
            lst_col = []
            for var_str in self.ptd_layout[k_ptd]["def"].split(","):
                var_name = var_str.split(":")[0]
                var_type = var_str.split(":")[1].split("[")[0]
                lst_col.append({"name": var_name, "type": var_type})

            # Copy column data from dict to NetCDF file
            for var_col in lst_col:
                k_col = var_col["name"]
                col_type = self.map_type_xls2net[var_col["type"]]

                if N_row == None:
                    N_row = len(dict_ptd[k_ptd][k_col])
                    ptd_group.createDimension("Dim", N_row)
                else:
                    assert N_row == len(dict_ptd[k_ptd][k_col])

                nc_var = ptd_group.createVariable(k_col, col_type, ("Dim",))
                s = dict_ptd[k_ptd][k_col]
                nc_var[:] = dict_ptd[k_ptd][k_col]
                # if(var_col["type"]=='Float32'):
                #    nc_var[:] = [float("{:.17e}".format(var)) for var in dict_ptd[k_ptd][k_col]]
                # else:
                #    nc_var[:] = dict_ptd[k_ptd][k_col]

        nc_file.close()
