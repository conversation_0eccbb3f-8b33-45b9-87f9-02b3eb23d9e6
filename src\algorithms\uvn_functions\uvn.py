import datetime
import math
import struct

# User Define Modules
import config
import netCDF4 as nc
import pandas as pd
import io
from src.utils.data_cache import cache
from src.utils.import_utils import basics, config
from src.logger_wrapper import logger

dict_config = config.CONFIG_INSTRUMENT["UVN"]

# ==========================================================

map_type_xls2net = {  # mapping of types from excel to netCDF
    "Uns8": "uint8",
    "Uns16": "uint16",
    "Uns32": "uint32",
    "Int32": "int32",
    "Float32": "float32",
}

map_type_xls2img = {  # mapping of types from excel to memory image
    "Uns8": ["unsigned integer", 8],
    "Uns16": ["unsigned integer", 16],
    "Uns32": ["unsigned integer", 32],
    "Int32": ["signed integer", 32],
    "Float32": ["float", 32],
}

# lst_ptd = ['7-1','7-2','7-3','7-4','7-5','7-6','7-7','7-8','7-9','3-6','2-1','2-2', '2-3', '2-4', '2-5', '2-6', '2-7', '2-8', '2-8a', '2-8b', '2-9','3-1', '3-2', '3-3', '3-4', '3-5', '4-1', '4-2','5-1', '5-2', '5-3', '5-4', '6-1', '6-2', '6-3', '6-4', '6-7', '6-8','6-9', '6-10', '6-11', '6-12', '6-13', '6-14', '6-15', '6-16', '6-17','6-18', '6-19', '6-20', '6-21', '6-22', '6-23', '6-24','6-25', '6-26', '6-27','8-1', '9-1']

# Define PTD tables layout:
# addr: start address in SDRAM
# N_pad: number of padding bytes (8bit) after table to enforce 4-Byte address aligment
# N_row: number of rows
# N_col: number of columns
# def: csv list of columns name:type[min..max]

# to allow address conversion from SDRAM to EEPROM img0/img1 addresses
PTD_start = {
    "EEPROM_img1": "0x10100000",
    "EEPROM_img0": "0x10200000",
    "SDRAM": "0x400FF810",
}
PTD_layout = {
    "2-6": {
        "title": "00_Redundancy_Definition_Table",
        "addr": "0x400FF810",
        "N_pad": 0,
        "N_row": 8,
        "N_col": 1,
        "N_len": 16,
        "def": "Value:Uns16[0..10]",
    },
    "4-1": {
        "title": "01_Mode_Transition_Table",
        "addr": "0x400FF820",
        "N_pad": 0,
        "N_row": 12,
        "N_col": 12,
        "N_len": 144,
        "def": "SBY_Allow:Uns8[0..1],SBY_Mode_Transition_Symbol:Uns8[min..max],SBR_Allow:Uns8[0..1],SBR_Mode_Transition_Symbol:Uns8[min..max],IDL_Allow:Uns8[0..1],IDL_Mode_Transition_Symbol:Uns8[min..max],IDR_Allow:Uns8[0..1],IDR_Mode_Transition_Symbol:Uns8[min..max],DCM_Allow:Uns8[0..1],DCM_Mode_Transition_Symbol:Uns8[min..max],MSM_Allow:Uns8[0..1],MSM_Mode_Transition_Symbol:Uns8[min..max]",
    },
    "4-2": {
        "title": "02_Allowed_TC_Table",
        "addr": "0x400FF8B0",
        "N_pad": 0,
        "N_row": 200,
        "N_col": 4,
        "N_len": 1600,
        "def": "TC_Type:Uns8[min..max],TC_Subtype:Uns8[min..max],Function_ID:Uns32[min..max],Mode_Word:Uns16[min..max]",
    },
    "2-7": {
        "title": "03_Critical_Command_Table",
        "addr": "0x400FFEF0",
        "N_pad": 0,
        "N_row": 20,
        "N_col": 3,
        "N_len": 120,
        "def": "TC_Type:Uns8[min..max],TC_Subtype:Uns8[min..max],Function_ID:Uns32[min..max]",
    },
    "5-1": {
        "title": "04_Thermal_Calibration_Coefficient_Table",
        "addr": "0x400FFF68",
        "N_pad": 0,
        "N_row": 380,
        "N_col": 3,
        "N_len": 2280,
        "def": "Coef_Set:Uns8[min..max],Coef_ID:Uns8[min..max],Coef_Value:Float32[min..max]",
    },
    "5-2": {
        "title": "05_Thermistor_Calibration_Table",
        "addr": "0x40100850",
        "N_pad": 0,
        "N_row": 76,
        "N_col": 2,
        "N_len": 152,
        "def": "Algorithm_ID:Uns8[min..max],Calib_Coef_Set:Uns8[min..max]",
    },
    "5-3": {
        "title": "06_Thermal_Control_Definition_Table",
        "addr": "0x401008E8",
        "N_pad": 0,
        "N_row": 144,
        "N_col": 14,
        "N_len": 4608,
        "def": "Thermistor_ID_1:Uns8[1..76],Thermistor_ID_2:Uns8[1..76],Thermistor_ID_3:Uns8[1..76],Assigned_Heater_Chan:Uns8[101..124],Heater_Chan_Output_Type:Uns8[1..2],Max_Heater_Power_Output:Float32[0.000..Float32_TLast],Thermistor_Input_Preproc:Uns8[1..7],Control_Law_Coef_Alpha:Float32[min..max],Control_Law_Coef_Beta:Float32[min..max],Control_Law_Coef_Gamma:Float32[min..max],Controller_Set_Point:Float32[min..max],Ctrl_Loop_Validity_Cond:Uns8[0..6],Heater_Cmd_Source:Uns8[0..1],Heater_Static_Setting:Float32[0.000..1.000]",
    },
    "5-4": {
        "title": "07_Thermistor_Resistance_Coefficients_Table",
        "addr": "0x40101AE8",
        "N_pad": 0,
        "N_row": 76,
        "N_col": 3,
        "N_len": 912,
        "def": "Coefficient0:Float32[min..max],Coefficient1:Float32[min..max],Coefficient2:Float32[min..max]",
    },
    "6-1": {
        "title": "08_CAA_General_Control_Parameters_Table",
        "addr": "0x40101E78",
        "N_pad": 0,
        "N_row": 1,
        "N_col": 11,
        "N_len": 44,
        "def": "WLSMirrorAngle:Float32[0.000..359.999],NominalDiffuserAngle:Float32[0.000..359.999],ReferenceDiffuserAngle:Float32[0.000..359.999],ThermistorAdcMin:Int32[0..2480],ThermistorAdcMax:Int32[0..2480],MovementTimeout:Uns32[0..3000],VelocityLimit:Uns32[0..1954],MotorGain:Uns32[0..64],CreepSpeed:Uns32[0..63],MaxCorrectionAngle:Float32[0.000..10.000],CorrTimePerStep:Uns32[0..3000]",
    },
    "6-2": {
        "title": "09_CAA_Resolver_Lookup_Table",
        "addr": "0x40101EA4",
        "N_pad": 0,
        "N_row": 360,
        "N_col": 4,
        "N_len": 5040,
        "def": "Data_Pair_ID:Uns16[1..360],Raw_Reading:Float32[min..max],Corrected_Reading:Float32[min..max],Resolution:Float32[min..max]",
    },
    "6-3": {
        "title": "10_CaaNominalMotionProfile",
        "addr": "0x40103254",
        "N_pad": 1,
        "N_row": 3,
        "N_col": 5,
        "N_len": 51,
        "def": "AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]",
    },
    "6-4": {
        "title": "11_CaaFastMotionProfile",
        "addr": "0x40103288",
        "N_pad": 1,
        "N_row": 3,
        "N_col": 5,
        "N_len": 51,
        "def": "AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]",
    },
    "6-7": {
        "title": "12_ACV_General_Control_Parameters_Table",
        "addr": "0x401032BC",
        "N_pad": 0,
        "N_row": 1,
        "N_col": 35,
        "N_len": 140,
        "def": "SummerClosedDefaultPos:Uns32[0..2047],SummerOpenDefaultPos:Uns32[0..2047],WinterClosedDefaultPos:Uns32[0..2047],WinterOpenDefaultPos:Uns32[0..2047],PositionLimitMax:Uns32[0..2047],PositionLimitMin:Uns32[0..2047],RecoveryVelocity:Uns32[0..2000000],CreepSpeed:Uns32[0..63],LowVelocityLimit:Uns32[0..2000000],ProfileVelocityLimit:Uns32[0..1954],ConstantVelocityLimit:Uns32[0..1954],RecoveryTimeout:Uns32[0..3000],NominalTimeout:Uns32[0..3000],EQSOLTimeout:Uns32[0..3000],NomSummerClosedSwitchPos:Uns32[0..131008],NomSummerOpenSwitchPos:Uns32[0..131008],NomWinterClosedSwitchPos:Uns32[0..131008],NomWinterOpenSwitchPos:Uns32[0..131008],RedSummerClosedSwitchPos:Uns32[0..131008],RedSummerOpenSwitchPos:Uns32[0..131008],RedWinterClosedSwitchPos:Uns32[0..131008],RedWinterOpenSwitchPos:Uns32[0..131008],ClosedPosRangeMin:Uns32[0..2047],ClosedPosRangeMax:Uns32[0..2047],OpenPosRangeMin:Uns32[0..2047],OpenPosRangeMax:Uns32[0..2047],SummerClosedTargetPos:Uns32[0..2047],SummerOpenTargetPos:Uns32[0..2047],WinterClosedTargetPos:Uns32[0..2047],WinterOpenTargetPos:Uns32[0..2047],SummerCoilResistance:Uns32[0..1999],WinterCoilResistance:Uns32[0..1999],MotorPower:Float32[0.000..19.900],ThermistorAdcMin:Int32[0..2480],ThermistorAdcMax:Int32[0..2480]",
    },
    "6-8": {
        "title": "13_AcvSummerNormalClose",
        "addr": "0x40103348",
        "N_pad": 1,
        "N_row": 3,
        "N_col": 5,
        "N_len": 51,
        "def": "AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]",
    },
    "6-9": {
        "title": "14_AcvSummerNormalOpen",
        "addr": "0x4010337C",
        "N_pad": 1,
        "N_row": 3,
        "N_col": 5,
        "N_len": 51,
        "def": "AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]",
    },
    "6-10": {
        "title": "15_AcvWinterNormalClose",
        "addr": "0x401033B0",
        "N_pad": 1,
        "N_row": 3,
        "N_col": 5,
        "N_len": 51,
        "def": "AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]",
    },
    "6-11": {
        "title": "16_AcvWinterNormalOpen",
        "addr": "0x401033E4",
        "N_pad": 1,
        "N_row": 3,
        "N_col": 5,
        "N_len": 51,
        "def": "AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]",
    },
    "6-12": {
        "title": "17_AcvSummerEmergencyClose",
        "addr": "0x40103418",
        "N_pad": 1,
        "N_row": 3,
        "N_col": 5,
        "N_len": 51,
        "def": "AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]",
    },
    "6-13": {
        "title": "18_AcvWinterEmergencyClose",
        "addr": "0x4010344C",
        "N_pad": 1,
        "N_row": 3,
        "N_col": 5,
        "N_len": 51,
        "def": "AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]",
    },
    "6-14": {
        "title": "19_Light_Sources_Control_Parameters",
        "addr": "0x40103480",
        "N_pad": 0,
        "N_row": 2,
        "N_col": 5,
        "N_len": 32,
        "def": "LedOff:Float32[0.0..20.0],LedOn:Float32[0.0..20.0],WlsOn:Uns16[0..4095],WlsDelta:Uns16[0..4095],WlsMinOnTime:Float32[0.0..3600.0]",
    },
    "6-15": {
        "title": "20_CCD_Operating_Voltages",
        "addr": "0x401034A0",
        "N_pad": 2,
        "N_row": 3,
        "N_col": 5,
        "N_len": 30,
        "def": "Clock1High:Uns16[16#0000#..16#3FFF#],Clock2High:Uns16[16#0000#..16#3FFF#],BiasOutputDrain:Uns16[16#0000#..16#3FFF#],BiasResetDrain:Uns16[16#0000#..16#3FFF#],BiasOutputGate:Uns16[16#0000#..16#3FFF#]",
    },
    "6-16": {
        "title": "21_EwAxisAcceleration",
        "addr": "0x401034C0",
        "N_pad": 0,
        "N_row": 60,
        "N_col": 2,
        "N_len": 360,
        "def": "EwAxisAcceleration_ID:Uns16[min..max],EwAxisAcceleration:Float32[0.0..Float32_TLast]",
    },
    "6-17": {
        "title": "22_EwAxisVelocity",
        "addr": "0x40103628",
        "N_pad": 0,
        "N_row": 60,
        "N_col": 2,
        "N_len": 360,
        "def": "EwAxisVelocity_ID:Uns16[min..max],EwAxisVelocity:Float32[0.0..Float32_TLast]",
    },
    "6-18": {
        "title": "23_EwAxisDeceleration",
        "addr": "0x40103790",
        "N_pad": 0,
        "N_row": 60,
        "N_col": 2,
        "N_len": 360,
        "def": "EwAxisDeceleration_ID:Uns16[min..max],EwAxisDeceleration:Float32[0.0..Float32_TLast]",
    },
    "6-19": {
        "title": "24_NsAxisAcceleration",
        "addr": "0x401038F8",
        "N_pad": 0,
        "N_row": 60,
        "N_col": 2,
        "N_len": 360,
        "def": "NsAxisAcceleration_ID:Uns16[min..max],NsAxisAcceleration:Float32[0.0..Float32_TLast]",
    },
    "6-20": {
        "title": "25_NsAxisVelocity",
        "addr": "0x40103A60",
        "N_pad": 0,
        "N_row": 60,
        "N_col": 2,
        "N_len": 360,
        "def": "NsAxisVelocity_ID:Uns16[min..max],NsAxisVelocity:Float32[0.0..Float32_TLast]",
    },
    "6-21": {
        "title": "26_NsAxisDeceleration",
        "addr": "0x40103BC8",
        "N_pad": 0,
        "N_row": 60,
        "N_col": 2,
        "N_len": 360,
        "def": "NsAxisDeceleration_ID:Uns16[min..max],NsAxisDeceleration:Float32[0.0..Float32_TLast]",
    },
    "6-22": {
        "title": "27_EwAxisAngleMapping",
        "addr": "0x40103D30",
        "N_pad": 0,
        "N_row": 500,
        "N_col": 2,
        "N_len": 3000,
        "def": "EwAxisAngleMapping_ID:Uns16[min..max],EwAxisAngleMapping_Value:Float32[min..max]",
    },
    "6-23": {
        "title": "28_NsAxisAngleMapping",
        "addr": "0x401048E8",
        "N_pad": 0,
        "N_row": 256,
        "N_col": 2,
        "N_len": 1280,
        "def": "NsAxisAngleMapping_ID:Uns8[min..max],NsAxisAngleMapping_Value:Float32[min..max]",
    },
    "6-24": {
        "title": "29_EwDeltaAngle",
        "addr": "0x40104DE8",
        "N_pad": 0,
        "N_row": 16,
        "N_col": 2,
        "N_len": 96,
        "def": "EwDeltaAngle_Delta_Angle_ID:Uns16[min..max],EwDeltaAngle_Delta_Angle_Value:Float32[min..max]",
    },
    "6-25": {
        "title": "30_NsDeltaAngle",
        "addr": "0x40104E48",
        "N_pad": 0,
        "N_row": 16,
        "N_col": 2,
        "N_len": 96,
        "def": "NsDeltaAngle_Delta_Angle_ID:Uns16[min..max],NsDeltaAngle_Delta_Angle_Value:Float32[min..max]",
    },
    "6-26": {
        "title": "31_RawDataValidityCheckLimits",
        "addr": "0x40104EA8",
        "N_pad": 0,
        "N_row": 6,
        "N_col": 1,
        "N_len": 12,
        "def": "Value:Uns16[min..max]",
    },
    "6-27": {
        "title": "32_SDE_Alarms_Configuration_Table",
        "addr": "0x40104EB4",
        "N_pad": 3,
        "N_row": 5,
        "N_col": 3,
        "N_len": 25,
        "def": "Configuration_ID:Uns8[min..max],Mask_Word:Uns16[min..max],Enable_Disable_Word:Uns16[min..max]",
    },
    "7-1": {
        "title": "33_ICID_Configurator_Table",
        "addr": "0x40104ED0",
        "N_pad": 0,
        "N_row": 1000,
        "N_col": 10,
        "N_len": 14000,
        "def": "ICID_No:Uns16[min..max],ICID_Ver:Uns16[min..max],PSN1:Uns16[min..max],PSN2:Uns8[min..max],PSN3:Uns8[min..max],PSN4:Uns8[min..max],PSN5:Uns8[min..max],PSN6:Uns16[min..max],PSN7:Uns8[min..max],PSN8:Uns8[min..max]",
    },
    "7-2": {
        "title": "34_FPS_Electronics_State_Parameters",
        "addr": "0x40108580",
        "N_pad": 0,
        "N_row": 100,
        "N_col": 8,
        "N_len": 1500,
        "def": "PSN:Uns16[min..max],Usage:Uns8[min..max],Frame_Period_Duration_UVVIS1:Uns16[min..max],Frame_Period_Duration_UVVIS2:Uns16[min..max],Frame_Period_Duration_NIR:Uns16[min..max],UVVIS1_Usage:Uns16[min..max],UVVIS2_Usage:Uns16[min..max],NIR_Usage:Uns16[min..max]",
    },
    "7-3": {
        "title": "35_NIR_Binning_Zones_Definition_Table",
        "addr": "0x40108B5C",
        "N_pad": 0,
        "N_row": 100,
        "N_col": 13,
        "N_len": 2500,
        "def": "PSN:Uns8[min..max],Binning_Line_Zone1_Start:Uns16[min..max],Binning_Line_Zone1_Rng:Uns16[min..max],Binning_Line_Zone1_Value:Uns16[min..max],Binning_Line_Zone2_Start:Uns16[min..max],Binning_Line_Zone2_Rng:Uns16[min..max],Binning_Line_Zone2_Value:Uns16[min..max],Binning_Line_Zone3_Start:Uns16[min..max],Binning_Line_Zone3_Rng:Uns16[min..max],Binning_Line_Zone3_Value:Uns16[min..max],Binning_Line_Zone4_Start:Uns16[min..max],Binning_Line_Zone4_Rng:Uns16[min..max],Binning_Line_Zone4_Value:Uns16[min..max]",
    },
    "7-4": {
        "title": "36_UVVIS1_Binning_Zones_Definition_Table",
        "addr": "0x40109520",
        "N_pad": 0,
        "N_row": 100,
        "N_col": 13,
        "N_len": 2500,
        "def": "PSN:Uns8[min..max],Binning_Line_Zone1_Start:Uns16[min..max],Binning_Line_Zone1_Rng:Uns16[min..max],Binning_Line_Zone1_Value:Uns16[min..max],Binning_Line_Zone2_Start:Uns16[min..max],Binning_Line_Zone2_Rng:Uns16[min..max],Binning_Line_Zone2_Value:Uns16[min..max],Binning_Line_Zone3_Start:Uns16[min..max],Binning_Line_Zone3_Rng:Uns16[min..max],Binning_Line_Zone3_Value:Uns16[min..max],Binning_Line_Zone4_Start:Uns16[min..max],Binning_Line_Zone4_Rng:Uns16[min..max],Binning_Line_Zone4_Value:Uns16[min..max]",
    },
    "7-5": {
        "title": "37_UVVIS2_Binning_Zones_Definition_Table",
        "addr": "0x40109EE4",
        "N_pad": 0,
        "N_row": 100,
        "N_col": 13,
        "N_len": 2500,
        "def": "PSN:Uns8[min..max],Binning_Line_Zone1_Start:Uns16[min..max],Binning_Line_Zone1_Rng:Uns16[min..max],Binning_Line_Zone1_Value:Uns16[min..max],Binning_Line_Zone2_Start:Uns16[min..max],Binning_Line_Zone2_Rng:Uns16[min..max],Binning_Line_Zone2_Value:Uns16[min..max],Binning_Line_Zone3_Start:Uns16[min..max],Binning_Line_Zone3_Rng:Uns16[min..max],Binning_Line_Zone3_Value:Uns16[min..max],Binning_Line_Zone4_Start:Uns16[min..max],Binning_Line_Zone4_Rng:Uns16[min..max],Binning_Line_Zone4_Value:Uns16[min..max]",
    },
    "7-6": {
        "title": "38_UVVIS1_Readout_Gain_Zones_Configuration_Table",
        "addr": "0x4010A8A8",
        "N_pad": 0,
        "N_row": 100,
        "N_col": 9,
        "N_len": 1700,
        "def": "PSN:Uns8[min..max],Gain_Line_Zone1_Start:Uns16[min..max],Gain_Line_Zone1_Rng:Uns16[min..max],Gain_Line_Zone2_Start:Uns16[min..max],Gain_Line_Zone2_Rng:Uns16[min..max],Gain_Line_Zone3_Start:Uns16[min..max],Gain_Line_Zone3_Rng:Uns16[min..max],Gain_Line_Zone4_Start:Uns16[min..max],Gain_Line_Zone4_Rng:Uns16[min..max]",
    },
    "7-7": {
        "title": "39_Science_Data_Processing_Parameters_Table",
        "addr": "0x4010AF4C",
        "N_pad": 0,
        "N_row": 1024,
        "N_col": 13,
        "N_len": 26624,
        "def": "PSN:Uns16[min..max],Coadding_Factor_UVVIS1:Uns8[min..max],Coadding_Factor_UVVIS2:Uns8[min..max],Coadding_Factor_NIR:Uns8[min..max],Data_Width_UVVIS1:Uns8[min..max],Data_Width_UVVIS2:Uns8[min..max],Data_Width_NIR:Uns8[min..max],Video_Size_UVVIS1:Uns32[min..max],Video_Size_UVVIS2:Uns32[min..max],Video_Size_NIR:Uns32[min..max],Exposure_Count_UVVIS1:Uns16[min..max],Exposure_Count_UVVIS2:Uns16[min..max],Exposure_Count_NIR:Uns16[min..max]",
    },
    "7-8": {
        "title": "40_Raw_Line_Transmission_Selections_Table",
        "addr": "0x4011174C",
        "N_pad": 2,
        "N_row": 50,
        "N_col": 115,
        "N_len": 22850,
        "def": "PSN:Uns8[min..max],NIR_Filter_0:Uns32[min..max],NIR_Filter_1:Uns32[min..max],NIR_Filter_2:Uns32[min..max],NIR_Filter_3:Uns32[min..max],NIR_Filter_4:Uns32[min..max],NIR_Filter_5:Uns32[min..max],NIR_Filter_6:Uns32[min..max],NIR_Filter_7:Uns32[min..max],NIR_Filter_8:Uns32[min..max],NIR_Filter_9:Uns32[min..max],NIR_Filter_10:Uns32[min..max],NIR_Filter_11:Uns32[min..max],NIR_Filter_12:Uns32[min..max],NIR_Filter_13:Uns32[min..max],NIR_Filter_14:Uns32[min..max],NIR_Filter_15:Uns32[min..max],NIR_Filter_16:Uns32[min..max],NIR_Filter_17:Uns32[min..max],NIR_Filter_18:Uns32[min..max],NIR_Filter_19:Uns32[min..max],NIR_Filter_20:Uns32[min..max],NIR_Filter_21:Uns32[min..max],NIR_Filter_22:Uns32[min..max],NIR_Filter_23:Uns32[min..max],NIR_Filter_24:Uns32[min..max],NIR_Filter_25:Uns32[min..max],NIR_Filter_26:Uns32[min..max],NIR_Filter_27:Uns32[min..max],NIR_Filter_28:Uns32[min..max],NIR_Filter_29:Uns32[min..max],NIR_Filter_30:Uns32[min..max],NIR_Filter_31:Uns32[min..max],NIR_Filter_32:Uns32[min..max],NIR_Filter_33:Uns32[min..max],NIR_Filter_34:Uns32[min..max],NIR_Filter_35:Uns32[min..max],NIR_Filter_36:Uns32[min..max],NIR_Filter_37:Uns32[min..max],UVVIS1_Filter_0:Uns32[min..max],UVVIS1_Filter_1:Uns32[min..max],UVVIS1_Filter_2:Uns32[min..max],UVVIS1_Filter_3:Uns32[min..max],UVVIS1_Filter_4:Uns32[min..max],UVVIS1_Filter_5:Uns32[min..max],UVVIS1_Filter_6:Uns32[min..max],UVVIS1_Filter_7:Uns32[min..max],UVVIS1_Filter_8:Uns32[min..max],UVVIS1_Filter_9:Uns32[min..max],UVVIS1_Filter_10:Uns32[min..max],UVVIS1_Filter_11:Uns32[min..max],UVVIS1_Filter_12:Uns32[min..max],UVVIS1_Filter_13:Uns32[min..max],UVVIS1_Filter_14:Uns32[min..max],UVVIS1_Filter_15:Uns32[min..max],UVVIS1_Filter_16:Uns32[min..max],UVVIS1_Filter_17:Uns32[min..max],UVVIS1_Filter_18:Uns32[min..max],UVVIS1_Filter_19:Uns32[min..max],UVVIS1_Filter_20:Uns32[min..max],UVVIS1_Filter_21:Uns32[min..max],UVVIS1_Filter_22:Uns32[min..max],UVVIS1_Filter_23:Uns32[min..max],UVVIS1_Filter_24:Uns32[min..max],UVVIS1_Filter_25:Uns32[min..max],UVVIS1_Filter_26:Uns32[min..max],UVVIS1_Filter_27:Uns32[min..max],UVVIS1_Filter_28:Uns32[min..max],UVVIS1_Filter_29:Uns32[min..max],UVVIS1_Filter_30:Uns32[min..max],UVVIS1_Filter_31:Uns32[min..max],UVVIS1_Filter_32:Uns32[min..max],UVVIS1_Filter_33:Uns32[min..max],UVVIS1_Filter_34:Uns32[min..max],UVVIS1_Filter_35:Uns32[min..max],UVVIS1_Filter_36:Uns32[min..max],UVVIS1_Filter_37:Uns32[min..max],UVVIS2_Filter_0:Uns32[min..max],UVVIS2_Filter_1:Uns32[min..max],UVVIS2_Filter_2:Uns32[min..max],UVVIS2_Filter_3:Uns32[min..max],UVVIS2_Filter_4:Uns32[min..max],UVVIS2_Filter_5:Uns32[min..max],UVVIS2_Filter_6:Uns32[min..max],UVVIS2_Filter_7:Uns32[min..max],UVVIS2_Filter_8:Uns32[min..max],UVVIS2_Filter_9:Uns32[min..max],UVVIS2_Filter_10:Uns32[min..max],UVVIS2_Filter_11:Uns32[min..max],UVVIS2_Filter_12:Uns32[min..max],UVVIS2_Filter_13:Uns32[min..max],UVVIS2_Filter_14:Uns32[min..max],UVVIS2_Filter_15:Uns32[min..max],UVVIS2_Filter_16:Uns32[min..max],UVVIS2_Filter_17:Uns32[min..max],UVVIS2_Filter_18:Uns32[min..max],UVVIS2_Filter_19:Uns32[min..max],UVVIS2_Filter_20:Uns32[min..max],UVVIS2_Filter_21:Uns32[min..max],UVVIS2_Filter_22:Uns32[min..max],UVVIS2_Filter_23:Uns32[min..max],UVVIS2_Filter_24:Uns32[min..max],UVVIS2_Filter_25:Uns32[min..max],UVVIS2_Filter_26:Uns32[min..max],UVVIS2_Filter_27:Uns32[min..max],UVVIS2_Filter_28:Uns32[min..max],UVVIS2_Filter_29:Uns32[min..max],UVVIS2_Filter_30:Uns32[min..max],UVVIS2_Filter_31:Uns32[min..max],UVVIS2_Filter_32:Uns32[min..max],UVVIS2_Filter_33:Uns32[min..max],UVVIS2_Filter_34:Uns32[min..max],UVVIS2_Filter_35:Uns32[min..max],UVVIS2_Filter_36:Uns32[min..max],UVVIS2_Filter_37:Uns32[min..max]",
    },
    "7-9": {
        "title": "41_Measurement_Start_Parameters_Table",
        "addr": "0x40117090",
        "N_pad": 0,
        "N_row": 100,
        "N_col": 5,
        "N_len": 500,
        "def": "PSN:Uns8[min..max],Meas_Start_Ref_Chan_Selection:Uns8[min..max],NIR_Meas_Start_Shift:Uns8[min..max],UVVIS1_Meas_Start_Shift:Uns8[min..max],UVVIS2_Meas_Start_Shift:Uns8[min..max]",
    },
    "9-1": {
        "title": "42_VASP_Configuration_Table",
        "addr": "0x40117284",
        "N_pad": 0,
        "N_row": 9,
        "N_col": 88,
        "N_len": 792,
        "def": "VASP_ID:Uns8[min..max],Selection_Cmd_1:Uns8[min..max],Selection_Cmd_2:Uns8[min..max],Selection_Cmd_3:Uns8[min..max],Selection_Cmd_4:Uns8[min..max],Selection_Cmd_5:Uns8[min..max],Selection_Cmd_6:Uns8[min..max],Selection_Cmd_7:Uns8[min..max],Config_Cmd_1:Uns8[min..max],Config_Cmd_2:Uns8[min..max],Config_Cmd_3:Uns8[min..max],Config_Cmd_4:Uns8[min..max],Config_Cmd_5:Uns8[min..max],Config_Cmd_6:Uns8[min..max],Config_Cmd_7:Uns8[min..max],Config_Cmd_8:Uns8[min..max],Config_Cmd_9:Uns8[min..max],Config_Cmd_10:Uns8[min..max],Config_Cmd_11:Uns8[min..max],Config_Cmd_12:Uns8[min..max],Config_Cmd_13:Uns8[min..max],Config_Cmd_14:Uns8[min..max],Config_Cmd_15:Uns8[min..max],Config_Cmd_16:Uns8[min..max],Config_Cmd_17:Uns8[min..max],Config_Cmd_18:Uns8[min..max],Config_Cmd_19:Uns8[min..max],Config_Cmd_20:Uns8[min..max],Config_Cmd_21:Uns8[min..max],Config_Cmd_22:Uns8[min..max],Config_Cmd_23:Uns8[min..max],Config_Cmd_24:Uns8[min..max],Config_Cmd_25:Uns8[min..max],Config_Cmd_26:Uns8[min..max],Config_Cmd_27:Uns8[min..max],Config_Cmd_28:Uns8[min..max],Config_Cmd_29:Uns8[min..max],Config_Cmd_30:Uns8[min..max],Config_Cmd_31:Uns8[min..max],Config_Cmd_32:Uns8[min..max],Config_Cmd_33:Uns8[min..max],Config_Cmd_34:Uns8[min..max],Config_Cmd_35:Uns8[min..max],Config_Cmd_36:Uns8[min..max],Config_Cmd_37:Uns8[min..max],Config_Cmd_38:Uns8[min..max],Config_Cmd_39:Uns8[min..max],Config_Cmd_40:Uns8[min..max],Config_Cmd_41:Uns8[min..max],Config_Cmd_42:Uns8[min..max],Config_Cmd_43:Uns8[min..max],Config_Cmd_44:Uns8[min..max],Config_Cmd_45:Uns8[min..max],Config_Cmd_46:Uns8[min..max],Config_Cmd_47:Uns8[min..max],Config_Cmd_48:Uns8[min..max],Config_Cmd_49:Uns8[min..max],Config_Cmd_50:Uns8[min..max],Config_Cmd_51:Uns8[min..max],Config_Cmd_52:Uns8[min..max],Config_Cmd_53:Uns8[min..max],Config_Cmd_54:Uns8[min..max],Config_Cmd_55:Uns8[min..max],Config_Cmd_56:Uns8[min..max],Config_Cmd_57:Uns8[min..max],Config_Cmd_58:Uns8[min..max],Config_Cmd_59:Uns8[min..max],Config_Cmd_60:Uns8[min..max],Config_Cmd_61:Uns8[min..max],Config_Cmd_62:Uns8[min..max],Config_Cmd_63:Uns8[min..max],Config_Cmd_64:Uns8[min..max],Config_Cmd_65:Uns8[min..max],Config_Cmd_66:Uns8[min..max],Config_Cmd_67:Uns8[min..max],Config_Cmd_68:Uns8[min..max],Config_Cmd_69:Uns8[min..max],Config_Cmd_70:Uns8[min..max],Config_Cmd_71:Uns8[min..max],Config_Cmd_72:Uns8[min..max],Config_Cmd_73:Uns8[min..max],Config_Cmd_74:Uns8[min..max],Config_Cmd_75:Uns8[min..max],Config_Cmd_76:Uns8[min..max],Config_Cmd_77:Uns8[min..max],Config_Cmd_78:Uns8[min..max],Config_Cmd_79:Uns8[min..max],Config_Cmd_80:Uns8[min..max]",
    },
    "2-9": {
        "title": "43_Util_Table",
        "addr": "0x4011759C",
        "N_pad": 0,
        "N_row": 1,
        "N_col": 20,
        "N_len": 80,
        "def": "SDE_NS_Axis_Motion_Min_Time:Uns32[0..3000],SDE_EW_Axis_Motion_Min_Time:Uns32[0..3000],Spare_02:Uns32[min..max],Spare_03:Uns32[min..max],Spare_04:Uns32[min..max],Spare_05:Uns32[min..max],Spare_06:Uns32[min..max],Spare_07:Uns32[min..max],Spare_08:Uns32[min..max],Spare_09:Uns32[min..max],Spare_10:Uns32[min..max],Spare_11:Uns32[min..max],Spare_12:Uns32[min..max],Spare_13:Uns32[min..max],Spare_14:Uns32[min..max],Spare_15:Uns32[min..max],Spare_16:Uns32[min..max],Spare_17:Uns32[min..max],Spare_18:Uns32[min..max],Spare_19:Uns32[min..max]",
    },
    "2-1": {
        "title": "44_Default_HK_Table",
        "addr": "0x401175EC",
        "N_pad": 0,
        "N_row": 100,
        "N_col": 111,
        "N_len": 43300,
        "def": "HK_SID:Uns32[min..max],Default_Coll_Interval:Uns16[min..max],Default_Generation_Status:Uns8[min..max],Gen_Cond_Operator_1:Uns8[min..max],Gen_Cond_Generation_Par_ID_1:Uns32[min..max],Gen_Cond_Mask_1:Uns32[min..max],Gen_Cond_Expected_Value_1:Uns32[min..max],Gen_Cond_Operator_2:Uns8[min..max],Gen_Cond_Generation_Par_ID_2:Uns32[min..max],Gen_Cond_Mask_2:Uns32[min..max],Gen_Cond_Expected_Value_2:Uns32[min..max],Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max]",
    },
    "2-2": {
        "title": "45_Default_Event_Reports",
        "addr": "0x40121F10",
        "N_pad": 0,
        "N_row": 1000,
        "N_col": 2,
        "N_len": 5000,
        "def": "Event_RID:Uns32[min..max],Enabled:Uns8[min..max]",
    },
    "2-3": {
        "title": "46_Default_Parameter_Monitoring_Table",
        "addr": "0x40123298",
        "N_pad": 0,
        "N_row": 550,
        "N_col": 13,
        "N_len": 25300,
        "def": "Enabled:Uns8[min..max],Monitoring_ID:Uns32[min..max],Param_ID:Uns32[min..max],Validity_Param_ID:Uns32[min..max],Validity_Mask:Uns32[min..max],Validity_Value:Uns32[min..max],Check_Interval:Uns32[min..max],Check_Repetition:Uns32[min..max],Check_Type:Uns8[min..max],Check_Def_Word_1:Uns32[min..max],Check_Def_Word_2:Uns32[min..max],Check_Def_Word_3:Uns32[min..max],Check_Def_Word_4:Uns32[min..max]",
    },
    "2-4": {
        "title": "47_Default_Event_Action_Table",
        "addr": "0x4012956C",
        "N_pad": 0,
        "N_row": 100,
        "N_col": 10,
        "N_len": 3700,
        "def": "Event_RID:Uns32[min..max],Enabled:Uns8[min..max],TC_Word_1:Uns32[min..max],TC_Word_2:Uns32[min..max],TC_Word_3:Uns32[min..max],TC_Word_4:Uns32[min..max],TC_Word_5:Uns32[min..max],TC_Word_6:Uns32[min..max],TC_Word_7:Uns32[min..max],TC_Word_8:Uns32[min..max]",
    },
    "2-5": {
        "title": "48_Default_Diagnostics_Report_Table",
        "addr": "0x4012A3E0",
        "N_pad": 0,
        "N_row": 50,
        "N_col": 105,
        "N_len": 20600,
        "def": "Is_HRDM:Uns8[min..max],Enabled:Uns8[min..max],SID:Uns32[min..max],Collection_Interval:Uns16[min..max],NREP:Uns32[min..max],Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max]",
    },
    "2-8a": {
        "title": "49_Init_And_Specific_Ancillary_Packets_Table",
        "addr": "0x4012F458",
        "N_pad": 0,
        "N_row": 6,
        "N_col": 100,
        "N_len": 2400,
        "def": "Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max]",
    },
    "2-8b": {
        "title": "50_Standard_Ancillary_Packet_Table",
        "addr": "0x4012FDB8",
        "N_pad": 2,
        "N_row": 1,
        "N_col": 258,
        "N_len": 1026,
        "def": "Gen_Cond_Operator_1:Uns8[min..max],Gen_Cond_Generation_Par_ID_1:Uns32[min..max],Gen_Cond_Mask_1:Uns32[min..max],Gen_Cond_Expected_Value_1:Uns32[min..max],Gen_Cond_Operator_2:Uns8[min..max],Gen_Cond_Generation_Par_ID_2:Uns32[min..max],Gen_Cond_Mask_2:Uns32[min..max],Gen_Cond_Expected_Value_2:Uns32[min..max],Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max],Param_ID_101:Uns32[min..max],Param_ID_102:Uns32[min..max],Param_ID_103:Uns32[min..max],Param_ID_104:Uns32[min..max],Param_ID_105:Uns32[min..max],Param_ID_106:Uns32[min..max],Param_ID_107:Uns32[min..max],Param_ID_108:Uns32[min..max],Param_ID_109:Uns32[min..max],Param_ID_110:Uns32[min..max],Param_ID_111:Uns32[min..max],Param_ID_112:Uns32[min..max],Param_ID_113:Uns32[min..max],Param_ID_114:Uns32[min..max],Param_ID_115:Uns32[min..max],Param_ID_116:Uns32[min..max],Param_ID_117:Uns32[min..max],Param_ID_118:Uns32[min..max],Param_ID_119:Uns32[min..max],Param_ID_120:Uns32[min..max],Param_ID_121:Uns32[min..max],Param_ID_122:Uns32[min..max],Param_ID_123:Uns32[min..max],Param_ID_124:Uns32[min..max],Param_ID_125:Uns32[min..max],Param_ID_126:Uns32[min..max],Param_ID_127:Uns32[min..max],Param_ID_128:Uns32[min..max],Param_ID_129:Uns32[min..max],Param_ID_130:Uns32[min..max],Param_ID_131:Uns32[min..max],Param_ID_132:Uns32[min..max],Param_ID_133:Uns32[min..max],Param_ID_134:Uns32[min..max],Param_ID_135:Uns32[min..max],Param_ID_136:Uns32[min..max],Param_ID_137:Uns32[min..max],Param_ID_138:Uns32[min..max],Param_ID_139:Uns32[min..max],Param_ID_140:Uns32[min..max],Param_ID_141:Uns32[min..max],Param_ID_142:Uns32[min..max],Param_ID_143:Uns32[min..max],Param_ID_144:Uns32[min..max],Param_ID_145:Uns32[min..max],Param_ID_146:Uns32[min..max],Param_ID_147:Uns32[min..max],Param_ID_148:Uns32[min..max],Param_ID_149:Uns32[min..max],Param_ID_150:Uns32[min..max],Param_ID_151:Uns32[min..max],Param_ID_152:Uns32[min..max],Param_ID_153:Uns32[min..max],Param_ID_154:Uns32[min..max],Param_ID_155:Uns32[min..max],Param_ID_156:Uns32[min..max],Param_ID_157:Uns32[min..max],Param_ID_158:Uns32[min..max],Param_ID_159:Uns32[min..max],Param_ID_160:Uns32[min..max],Param_ID_161:Uns32[min..max],Param_ID_162:Uns32[min..max],Param_ID_163:Uns32[min..max],Param_ID_164:Uns32[min..max],Param_ID_165:Uns32[min..max],Param_ID_166:Uns32[min..max],Param_ID_167:Uns32[min..max],Param_ID_168:Uns32[min..max],Param_ID_169:Uns32[min..max],Param_ID_170:Uns32[min..max],Param_ID_171:Uns32[min..max],Param_ID_172:Uns32[min..max],Param_ID_173:Uns32[min..max],Param_ID_174:Uns32[min..max],Param_ID_175:Uns32[min..max],Param_ID_176:Uns32[min..max],Param_ID_177:Uns32[min..max],Param_ID_178:Uns32[min..max],Param_ID_179:Uns32[min..max],Param_ID_180:Uns32[min..max],Param_ID_181:Uns32[min..max],Param_ID_182:Uns32[min..max],Param_ID_183:Uns32[min..max],Param_ID_184:Uns32[min..max],Param_ID_185:Uns32[min..max],Param_ID_186:Uns32[min..max],Param_ID_187:Uns32[min..max],Param_ID_188:Uns32[min..max],Param_ID_189:Uns32[min..max],Param_ID_190:Uns32[min..max],Param_ID_191:Uns32[min..max],Param_ID_192:Uns32[min..max],Param_ID_193:Uns32[min..max],Param_ID_194:Uns32[min..max],Param_ID_195:Uns32[min..max],Param_ID_196:Uns32[min..max],Param_ID_197:Uns32[min..max],Param_ID_198:Uns32[min..max],Param_ID_199:Uns32[min..max],Param_ID_200:Uns32[min..max],Param_ID_201:Uns32[min..max],Param_ID_202:Uns32[min..max],Param_ID_203:Uns32[min..max],Param_ID_204:Uns32[min..max],Param_ID_205:Uns32[min..max],Param_ID_206:Uns32[min..max],Param_ID_207:Uns32[min..max],Param_ID_208:Uns32[min..max],Param_ID_209:Uns32[min..max],Param_ID_210:Uns32[min..max],Param_ID_211:Uns32[min..max],Param_ID_212:Uns32[min..max],Param_ID_213:Uns32[min..max],Param_ID_214:Uns32[min..max],Param_ID_215:Uns32[min..max],Param_ID_216:Uns32[min..max],Param_ID_217:Uns32[min..max],Param_ID_218:Uns32[min..max],Param_ID_219:Uns32[min..max],Param_ID_220:Uns32[min..max],Param_ID_221:Uns32[min..max],Param_ID_222:Uns32[min..max],Param_ID_223:Uns32[min..max],Param_ID_224:Uns32[min..max],Param_ID_225:Uns32[min..max],Param_ID_226:Uns32[min..max],Param_ID_227:Uns32[min..max],Param_ID_228:Uns32[min..max],Param_ID_229:Uns32[min..max],Param_ID_230:Uns32[min..max],Param_ID_231:Uns32[min..max],Param_ID_232:Uns32[min..max],Param_ID_233:Uns32[min..max],Param_ID_234:Uns32[min..max],Param_ID_235:Uns32[min..max],Param_ID_236:Uns32[min..max],Param_ID_237:Uns32[min..max],Param_ID_238:Uns32[min..max],Param_ID_239:Uns32[min..max],Param_ID_240:Uns32[min..max],Param_ID_241:Uns32[min..max],Param_ID_242:Uns32[min..max],Param_ID_243:Uns32[min..max],Param_ID_244:Uns32[min..max],Param_ID_245:Uns32[min..max],Param_ID_246:Uns32[min..max],Param_ID_247:Uns32[min..max],Param_ID_248:Uns32[min..max],Param_ID_249:Uns32[min..max],Param_ID_250:Uns32[min..max]",
    },
    "2-8": {
        "title": "51_Diagnostic_Ancillary_Packet_Table",
        "addr": "0x401301BC",
        "N_pad": 2,
        "N_row": 50,
        "N_col": 111,
        "N_len": 21650,
        "def": "Anc_Diag_SID:Uns32[min..max],Default_Coll_Interval:Uns16[min..max],Default_Generation_Status:Uns8[min..max],Gen_Cond_Operator_1:Uns8[min..max],Gen_Cond_Generation_Par_ID_1:Uns32[min..max],Gen_Cond_Mask_1:Uns32[min..max],Gen_Cond_Expected_Value_1:Uns32[min..max],Gen_Cond_Operator_2:Uns8[min..max],Gen_Cond_Generation_Par_ID_2:Uns32[min..max],Gen_Cond_Mask_2:Uns32[min..max],Gen_Cond_Expected_Value_2:Uns32[min..max],Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max]",
    },
    "3-1": {
        "title": "52_RTCS_Table",
        "addr": "0x40135650",
        "N_pad": 0,
        "N_row": 32768,
        "N_col": 3,
        "N_len": 196608,
        "def": "Delta_Time:Uns16[min..max],Microcommand_Code:Uns16[min..max],Microcommand_Parameter:Uns16[min..max]",
    },
    "3-2": {
        "title": "53_RTCS_Start_Index_Table",
        "addr": "0x40165650",
        "N_pad": 0,
        "N_row": 256,
        "N_col": 2,
        "N_len": 768,
        "def": "RTCS_ID:Uns8[min..max],Start_Index:Uns16[min..max]",
    },
    "3-3": {
        "title": "54_Mode_Transition_RTCS_Table",
        "addr": "0x40165950",
        "N_pad": 0,
        "N_row": 256,
        "N_col": 2,
        "N_len": 512,
        "def": "Mode_Transition_Symbol:Uns8[min..max],RTCS_ID:Uns8[min..max]",
    },
    "3-4": {
        "title": "55_Measurement_Sequence_Table",
        "addr": "0x40165B50",
        "N_pad": 0,
        "N_row": 32768,
        "N_col": 6,
        "N_len": 393216,
        "def": "Delta_Time:Uns32[min..max],ICID_Number:Uns16[min..max],ICID_Version:Uns16[min..max],Scanner_EW_Target_Angle_ID:Uns16[min..max],Scanner_NS_Target_Angle_ID:Uns8[min..max],Calibration_Equipment:Uns8[min..max]",
    },
    "3-5": {
        "title": "56_Measurement_Sequence_Start_Index_Table",
        "addr": "0x401C5B50",
        "N_pad": 0,
        "N_row": 512,
        "N_col": 3,
        "N_len": 2048,
        "def": "Sequence_No:Uns8[min..max],Version_No:Uns8[min..max],Start_Index:Uns16[min..max]",
    },
    "3-6": {
        "title": "57_State_RTCS_Reference_Table",
        "addr": "0x401C6350",
        "N_pad": 0,
        "N_row": 256,
        "N_col": 3,
        "N_len": 1280,
        "def": "ICID_Number:Uns16[min..max],ICID_Version:Uns16[min..max],RTCS_ID:Uns8[min..max]",
    },
    "8-1": {
        "title": "58_PTD_Version_Table",
        "addr": "0x401C6850",
        "N_pad": 4,
        "N_row": 1,
        "N_col": 10,
        "N_len": 12,
        "def": "Version_No:Uns8[min..max],Revision_No:Uns8[min..max],Branch_No:Uns8[min..max],Patch_No:Uns8[min..max],Year:Uns16[min..max],Month:Uns8[1..12],Day:Uns8[1..31],Hour:Uns8[0..23],Minute:Uns8[0..59],ISO_Checksum:Uns16[min..max]",
    },
}


def convert_dec_to_bit(val_dec=0.0, len_bit=16, type="integer", flag_hex=False):
    """Function to convert from decimal to bitstring or hexstring
    :param val_dec: Value of the input decimal value to be converted to bit/hexstring, defaults to 0.0
    :type val_dec: float, optional
    :param len_bit: bit-length of the input decimal value, defaults to 16. Value will be overriden by 32/64 for type='float'/'double' respectively.
    :type len_bit: int, optional
    :param type: type of the input decimal value, defaults to 'integer'
    :type type: enum['integer','unsigned integer','enumerated','signed integer','float','double'], optional
    :param flag_hex: Function will return hexstring if True, else bitstring, defaults to False
    :type flag_hex: bool, optional
    :return: bitstring or hexstring (depending on flag_hex)
    :rtype: str
    """
    if type in ["unsigned integer", "integer", "enumerated"]:
        # if(val_dec<0 or 2**len_bit-1<val_dec):
        if val_dec < 0:
            raise Exception(
                'Cannot convert to integer of length "len_bit":'
                + str(len_bit)
                + ' "val_dec":'
                + str(val_dec)
            )
        elif len_bit < math.log(int(val_dec) + 1) / math.log(2):
            raise Exception(
                'Cannot convert to integer of length "len_bit":'
                + str(len_bit)
                + ' "val_dec":'
                + str(val_dec)
            )
        dat_bit = format(int(val_dec), "b").zfill(len_bit)
        flag_hex_ = False
    elif type in ["signed integer"]:
        # if(val_dec<-2**(len_bit-1) or 2**(len_bit-1)-1<val_dec):
        if int(val_dec) == 0:
            dat_bit = "0" + format(0, "b").zfill(len_bit - 1)
        elif (val_dec < 0 and math.log(abs(val_dec)) / math.log(2) > (len_bit - 1)) or (
            val_dec >= 0 and (len_bit - 1) < math.log(int(val_dec) + 1) / math.log(2)
        ):
            raise Exception(
                'Cannot convert to signed integer of length "len_bit":'
                + str(len_bit)
                + ' "val_dec":'
                + str(val_dec)
            )
        elif val_dec >= 0:
            dat_bit = "0" + format(int(val_dec), "b").zfill(len_bit - 1)
        else:
            dat_bit = "1" + format(int(val_dec) + 2 ** (len_bit - 1), "b").zfill(
                len_bit - 1
            )
        flag_hex_ = False
    elif type in ["float"]:
        len_bit = 32
        # dat_bit=struct.pack('>f',val_dec).encode('hex').zfill(32//4)
        dat_bit = hex(struct.unpack(">I", struct.pack(">f", val_dec))[0])[2:].zfill(
            32 // 4
        )
        flag_hex_ = True
    elif type in ["double"]:
        len_bit = 64
        # dat_bit=struct.pack('>d',val_dec).encode('hex').zfill(64//4)
        dat_bit = hex(struct.unpack(">Q", struct.pack(">d", val_dec))[0])[2:].zfill(
            64 // 4
        )
        flag_hex_ = True
    elif type in ["boolean"]:
        if val_dec == True:
            val_dec_ = 1
        elif val_dec == False:
            val_dec_ = 0
        else:
            raise Exception(
                'Invalid argument value "val_dec"='
                + str(val_dec)
                + ' for "type"="boolean"'
            )
        dat_bit = format(int(val_dec_), "b").zfill(len_bit)
        flag_hex_ = False
    else:
        raise Exception('Unsupported "type":' + str(type))

    if flag_hex and not flag_hex_:
        if len_bit % 8 != 0:
            raise Exception('Cannot convert to hex for "len_bit":' + str(len_bit))
        dat_bit = format(int(dat_bit, 2), "x").zfill(len_bit // 4)
        if len(dat_bit) != len_bit // 4:
            raise Exception(
                'Ouput "dat_bit":'
                + str(dat_bit)
                + ' has not a length of "len_bit/4":'
                + str(len_bit // 4)
            )
    elif not flag_hex and flag_hex_:
        dat_bit = format(int(dat_bit, 16), "b").zfill(len_bit)
        if len(dat_bit) != len_bit:
            raise Exception(
                'Ouput "dat_bit":'
                + str(dat_bit)
                + ' has not a length of "len_bit":'
                + str(len_bit)
            )

    return dat_bit


def convert_bit_to_dec(dat_bit="", type="integer", flag_hex=False):
    """Function to convert from bitstring or hexstring to decimal
    :param dat_bit: ASCII bitstring or hexstring to be interpreted (NB: bit/hex strings are not expected to have prepended '0x'/'0b'), defaults to ''
    :type dat_bit: str, optional
    :param type: type of the input bit/hex-string to be interpreted as decimal, defaults to 'integer'
    :type type: enum['integer','unsigned integer','enumerated','signed integer','float','double'], optional
    :param flag_hex: If True argument dat_bit is an hexstring, else it is a bitstring, defaults to False
    :type flag_hex: bool, optional
    :return: Decimal value interpreted from bit/hex-string input
    :rtype: boolean, integer, float, double or string
    """

    if type in ["boolean"]:
        if flag_hex:
            val_dec = int(dat_bit, 16)
        else:
            val_dec = int(dat_bit, 2)
        if val_dec == 0:
            val_dec = False
        elif val_dec == 1:
            val_dec = True
        else:
            raise Exception(
                "Cannot convert to <" + str(type) + '> from "dat_bit":' + str(dat_bit)
            )

    elif type in ["unsigned integer", "integer", "enumerated"]:
        if flag_hex:
            val_dec = int(dat_bit, 16)
        else:
            val_dec = int(dat_bit, 2)

    elif type in ["signed integer"]:
        if flag_hex:
            val_dec = int(dat_bit, 16)
            len_bit = len(dat_bit) * 4
        else:
            val_dec = int(dat_bit, 2)
            len_bit = len(dat_bit)

        if val_dec >= 2 ** (len_bit - 1):
            val_dec = val_dec - 2**len_bit

    elif type in ["float"]:
        if not flag_hex:
            if len(dat_bit) != 32:
                raise Exception(
                    "Cannot convert to <"
                    + str(type)
                    + '> from "dat_bit":'
                    + str(dat_bit)
                )
            dat_bit = format(int(dat_bit, 2), "x").zfill(len(dat_bit) // 4)
        else:
            if len(dat_bit) != 32 / 4:
                raise Exception(
                    "Cannot convert to <"
                    + str(type)
                    + '> from "dat_bit":'
                    + str(dat_bit)
                )

        # val_dec=struct.unpack('>f',dat_bit.decode('hex'))[0]
        val_dec = struct.unpack(">f", bytes.fromhex(dat_bit))[0]
        # val_dec=float("{:.7e}".format(val_dec))

        if math.isnan(val_dec):
            val_dec = "NaN"

    elif type in ["double"]:
        if not flag_hex:
            if len(dat_bit) != 64:
                raise Exception(
                    "Cannot convert to <"
                    + str(type)
                    + '> from "dat_bit":'
                    + str(dat_bit)
                )
            dat_bit = format(int(dat_bit, 2), "x").zfill(len(dat_bit) // 4)
        else:
            if len(dat_bit) != 64 / 4:
                raise Exception(
                    "Cannot convert to <"
                    + str(type)
                    + '> from "dat_bit":'
                    + str(dat_bit)
                )

        # val_dec=struct.unpack('>d',dat_bit.decode('hex'))[0]
        val_dec = struct.unpack(">d", bytes.fromhex(dat_bit))[0]
        # val_dec=float("{:.15e}".format(val_dec))

        if math.isnan(val_dec):
            val_dec = "NaN"

    else:
        raise Exception('Unsupported "type":' + str(type))
        # logger.error('[WARNING]: Conversion from bit to dec not yet supported for type:'+str(type))
        # return '<'+str(type)+'>'

    return val_dec


def convert_excel_to_dict(filename_xls=""):
    """Function to convert the content of an excel file into a dictionary containing PTD tables

    :param filename_xls: excel file path, defaults to ''
    :type filename_xls: str
    :return: dictionary containing the PTD tables read from the excel file
    :rtype: dictionary
    """
    # Read Excel file from cache
    file_bytes = cache.get(filename_xls)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {filename_xls}")
    excel_data = pd.ExcelFile(io.BytesIO(file_bytes))
    dict_ptd = {}

    for k_ptd in PTD_layout:
        df = excel_data.parse(k_ptd)
        dict_ptd[k_ptd] = {}
        for k_col in df.columns:
            dict_ptd[k_ptd][k_col] = df[k_col].tolist()

    return dict_ptd


def convert_dict_to_excel(dict_ptd={}, filename_xls=""):
    """Function to convert the content of a dictionary containing PTD tables into an excel file

    :param dict_ptd: Dictionary containing PTD tables, defaults to {}
    :type dict: dict, optional
    :param filename_xls: Path of the excel file to be created, defaults to ''
    :type filename_xls: str, optional
    """
    writer = pd.ExcelWriter(filename_xls)
    # for k_ptd in dict_ptd:
    lst_ptd = sorted([k_ptd for k_ptd in dict_ptd])
    for k_ptd in lst_ptd:
        df = pd.DataFrame.from_dict(dict_ptd[k_ptd])
        df.to_excel(writer, sheet_name=k_ptd, index=False)
    writer.close()


def convert_netcdf_to_dict(filename_ncd=""):
    """Function to convert the content of a NetCDF file (containing groups, 1 group per PTD table) into a dictionary containing PTD tables

    :param filename_ncd: NetCDF file path, defaults to ''
    :type filename_ncd: str
    :return: dictionary containing the PTD tables read from the NetCDF file
    :rtype: dictionary
    """
    # Read NetCDF file from cache
    file_bytes = cache.get(filename_ncd)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {filename_ncd}")
    nc_file = nc.Dataset(io.BytesIO(file_bytes), mode="r", format="NETCDF4")
    dict_ptd = {}
    for k_ptd in nc_file.groups:

        # Extract columns name and type from PTD layout info:
        dict_col = {}
        for var_str in PTD_layout[k_ptd]["def"].split(","):
            var_name = var_str.split(":")[0]
            var_type = var_str.split(":")[1].split("[")[0]
            dict_col[var_name] = var_type

        dict_ptd[k_ptd] = {}
        for k_col in nc_file[k_ptd].variables.keys():

            if dict_col[k_col] == "Float32":
                # enforce fixed precision to prevent rounding errors
                dict_ptd[k_ptd][k_col] = [
                    float("{:.15e}".format(var))
                    for var in nc_file[k_ptd].variables[k_col][:].data
                ]
            else:
                dict_ptd[k_ptd][k_col] = (
                    nc_file[k_ptd].variables[k_col][:].data.tolist()
                )
    nc_file.close()
    return dict_ptd


def convert_dict_to_netcdf(dict_ptd={}, filename_ncd=""):
    """Function to convert the content of a dictionary containing PTD tables into an NetCDF file

    :param dict_ptd: Dictionary containing PTD tables, defaults to {}
    :type dict_ptd: dict, optional
    :param filename_ncd: Path of the NetCDF file to be created, defaults to ''
    :type filename_ncd: str, optional
    """

    nc_file = nc.Dataset(filename_ncd, "a", format="NETCDF4")

    for k_ptd in dict_ptd:

        N_col = len(dict_ptd[k_ptd])
        N_row = None

        # k_ptd_=k_ptd.replace('-','_')
        ptd_group = nc_file.createGroup(k_ptd)
        ptd_group.setncattr("long_name", k_ptd)

        # Extract columns name and type from PTD layout info:
        lst_col = []
        for var_str in PTD_layout[k_ptd]["def"].split(","):
            var_name = var_str.split(":")[0]
            var_type = var_str.split(":")[1].split("[")[0]
            lst_col.append({"name": var_name, "type": var_type})

        # Copy column data from dict to NetCDF file
        for var_col in lst_col:
            k_col = var_col["name"]
            col_type = map_type_xls2net[var_col["type"]]

            if N_row == None:
                N_row = len(dict_ptd[k_ptd][k_col])
                ptd_group.createDimension("Dim", N_row)
            else:
                assert N_row == len(dict_ptd[k_ptd][k_col])

            nc_var = ptd_group.createVariable(k_col, col_type, ("Dim",))
            nc_var[:] = dict_ptd[k_ptd][k_col]
            # if(var_col["type"]=='Float32'):
            #    nc_var[:] = [float("{:.17e}".format(var)) for var in dict_ptd[k_ptd][k_col]]
            # else:
            #    nc_var[:] = dict_ptd[k_ptd][k_col]

    nc_file.close()


def convert_dict_to_image(dict_ptd={}, filename_img="", memory_id="SDRAM", sw_id="ASW"):
    """Function to convert the content of a dictionary containing PTD tables in to a memory image file

    :param dict_ptd: Dictionary containing PTD tables, defaults to {}
    :type dict_ptd: dict, optional
    :param filename_img: Path of the memory image file to be created, defaults to ''
    :type filename_img: str, optional
    :param memory_id: Name of the target memory device, defaults to 'SDRAM'
    :type memory_id: enum['SDRAM','EEPROM_A','EEPROM_B'], optional
    :param sw_id: Select the SW that will perform the image patching, defaults to 'ASW'
    :type sw_id: enum['ASW','SSW'], optional
    """

    lst_head_print = [
        "DOMAIN",
        "TYPE",
        "DESCRIPTION",
        "CREATIONDATE",
        "MODEL",
        "MODELVER",
        "DEVICE",
        "STARTADDR",
        "ENDADDR",
        "LENGTH",
        "CHECKSUM",
        "UNIT",
    ]

    # Initialise memory image dictionary:
    obj_image = {}
    obj_image["head"] = {}
    # dict of decimal_addr:{"START":'hex',"COUNT":0,"DATA":'hex'}
    obj_image["body"] = {}

    memory_model = "S4"
    if sw_id == "ASW":
        memory_model += "AA"
    elif sw_id == "SSW":
        memory_model += "AS"
    else:
        raise Exception('Invalid value for "SW_id"=%s' % (sw_id))
    if memory_id == "SDRAM":
        memory_model += "RAM"
    elif memory_id == "EEPROM_A":
        memory_model += "EEPA"
    elif memory_id == "EEPROM_B":
        memory_model += "EEPB"
    else:
        raise Exception('Invalid value for "memory_id"=%s' % (memory_id))
    memory_model += "_C_20230406.MMF"

    # Prepare memory image file header:
    obj_image["head"]["DOMAIN"] = "MTS1"
    obj_image["head"]["TYPE"] = "REFERENCE"
    obj_image["head"]["DESCRIPTION"] = "PTD tables memory image"
    obj_image["head"]["CREATIONDATE"] = datetime.datetime.now(
        datetime.timezone.utc
    ).strftime("%Y-%m-%dT%H:%M:%S.%f")
    obj_image["head"]["MODEL"] = memory_model
    obj_image["head"]["MODELVER"] = "1.0"
    obj_image["head"]["DEVICE"] = "S4AARAM"
    obj_image["head"]["STARTADDR"] = "TBD"  # '40000000'
    obj_image["head"]["ENDADDR"] = "TBD"  # '401C685F'
    obj_image["head"]["LENGTH"] = "TBD"  # '1331216'
    obj_image["head"]["CHECKSUM"] = "TBD"  # '7EA1'
    obj_image["head"]["UNIT"] = "1"

    # Workaround until harmonisation of netcdf files coming from IPSSR
    dict_rename = {  # from excel to netCDF
        "7-7.Exposure_Count_UVVIS1": "UVVIS1 Number-of-Exposures",
        "7-7.Exposure_Count_UVVIS2": "UVVIS2 Number-of-Exposures",
        "7-7.Exposure_Count_NIR": "NIR Number-of-Exposures",
        "3-6.ICID_Number": "ICID_No",
        "3-6.ICID_Version": "ICID_Ver",
    }

    addr_offset = int(PTD_start[memory_id][2:], 16) - int(PTD_start["SDRAM"][2:], 16)

    # Convert each PTD table into an hexstring:
    for k_ptd in PTD_layout:
        str_addr = PTD_layout[k_ptd]["addr"]
        N_pad = PTD_layout[k_ptd]["N_pad"]
        N_row = PTD_layout[k_ptd]["N_row"]
        N_col = PTD_layout[k_ptd]["N_col"]
        str_def = PTD_layout[k_ptd]["def"]

        int_addr = int(str_addr[2:], 16) + addr_offset
        str_addr_ = format(int_addr, "x").zfill(8).upper()

        assert int_addr not in obj_image["body"]
        obj_image["body"][int_addr] = {"START": str_addr_, "COUNT": 0, "DATA": ""}

        # Extract columns name and type from PTD layout info:
        lst_col = []
        for var_str in str_def.split(","):
            var_name = var_str.split(":")[0]
            var_type = var_str.split(":")[1].split("[")[0]
            lst_col.append({"name": var_name, "type": var_type})

        assert N_col == len(lst_col)

        logger.info("Reading PTD table %s ..." % (k_ptd))
        for i_row in range(N_row):
            for i_col in range(N_col):
                k_col = lst_col[i_col]["name"]
                var_type = lst_col[i_col]["type"]

                k_col_ = k_col
                # if(k_ptd+'.'+k_col_ in dict_rename):
                #    k_col_=dict_rename[k_ptd+'.'+k_col_]

                # var_cell=netcdf_object[k_ptd].variables[k_col_][:].data[i_row]
                var_cell = dict_ptd[k_ptd][k_col_][i_row]
                var_type_ = map_type_xls2img[var_type]

                var_hex = convert_dec_to_bit(
                    val_dec=var_cell,
                    len_bit=var_tyfpe_[1],
                    type=var_type_[0],
                    flag_hex=True,
                )

                assert var_type_[1] // 8 == len(var_hex) // 2

                obj_image["body"][int_addr]["COUNT"] += len(var_hex) // 2
                obj_image["body"][int_addr]["DATA"] += var_hex.upper()

        # add padding:
        obj_image["body"][int_addr]["COUNT"] += N_pad
        obj_image["body"][int_addr]["DATA"] += "00" * N_pad

        assert (
            obj_image["body"][int_addr]["COUNT"]
            == len(obj_image["body"][int_addr]["DATA"]) / 2
        )

    # Generate memory image file:
    logger.info("Processing collected data ...")
    # re-order lines if necessary
    lst_addr = sorted([var_addr for var_addr in obj_image["body"]])
    beg_addr = None
    end_addr = None
    len_addr = None
    len_line = 15  # number of bytes per img file line
    DATA = ""
    for var_addr in lst_addr:

        var_length = obj_image["body"][var_addr]["COUNT"]

        # Check that there is no gap or overlap with previous line:
        if end_addr != None:
            assert end_addr + 1 == var_addr

        # Update patch length and start/end addresses
        if beg_addr == None:
            beg_addr = var_addr
        if end_addr == None:
            end_addr = var_addr + var_length - 1
        if var_addr < beg_addr:
            beg_addr = var_addr
        if var_addr + var_length - 1 > end_addr:
            end_addr = var_addr + var_length - 1
        len_addr = end_addr - beg_addr + 1

        # concatenate patch data in a single string:
        DATA += obj_image["body"][var_addr]["DATA"]

    # Update memory image file header:
    obj_image["head"]["STARTADDR"] = convert_dec_to_bit(
        val_dec=beg_addr, len_bit=32, type="integer", flag_hex=True
    ).upper()
    obj_image["head"]["ENDADDR"] = convert_dec_to_bit(
        val_dec=end_addr, len_bit=32, type="integer", flag_hex=True
    ).upper()
    obj_image["head"]["LENGTH"] = str(len_addr)
    obj_image["head"]["CHECKSUM"] = basics.checksum(DATA, "CRC16").upper()

    # Print memory image file:
    logger.info("Writing memory image file ...")
    with open(filename_img, "w") as f_img:
        # Print file header:
        for k_field in lst_head_print:
            f_img.write(k_field + "=" + obj_image["head"][k_field] + "\n")

        # Print file body:
        # NB: field 'COUNT' to be printed as hex
        var_addr = beg_addr
        while var_addr <= end_addr:
            str_addr = convert_dec_to_bit(
                val_dec=var_addr, len_bit=32, type="integer", flag_hex=True
            ).upper()

            i_var = (var_addr - beg_addr) * 2
            str_data = DATA[i_var : (i_var + 2 * len_line)]
            str_count = hex(len(str_data) // 2)[2:].upper()
            f_img.write("START=%s,COUNT=%s,DATA=%s\n" % (str_addr, str_count, str_data))

            var_addr += len_line


def convert_image_to_dict(filename_img="", memory_id="SDRAM"):
    """Function to convert the content of memory image file into a dictionary containing PTD tables

    :param filename_img: Path of the memory image file containing PTD tables, defaults to ''
    :type filename_img: str, optional
    :param memory_id: Name of the target memory device, defaults to 'SDRAM'
    :type memory_id: enum['SDRAM','EEPROM_A','EEPROM_B'], optional
    :return: dictionary containing the PTD tables read from the memory image file
    :rtype: dictionary
    """
    dict_ptd = {}

    # Initialise memory image dictionary:
    obj_image = {}
    obj_image["head"] = {}
    # dict of decimal_addr:{"START":'hex',"COUNT":0,"DATA":'hex'}
    obj_image["body"] = {}

    lst_head_field = [
        "DOMAIN",
        "TYPE",
        "DESCRIPTION",
        "CREATIONDATE",
        "MODEL",
        "MODELVER",
        "DEVICE",
        "STARTADDR",
        "ENDADDR",
        "LENGTH",
        "CHECKSUM",
        "UNIT",
    ]

    # Load memory image file:
    logger.info("Reading memory image file ...")
    file_bytes = cache.get(filename_img)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {filename_img}")
    # Read as text lines
    lines = io.BytesIO(file_bytes).read().decode("utf-8").splitlines()
    i_line = 0
    for var_line in lines:
        i_line += 1
        if var_line.split("=")[0] in lst_head_field:
            obj_image["head"][var_line.split("=")[0]] = var_line.split("=")[1]
        elif var_line.split("=")[0] == "START":
            try:
                assert var_line.split(",")[0].split("=")[0] == "START"
                assert var_line.split(",")[1].split("=")[0] == "COUNT"
                assert var_line.split(",")[2].split("=")[0] == "DATA"
                str_addr = var_line.split(",")[0].split("=")[1]
                N_count = int(var_line.split(",")[1].split("=")[1], 16)
                hex_data = var_line.split(",")[2].split("=")[1].strip()
                int_addr = int(str_addr, 16)
                assert len(hex_data) // 2 == N_count
                obj_image["body"][int_addr] = {
                    "START": str_addr,
                    "COUNT": N_count,
                    "DATA": hex_data,
                }
            except:
                raise Exception(
                    "Memory image file contains invalid line[%s]:%s"
                    % (i_line, var_line)
                )
        else:
            raise Exception(
                "Memory image file contains invalid line[%s]:%s"
                % (i_line, var_line)
            )

    # Concatenate patch data as a single hexstring and verify there is no gap:
    DATA = ""
    lst_addr = sorted([var_addr for var_addr in obj_image["body"]])

    int_addr_ = lst_addr[0]
    for int_addr in lst_addr:

        N_count = obj_image["body"][int_addr]["COUNT"]
        str_addr = obj_image["body"][int_addr]["START"]
        hex_data = obj_image["body"][int_addr]["DATA"]

        if int_addr != int_addr_:
            raise Exception("Gap was detected before address 0x%s" % str_addr)

        int_addr_ = int_addr + N_count
        DATA += hex_data

    # Extract PTD table data:
    int_addr0 = lst_addr[0]
    addr_offset = int(PTD_start[memory_id][2:], 16) - int(PTD_start["SDRAM"][2:], 16)
    for k_ptd in PTD_layout:
        dict_ptd[k_ptd] = {}

        int_addr = int(PTD_layout[k_ptd]["addr"][2:], 16) + addr_offset
        N_len = PTD_layout[k_ptd]["N_len"]
        N_col = PTD_layout[k_ptd]["N_col"]
        N_row = PTD_layout[k_ptd]["N_row"]
        str_def = PTD_layout[k_ptd]["def"]

        # Extract columns name and type from PTD layout info, and initialise dict_ptd[k_ptd]:
        lst_col = []
        for var_str in str_def.split(","):
            var_name = var_str.split(":")[0]
            var_type = var_str.split(":")[1].split("[")[0]
            lst_col.append({"name": var_name, "type": var_type})

            dict_ptd[k_ptd][var_name] = []

        hex_data = DATA[(int_addr - int_addr0) * 2 : (int_addr - int_addr0 + N_len) * 2]

        i_byte = 0
        for i_row in range(N_row):
            for i_col in range(N_col):
                var_type = map_type_xls2img[lst_col[i_col]["type"]][0]
                len_type = map_type_xls2img[lst_col[i_col]["type"]][1]
                var_name = lst_col[i_col]["name"]
                dat_bit = hex_data[i_byte * 2 : (i_byte + len_type // 8) * 2]
                # logger.info('%s[%s,%s]=%s'%(k_ptd,i_row,i_col,dat_bit),lst_col[i_col]["type"],var_type,len_type,i_byte)

                var = convert_bit_to_dec(dat_bit=dat_bit, type=var_type, flag_hex=True)

                if var_type == "float":
                    # enforce fixed precision to prevent rounding errors
                    var = float("{:.15e}".format(var))

                dict_ptd[k_ptd][var_name].append(var)

                i_byte += len_type // 8

    return dict_ptd
