import tkinter as tk
import tkinter.font as tkFont
from tkinter import ttk
from typing import List
from src import functions
from src.utils.import_utils import basics, config
from src.utils.activity_params import ActivityParams
from ...logger_wrapper import logger
from ..frames import BaseFrame
from ..theme_manager import ThemeManager
from ..custom_widgets import SecondaryCard, AccentCard, SuccessCard

class LME_CONF_GUI(BaseFrame):
    """GUI for LI LME Configuration"""

    def __init__(self, parent: tk.Widget, act_params: ActivityParams, *args, **kwargs):
        """Initialize LME Configuration GUI frame."""
        self.act_params = act_params
        # Initialize widget variables
        self.var_Mem = tk.StringVar()
        self.lst_variables_exclude = []
        self.input_icid = None
        self.input_icid_ver = None
        super().__init__(parent, *args, **kwargs)

    def create_widgets(self):
        """Create and layout GUI components using grid."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Main frame using grid
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Configure main layout rows/columns
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)

    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"LI LME Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew")

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky='ew', pady=5)

    def _create_body(self, parent):
        """Creates the main body content area with two columns."""        
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)
        body_frame.columnconfigure(0, weight=1, minsize=250) # Left column
        body_frame.columnconfigure(1, weight=1, minsize=250) # Right column
        body_frame.rowconfigure(0, weight=1) # Allow columns to expand vertically

        self._create_left_column(body_frame)
        self._create_right_column(body_frame)

    def _create_left_column(self, parent):
        """Creates the left column widgets."""        
        left_column = ttk.Frame(parent)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        # Configure rows for vertical stacking
        left_column.rowconfigure(0, weight=0) # Memory Type Card
        left_column.rowconfigure(1, weight=0) # ICID Config Card
        left_column.rowconfigure(2, weight=1) # Spacer/Expander
        left_column.columnconfigure(0, weight=1)

        self._create_memory_type(left_column)
        self._create_icid_config(left_column) # Moved ICID config here

    def _create_right_column(self, parent):
        """Creates the right column widgets."""        
        right_column = ttk.Frame(parent)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        # Configure rows - only Exclude Params now
        right_column.rowconfigure(0, weight=0) # Exclude Params Card
        right_column.rowconfigure(1, weight=1) # Spacer/Expander (Adjusted index)
        right_column.columnconfigure(0, weight=1)

        self._create_exclude_params(right_column)

    def _create_memory_type(self, parent):
        """Creates the Memory Type selection card."""        
        mem_card = SecondaryCard(parent, title="Memory Type", padding=5)
        mem_card.grid(row=0, column=0, sticky="ew", pady=(0, 5)) # Use grid

        mem_content = mem_card.get_content_frame()
        mem_content.columnconfigure(0, weight=1) # Configure grid inside card
        lst_mems = ["RAM", "Flash", "RAM and Flash"]
        for i, mem_type in enumerate(lst_mems):
            mem_radio = ttk.Radiobutton(
                mem_content, text=mem_type, variable=self.var_Mem, value=mem_type,
                style="SecondaryCard.TRadiobutton" # Apply card style
            )
            mem_radio.grid(row=i, column=0, sticky="w", pady=1, padx=5)
        self.var_Mem.set(lst_mems[0]) # Set default

    def _create_exclude_params(self, parent):
        """Creates the Exclude Parameters card."""
        params_card = AccentCard(parent, title="Exclude Parameters", padding=5)
        params_card.grid(row=0, column=0, sticky="nsew", pady=(0, 5))

        params_content = params_card.get_content_frame()
        params_content.columnconfigure(0, weight=1)

        # --- Define smaller font and style for checkboxes ---
        small_font = None
        style_name = "Card.TCheckbutton" # Default style
        default_font = tkFont.nametofont("TkDefaultFont")
        small_font = tkFont.Font(font=default_font)
        small_font.config(size=9)

        # Define a new style based on the existing one
        style_name = "Small.Card.TCheckbutton"
        style = ttk.Style()
        # Ensure the base style exists before deriving
        # Check if base style exists before attempting to layout/configure
        try:
            style.layout(style_name, style.layout("Card.TCheckbutton"))
            # Removed incorrect style.element_options call
            style.map(style_name, **style.map("Card.TCheckbutton"))
            # Configure the new style with the smaller font
            style.configure(style_name, font=small_font)
        except tk.TclError:
             logger.warning("Could not derive from 'Card.TCheckbutton' style. Using default.")
             style_name = "Card.TCheckbutton" # Fallback to default if derivation fails

        # --- End font/style definition ---

        lst_par_name = config.CONFIG_INSTRUMENT["LI"]["par_name"]
        self.lst_variables_exclude = []
        for i, str_step in enumerate(lst_par_name):
            str_variable = tk.StringVar()
            param_cb = ttk.Checkbutton(
                params_content,
                text=str_step,
                variable=str_variable,
                onvalue=str_step,
                offvalue="",
                style=style_name # Apply the new or fallback style
            )
            param_cb.grid(row=i, column=0, sticky="w", padx=5, pady=1)
            self.lst_variables_exclude.append(str_variable)

    def _create_icid_config(self, parent):
        """Creates the ICID Configuration card."""        
        icid_card = SuccessCard(parent, title="ICID Configuration", padding=5)
        # Grid placement within the parent (which is now left_column)
        icid_card.grid(row=1, column=0, sticky="ew", pady=5) # Place in row 1

        icid_content = icid_card.get_content_frame()        # Use grid for labels and entries within the card content
        icid_content.columnconfigure(1, weight=1) # Allow entry to expand slightly        
        ttk.Label(icid_content, text="ICID:", style="SuccessCard.TLabel").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.input_icid = ttk.Entry(icid_content, width=10)
        self.input_icid.grid(row=0, column=1, padx=5, pady=2, sticky="ew")
        self.input_icid.insert(0, "1")

        ttk.Label(icid_content, text="ICID Version:", style="SuccessCard.TLabel").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.input_icid_ver = ttk.Entry(icid_content, width=10)
        self.input_icid_ver.grid(row=1, column=1, padx=5, pady=2, sticky="ew")
        self.input_icid_ver.insert(0, "1")

    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""        
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=1)

        controls_frame = ttk.Frame(bottom_frame)
        # Remove pady=5
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5) # Use row 1

        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )

    def _get_selected_check_button(self, variables: List[tk.StringVar]) -> List[str]:
        """Helper to get selected values from a list of checkbutton variables."""        
        return [var.get() for var in variables if var.get()]

    def execute(self):
        """Validate inputs and execute the LME configuration generation."""
        # Ensure other widgets are created
        if self.input_icid is None or self.input_icid_ver is None:
            logger.error("Execute called before widgets created in LME_CONF_GUI")
            basics.pop_up_message("Error", "GUI not fully initialized.", "error")
            return
        self.update_idletasks()

        # Get values selected by user
        str_mem_type = self.var_Mem.get()
        lst_exclude = self._get_selected_check_button(self.lst_variables_exclude)
        icid_val = self.input_icid.get()
        icid_ver_val = self.input_icid_ver.get()

        # --- Input Validation ---
        if not str_mem_type:
            raise ValueError("Memory Type not selected.") # Should not happen with radio buttons

        if not icid_val:
            raise ValueError("ICID cannot be empty.")
        icid_int = int(icid_val) # This can raise ValueError

        if not icid_ver_val:
            raise ValueError("ICID Version cannot be empty.")
        icid_ver_int = int(icid_ver_val) # This can raise ValueError

        # --- Execute Generation ---
        self.update_idletasks()
        # Update activity params
        self.act_params.icid = icid_int
        self.act_params.icid_ver = icid_ver_int
        self.act_params.mem_type = str_mem_type # Assuming mem_type is needed in act_params or generate_outputs
        logger.info(f"Generating LME Configuration - ICID: {icid_int}, Version: {icid_ver_int}, Mem: {str_mem_type}, Exclude: {lst_exclude}")

        functions.generate_outputs(
            act_params=self.act_params,
            str_mem_type=str_mem_type,
            lst_exclude=lst_exclude
        )

        basics.pop_up_message("Success", "LME configuration generated successfully.", "info")


    def back(self):
        """Handle back navigation"""
        self.app.back()
