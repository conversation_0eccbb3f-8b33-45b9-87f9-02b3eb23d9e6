import tkinter as tk
from tkinter import ttk

from src import functions
from src.algorithms.uvn_functions.utils.ptd_config_file_parser import \
    PTDConfigFileParser
from src.utils.activity_params import ActivityParams
from src.utils.import_utils import basics, logger

# Import the custom card widget
from ..custom_widgets import AccentCard, PrimaryCard, SecondaryCard
from ..frames import BaseFrame
from ..theme_manager import ThemeManager


class MemoryImageGenerator(BaseFrame):
    """Frame for UVN memory image"""

    def __init__(self, parent: tk.Widget, act_params: ActivityParams, *args, **kwargs):
        """Initialize the Memory image Activity GUI frame.

        Args:
            parent (tk.Widget): Tk.Widget object
            act_params (ActivityParams): List of Activity parameters
        """
        self.act_params = act_params
        self.ptd_parser = PTDConfigFileParser()

        # Initialize widget variables
        self.var_icu = tk.StringVar()
        self.var_software = tk.StringVar()
        self.var_table_id = tk.StringVar()
        self.lst_ptd_tables = []

        self.ptd_entry = None
        super().__init__(parent, *args, **kwargs)


    def create_widgets(self) -> None:
        """Create and layout GUI components using grid."""

        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Remove bottom padding: padding=5 -> padding=(5, 5, 5, 0)
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure main_frame's internal layout
        main_frame.rowconfigure(0, weight=0)  # Title
        main_frame.rowconfigure(1, weight=1)  # Body (expands)
        main_frame.rowconfigure(2, weight=0)  # Footer
        main_frame.columnconfigure(0, weight=1)  # Left column for cards
        main_frame.columnconfigure(1, weight=1)  # Right column for channel selection

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)

    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        satellites = self.act_params.satellites or []
        satellites_str = ", ".join(satellites)
        ttk.Label(
            title_frame,
            text=f"UVN Memory Image Generator - {satellites_str}",
            style="Title.TLabel",
        ).grid(
            row=0, column=0, sticky="ew"
        )  # Use grid

        ttk.Separator(title_frame, orient="horizontal").grid(
            row=1, column=0, sticky="ew", pady=5
        )  # Use grid

    def _create_body(self, parent):
        """Creates the body section with input cards, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)

        # Configure columns for the two-column layout
        body_frame.columnconfigure(0, weight=1, minsize=300)  # Left column
        body_frame.columnconfigure(1, weight=1, minsize=300)  # Right column
        body_frame.rowconfigure(0, weight=1)  # Allow columns to expand vertically

        # Left column frame
        left_column = ttk.Frame(body_frame)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        # Configure rows within left column
        left_column.rowconfigure(0, weight=0)  # Side Card
        left_column.rowconfigure(1, weight=0)  # Image Type Card
        left_column.rowconfigure(2, weight=0)  # Activity Card
        left_column.rowconfigure(3, weight=0)  # ICID Card
        left_column.rowconfigure(4, weight=1)  # ptd table card
        left_column.columnconfigure(0, weight=1)

        # Right column frame
        right_column = ttk.Frame(body_frame)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        right_column.rowconfigure(0, weight=1)
        right_column.columnconfigure(0, weight=1)

        # Populate columns
        self._create_icu_type_selection(left_column)
        self._create_software_type_selection(left_column)
        self._create_memory_type_selection(left_column)
        self._create_ptd_type_selection(right_column)

    def _create_icu_type_selection(self, parent):
        """Creates the ICU Type selection"""

        card_icu = PrimaryCard(parent, title="ICU Type selection", padding=5)
        card_icu.grid(row=0, column=0, sticky="ew", pady=5)
        content_icu = card_icu.get_content_frame()

        uvn_icu = self.ptd_parser.get_ptd_property("uvn_icu")
        for fmt in uvn_icu:
            fmt_radio = ttk.Radiobutton(
                content_icu,
                text=fmt,
                variable=self.var_icu,
                value=fmt,
                style="PrimaryCard.TRadiobutton",
            )
            fmt_radio.pack(anchor="w", pady=2, padx=5)
        self.var_icu.set(uvn_icu[0])

    def _create_software_type_selection(self, parent):
        """Create the software selection options

        Args:
            parent (_type_): Parent object
        """
        card_software = SecondaryCard(
            parent, title="Software Type selection", padding=5
        )
        card_software.grid(row=1, column=0, sticky="ew", pady=5)
        content_software = card_software.get_content_frame()

        uvn_software = self.ptd_parser.get_ptd_property("uvn_software")

        for fmt in uvn_software:
            fmt_radio1 = ttk.Radiobutton(
                content_software,
                text=fmt,
                variable=self.var_software,
                value=fmt,
                style="PrimaryCard.TRadiobutton",
            )
            fmt_radio1.pack(anchor="w", pady=2, padx=5)
        self.var_software.set(uvn_software[0])

    def _create_memory_type_selection(self, parent):
        """Create the memory selection type

        Args:
            parent (_type_): memory selection option
        """
        card_memory_device = AccentCard(
            parent, title="Memory Device selection", padding=5
        )
        card_memory_device.grid(row=2, column=0, sticky="ew", pady=5)
        content_memory_device = card_memory_device.get_content_frame()

        memory_device = self.ptd_parser.get_ptd_property("uvn_memory_device")

        for fmt in memory_device:
            radio_btn = ttk.Radiobutton(
                content_memory_device,
                text=fmt,
                variable=self.var_table_id,
                value=fmt,
                style="PrimaryCard.TRadiobutton",
            )
            radio_btn.pack(anchor="w", pady=2, padx=5)
        self.var_table_id.set(memory_device[0])

    def _create_ptd_type_selection(self, parent):
        """Create the ptd type selection default is ALL

        Args:
            parent (_type_): parent object
        """
        ptd_card = PrimaryCard(parent, title="PTD table selection", padding=5)
        ptd_card.grid(row=0, column=0, sticky="nsew")
        ptd_content = ptd_card.get_content_frame()

        # Configure columns inside the card content frame
        ptd_content.columnconfigure(0, weight=1)

        self.ptd_entry = tk.Text(ptd_content, height=5, width=5, wrap="word", undo=True)
        self.ptd_entry.pack(side="left", fill="both", expand=True)

        scrollbar = ttk.Scrollbar(ptd_content, command=self.ptd_entry.yview)
        scrollbar.pack(side="right", fill="y")
        self.ptd_entry.configure(yscrollcommand=scrollbar.set)
        self.ptd_entry.insert("1.0", "ALL")

        self.lst_ptd_tables = self.ptd_entry.get("1.0", tk.END)

    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient="horizontal").grid(
            row=0, column=0, sticky="ew", pady=1
        )

        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5)

        ThemeManager.create_action_buttons(
            parent=controls_frame,  # Pass the frame where buttons should be packed/gridded
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back,
        )

    def execute(self) -> None:
        """Handle execution of Memory image generation"""

        ptd_table_content = self._get_ptd_table_inputs()
        if ptd_table_content:
            logger.info(
                f"Generating Memory Image for {self.var_software.get()} and for  {self.var_table_id.get()} for satellites {self.act_params.satellites} for lst_table {ptd_table_content}"
            )
            functions.generate_outputs(
                act_params=self.act_params,
                lst_par=[
                    self.var_software.get(),
                    self.var_table_id.get(),
                    self.var_icu.get(),
                    ptd_table_content
                ],
            )
            basics.pop_up_message(
                "Success", "Memory image generation completed successfully.", "info"
            )

    def _get_ptd_table_inputs(self):
        """Process the selected ptd table inputs, the default option is All

        Returns:
            str: selected ptd table option
        """
        ptd = self.ptd_entry.get("1.0", tk.END).strip()
        if not ptd:
            basics.pop_up_message(
                "Error", "Enter ALL or the correct otd tables.", "error"
            )
        else:
            ptd_table_content = ptd.replace("\n", "")
            ptd_all = self.ptd_parser.get_ptd_property("map_ptd_tables")
            return ptd_all if ptd_table_content == "All" else ptd_table_content

    def back(self) -> None:
        """Handle back navigation"""
        if self.app and hasattr(self.app, "back"):
            self.app.back()
