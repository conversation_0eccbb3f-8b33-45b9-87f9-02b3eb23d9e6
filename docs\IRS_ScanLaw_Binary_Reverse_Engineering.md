# IRS Scan Law Binary Reverse Engineering

**Input NetCDF file used:**
`assets/Input_Files/IRS/MTS1_IRS_SCANLAW_0000_v2.nc`

**Reference XLDT binary file used:**
`tests/tests_assets/IRS/Input/test_IRS_SL_generation_16384_5/ScanLaw_West_Summer_16385.xldt`

This document records the detailed reverse engineering of each analyzed binary section (FDA, MPA, etc.), including field-by-field mapping, NetCDF values, binary values, and conclusions for each entry.

---

## FDA Section Analysis

### Field Order (from VSM)
1. dwell_position_alpha (float)
2. dwell_position_epsilon (float)
3. fda_mp_pointer_alpha (int/pointer)
4. fda_mp_pointer_epsilon (int/pointer)

### Entry-by-Entry Comparison

| Entry | Byte 0-3         | Byte 4-7         | Byte 8-11        | Byte 12-13 | Byte 14-15 | Full Hex String                        | Notes/Interpretation |
|-------|------------------|------------------|------------------|------------|------------|----------------------------------------|---------------------|
| 0     | 00 00 01 00      | 0e 58 00 00      | 00 00 00 00      | 00 00      | 00 0c      | 00 00 01 00 0e 58 00 00 00 00 00 00 00 00 00 0c |                  |
| 1     | 00 0c 00 0c      | bb dc ff fe      | 6a 10 00 05      | 00 05      | 00 0c      | 00 0c 00 0c bb dc ff fe 6a 10 00 05 00 05 00 0c |                  |
| 2     | 83 1a ff ff      | 16 02 00 01      | 00 01 00 0c      | 4c 3b      | ff ff      | 83 1a ff ff 16 02 00 01 00 01 00 0c 4c 3b ff ff |                  |
| 3     | 1a27404e                           | 208471cb                             | 00000001                           | 00000001                             | (missing from script output)               |       |
| 4     | 00 01 00 0b      | e3 d8 00 01      | 1b 1e 00 01      | 00 01      | 00 0b      | 00 01 00 0b e3 d8 00 01 1b 1e 00 01 00 01 00 0b |                  |

**Legend:**
- Each entry is 16 bytes.
- Byte groups are split for possible field boundaries (4, 4, 4, 2, 2 bytes).
- The last two bytes often repeat or are small values (possible pointer or status).
- Some 4-byte groups could be floats (big/little endian), signed ints, or packed values.

*Fill in the Notes/Interpretation column as patterns are deduced. Add more rows/sections as needed for MPA and other sections.*

---

## MPA Section Analysis

*To be filled as MPA entries are analyzed.*

---

## Conclusions & Patterns

*Summarize findings, matches, mismatches, and any reverse-engineered rules or offsets here.* 