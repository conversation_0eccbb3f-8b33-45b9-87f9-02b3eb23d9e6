"""
CDC Logger Wrapper Module

This module provides a standardized logging interface for the CDC project.
It implements a singleton logger that can be used across the entire application
with consistent formatting and behavior.

Features:
- Consistent log formatting across the application
- Both file and console output with different log levels
- Automatic log file rotation with date-based naming
- Context-aware logging with module/function information
- Thread-safe singleton implementation
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional, Union
import threading
import inspect


class CDCLogger:
    """
    Singleton logger class for CDC project that provides standardized logging functionality.
    
    Features:
    - Consistent log formatting across the application
    - Both file and console output
    - Different log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    - Automatic log file rotation
    - Context-aware logging with module/function information
    - Thread-safe singleton pattern
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(CDCLogger, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.logger = logging.getLogger('CDC')
        self.logger.setLevel(logging.DEBUG)
        
        # Create logs directory if it doesn't exist
        self.logs_dir = Path('logs')
        self.logs_dir.mkdir(exist_ok=True)
        
        # Set up logging format with caller information
        class CallerFormatter(logging.Formatter):
            def format(self, record):
                # Get the caller's frame
                current_frame = inspect.currentframe()
                caller_frame = None
                try:
                    # Walk up the frame stack to find the actual caller
                    frame = current_frame
                    while frame:
                        if (frame.f_code.co_filename != __file__ and
                            'logging' not in frame.f_code.co_filename):
                            caller_frame = frame
                            break
                        frame = frame.f_back

                    if caller_frame:
                        # Update record with caller information
                        record.filename = os.path.basename(caller_frame.f_code.co_filename)
                        record.lineno = caller_frame.f_lineno
                finally:
                    del current_frame
                    if caller_frame:
                        del caller_frame

                return super().format(record)

        self.log_format = CallerFormatter(
            '%(asctime)s [%(levelname)s] %(filename)s:%(lineno)d: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Set up console handler
        self.console_handler = logging.StreamHandler(sys.stdout)
        self.console_handler.setFormatter(self.log_format)
        self.console_handler.setLevel(logging.INFO)
        self.logger.addHandler(self.console_handler)
        
        # Set up file handler with rotation
        self.file_handler = logging.handlers.TimedRotatingFileHandler(
            self.logs_dir / 'cdc.log',
            when='midnight',
            backupCount=7,
            encoding='utf-8'
        )
        self.file_handler.setFormatter(self.log_format)
        self.file_handler.setLevel(logging.DEBUG)
        self.logger.addHandler(self.file_handler)
        
        # Set up error file handler
        self.error_handler = logging.handlers.RotatingFileHandler(
            self.logs_dir / 'cdc_error.log',
            maxBytes=1024*1024,  # 1MB
            backupCount=5,
            encoding='utf-8'
        )
        self.error_handler.setFormatter(self.log_format)
        self.error_handler.setLevel(logging.ERROR)
        self.logger.addHandler(self.error_handler)
    
    def debug(self, message: str, *args, exc_info: bool = False, extra: dict = None, **kwargs):
        """Log debug message."""
        self.logger.debug(message, *args, exc_info=exc_info, extra=extra, **kwargs)
    
    def info(self, message: str, *args, exc_info: bool = False, extra: dict = None, **kwargs):
        """Log info message."""
        self.logger.info(message, *args, exc_info=exc_info, extra=extra, **kwargs)
    
    def warning(self, message: str, *args, exc_info: bool = False, extra: dict = None, **kwargs):
        """Log warning message."""
        self.logger.warning(message, *args, exc_info=exc_info, extra=extra, **kwargs)
    
    def error(self, message: str, *args, exc_info: bool = True, extra: dict = None, **kwargs):
        """Log error message with automatic exception info."""
        self.logger.error(message, *args, exc_info=exc_info, extra=extra, **kwargs)
    
    def critical(self, message: str, *args, exc_info: bool = True, extra: dict = None, **kwargs):
        """Log critical message with automatic exception info."""
        self.logger.critical(message, *args, exc_info=exc_info, extra=extra, **kwargs)
    
    def set_level(self, level: Union[int, str]):
        """Set logging level for both console and file handlers."""
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        self.logger.setLevel(level)
    
    def add_handler(self, handler: logging.Handler):
        """Add custom handler to logger."""
        handler.setFormatter(self.log_format)
        self.logger.addHandler(handler)
    
    def remove_handler(self, handler: logging.Handler):
        """Remove handler from logger."""
        self.logger.removeHandler(handler)

# Create global logger instance
logger = CDCLogger()

# Define public API
__all__ = ['logger', 'CDCLogger']