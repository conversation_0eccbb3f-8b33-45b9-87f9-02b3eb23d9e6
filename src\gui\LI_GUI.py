"""
LI GUI module for handling LI-specific activities
"""

from .frames import BaseFrame

# Import LI-specific frames
from .li_gui_features.basic_conf_gui import BASIC_CONF_GUI
from .li_gui_features.oper_conf_gui import OPER_CONF_GUI
from .li_gui_features.lme_conf_gui import LME_CONF_GUI
from .li_gui_features.lme_conf_paf_gui import LME_CONF_PAF_GUI
from .li_gui_features.li_paf_gui import LI_PAF_GUI

class LI_GUI(BaseFrame):
    """Main LI GUI class"""
    
    @staticmethod
    def register_activities(handler):
        # Register LI activities with the handler
        handler.register_activity("LI", "LOH BASIC Conf", BASIC_CONF_GUI)
        handler.register_activity("LI", "LOH OPER Conf", OPER_CONF_GUI)
        handler.register_activity("LI", "LME Conf (Patch)", LME_CONF_GUI)
        handler.register_activity("LI", "LME Conf (PAF)", LME_CONF_PAF_GUI)
        handler.register_activity("LI", "LI Calibration", LI_PAF_GUI)
