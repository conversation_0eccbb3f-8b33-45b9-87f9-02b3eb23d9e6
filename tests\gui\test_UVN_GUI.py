import pytest
from src.gui import create_app
from src.gui.UVN_GUI import UVN_GUI
from src.gui.frames import InstrumentHandler # Import InstrumentHandler

def test_uvn_gui_handle_activity(mock_app):
    # Create test activity parameters
    from src.utils.activity_params import ActivityParams
    act_params = ActivityParams()
    act_params.instrument = "UVN"
    act_params.activity = "Generate PTD Memory image File"
    act_params.satellites = ["MTG-S1"]  # Initialize with a satellite
    
    # Register UVN activities with the InstrumentHandler
    UVN_GUI.register_activities(InstrumentHandler)
    
    # Call handle_activity on InstrumentHandler
    InstrumentHandler.handle_activity(mock_app, act_params)

