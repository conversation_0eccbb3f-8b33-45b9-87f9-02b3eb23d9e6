#!/usr/bin/env python3
"""
XLDT Reader GUI Application
==========================

A graphical user interface for the XLDT file reader.
Provides an easy-to-use interface for analyzing XLDT files.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import json
from xldt_reader_standalone import (
    XLDTReader,
    create_xldt_file,
    format_structured_parameters_cdc_style,
    format_xldt_cdc_input_style,
    export_structured_parameters_to_dict
)


class XLDTReaderGUI:
    """Main GUI application for XLDT Reader."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("XLDT File Reader - MTG Satellite Operations")
        self.root.geometry("1200x800")
        
        # Initialize reader
        self.reader = None
        self.current_data = None
        
        # Create GUI components
        self.create_widgets()
        self.setup_layout()
        
        # Load default configuration
        self.load_default_config()
    
    def create_widgets(self):
        """Create all GUI widgets."""
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # File selection frame
        self.file_frame = ttk.LabelFrame(self.main_frame, text="File Selection", padding="10")
        
        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(self.file_frame, textvariable=self.file_path_var, width=60)
        self.browse_btn = ttk.Button(self.file_frame, text="Browse XLDT File", command=self.browse_file)
        
        # Configuration frame
        self.config_frame = ttk.LabelFrame(self.main_frame, text="Configuration", padding="10")
        
        self.config_path_var = tk.StringVar()
        self.config_entry = ttk.Entry(self.config_frame, textvariable=self.config_path_var, width=60)
        self.config_browse_btn = ttk.Button(self.config_frame, text="Browse Config", command=self.browse_config)
        
        # Quick config buttons
        self.irs_btn = ttk.Button(self.config_frame, text="IRS Config", command=self.load_irs_config)
        self.fci_btn = ttk.Button(self.config_frame, text="FCI Config", command=self.load_fci_config)
        
        # Analysis frame
        self.analysis_frame = ttk.LabelFrame(self.main_frame, text="Analysis", padding="10")
        
        self.analyze_btn = ttk.Button(self.analysis_frame, text="Analyze File", command=self.analyze_file)
        self.validate_crc_var = tk.BooleanVar(value=True)
        self.crc_check = ttk.Checkbutton(self.analysis_frame, text="Validate CRC", variable=self.validate_crc_var)
        
        # Results notebook
        self.results_notebook = ttk.Notebook(self.main_frame)
        
        # Summary tab
        self.summary_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.summary_frame, text="Summary")
        
        self.summary_text = scrolledtext.ScrolledText(self.summary_frame, height=15, width=80)
        
        # Sections tab
        self.sections_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.sections_frame, text="Sections")
        
        # Create treeview for sections
        self.sections_tree = ttk.Treeview(self.sections_frame, columns=("Length", "Order", "MSDF_ID", "Data_Preview"), show="tree headings")
        self.sections_tree.heading("#0", text="Section Name")
        self.sections_tree.heading("Length", text="Length (bytes)")
        self.sections_tree.heading("Order", text="Order")
        self.sections_tree.heading("MSDF_ID", text="MSDF ID")
        self.sections_tree.heading("Data_Preview", text="Data Preview")
        
        # Scrollbar for treeview
        self.sections_scrollbar = ttk.Scrollbar(self.sections_frame, orient="vertical", command=self.sections_tree.yview)
        self.sections_tree.configure(yscrollcommand=self.sections_scrollbar.set)

        # CDC Input Format tab
        self.cdc_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.cdc_frame, text="CDC Input Format")

        self.cdc_text = scrolledtext.ScrolledText(self.cdc_frame, height=15, width=80, font=("Courier", 10))

        # Raw data tab
        self.raw_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.raw_frame, text="Raw Data")
        
        self.raw_text = scrolledtext.ScrolledText(self.raw_frame, height=15, width=80, font=("Courier", 10))
        
        # JSON tab
        self.json_frame = ttk.Frame(self.results_notebook)
        self.results_notebook.add(self.json_frame, text="JSON Export")
        
        self.json_text = scrolledtext.ScrolledText(self.json_frame, height=15, width=80, font=("Courier", 10))
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        self.status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, relief="sunken")
        
        # Menu bar
        self.create_menu()
    
    def create_menu(self):
        """Create menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open XLDT File", command=self.browse_file)
        file_menu.add_command(label="Load Configuration", command=self.browse_config)
        file_menu.add_separator()
        file_menu.add_command(label="Export Summary", command=self.export_summary)
        file_menu.add_command(label="Export JSON", command=self.export_json)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Create Sample XLDT", command=self.create_sample)
        tools_menu.add_command(label="Batch Analysis", command=self.batch_analysis)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="User Guide", command=self.show_help)
    
    def setup_layout(self):
        """Setup the layout of widgets."""
        self.main_frame.grid(row=0, column=0, sticky="nsew")
        
        # Configure root grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.rowconfigure(3, weight=1)
        
        # File selection
        self.file_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        self.file_entry.grid(row=0, column=0, sticky="ew", padx=(0, 10))
        self.browse_btn.grid(row=0, column=1)
        self.file_frame.columnconfigure(0, weight=1)
        
        # Configuration
        self.config_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        self.config_entry.grid(row=0, column=0, columnspan=3, sticky="ew", padx=(0, 10))
        self.config_browse_btn.grid(row=0, column=3)
        self.irs_btn.grid(row=1, column=0, sticky="w", pady=(5, 0))
        self.fci_btn.grid(row=1, column=1, sticky="w", pady=(5, 0), padx=(10, 0))
        self.config_frame.columnconfigure(0, weight=1)
        
        # Analysis
        self.analysis_frame.grid(row=2, column=0, sticky="ew", pady=(0, 10))
        self.analyze_btn.grid(row=0, column=0)
        self.crc_check.grid(row=0, column=1, padx=(20, 0))
        
        # Results notebook
        self.results_notebook.grid(row=3, column=0, sticky="nsew", pady=(0, 10))
        
        # Summary tab layout
        self.summary_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Sections tab layout
        self.sections_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        self.sections_scrollbar.pack(side="right", fill="y", pady=10, padx=(0, 10))

        # CDC Input Format tab layout
        self.cdc_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Raw data tab layout
        self.raw_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # JSON tab layout
        self.json_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Status bar
        self.status_bar.grid(row=4, column=0, sticky="ew")
    
    def load_default_config(self):
        """Load default IRS configuration."""
        default_config = "config/IRS/IRS_SL_Conf.csv"
        if os.path.exists(default_config):
            self.config_path_var.set(default_config)
            self.update_status("Default IRS configuration loaded")
    
    def browse_file(self):
        """Browse for XLDT file."""
        filename = filedialog.askopenfilename(
            title="Select XLDT File",
            filetypes=[("XLDT files", "*.xldt"), ("All files", "*.*")],
            initialdir="inputs" if os.path.exists("inputs") else "."
        )
        if filename:
            self.file_path_var.set(filename)
            self.update_status(f"Selected file: {os.path.basename(filename)}")
    
    def browse_config(self):
        """Browse for configuration file."""
        filename = filedialog.askopenfilename(
            title="Select Configuration File",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            initialdir="config" if os.path.exists("config") else "."
        )
        if filename:
            self.config_path_var.set(filename)
            self.update_status(f"Selected config: {os.path.basename(filename)}")
    
    def load_irs_config(self):
        """Load IRS configuration."""
        config_path = "config/IRS/IRS_SL_Conf.csv"
        if os.path.exists(config_path):
            self.config_path_var.set(config_path)
            self.update_status("IRS configuration loaded")
        else:
            messagebox.showerror("Error", "IRS configuration file not found")
    
    def load_fci_config(self):
        """Load FCI configuration."""
        config_path = "config/FCI/FCI_SL_Conf.csv"
        if os.path.exists(config_path):
            self.config_path_var.set(config_path)
            self.update_status("FCI configuration loaded")
        else:
            messagebox.showerror("Error", "FCI configuration file not found")
    
    def analyze_file(self):
        """Analyze the selected XLDT file."""
        file_path = self.file_path_var.get()
        config_path = self.config_path_var.get()
        
        if not file_path:
            messagebox.showerror("Error", "Please select an XLDT file")
            return
        
        if not os.path.exists(file_path):
            messagebox.showerror("Error", "Selected file does not exist")
            return
        
        try:
            self.update_status("Analyzing file...")
            
            # Initialize reader with configuration
            self.reader = XLDTReader(config_path if config_path and os.path.exists(config_path) else None)
            
            # Read and parse file
            self.current_data = self.reader.read_xldt_file(file_path)
            
            # Validate CRC if requested
            if self.validate_crc_var.get() and 'CRC' in self.current_data['sections']:
                crc_valid = self.reader.validate_crc(
                    self.current_data['raw_hex'],
                    self.current_data['sections']['CRC']
                )
                self.current_data['crc_valid'] = crc_valid
            
            # Update all tabs
            self.update_summary()
            self.update_sections()
            self.update_cdc_format()
            self.update_raw_data()
            self.update_json()
            
            self.update_status("Analysis complete")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to analyze file: {str(e)}")
            self.update_status("Analysis failed")
    
    def update_summary(self):
        """Update the summary tab."""
        if not self.current_data:
            return
        
        summary_lines = []
        summary_lines.append("XLDT File Analysis Summary")
        summary_lines.append("=" * 50)
        
        # File info
        file_info = self.current_data.get('file_info', {})
        summary_lines.append(f"File: {os.path.basename(file_info.get('file_path', 'Unknown'))}")
        summary_lines.append(f"Size: {file_info.get('file_size', 0):,} bytes")
        summary_lines.append(f"Hex Length: {file_info.get('hex_length', 0):,} characters")
        
        # Header info
        header = self.current_data.get('header')
        if header:
            summary_lines.append(f"\nXLDT Header:")
            summary_lines.append(f"  Format ID: {header.format_id} (0x{header.format_id:04X})")
            summary_lines.append(f"  MM Slot: {header.mm_slot} (0x{header.mm_slot:04X})")
        
        # CRC validation
        if 'crc_valid' in self.current_data:
            status = "✅ VALID" if self.current_data['crc_valid'] else "❌ INVALID"
            summary_lines.append(f"\nCRC Status: {status}")
        
        # Sections summary
        sections = self.current_data.get('sections', {})
        summary_lines.append(f"\nSections ({len(sections)}):")
        summary_lines.append("-" * 30)
        
        total_bytes = 0
        for name, section in sections.items():
            if hasattr(section, 'length'):
                summary_lines.append(f"  {name}: {section.length} bytes")
                total_bytes += section.length
            elif isinstance(section, dict) and 'remaining_bytes' in section:
                summary_lines.append(f"  Remaining data: {section['remaining_bytes']} bytes")
                total_bytes += section['remaining_bytes']
            else:
                summary_lines.append(f"  {name}: {type(section).__name__}")
        
        summary_lines.append(f"\nTotal parsed: {total_bytes:,} bytes")
        
        # Configuration info
        if self.reader and self.reader.config_path:
            summary_lines.append(f"\nConfiguration: {os.path.basename(self.reader.config_path)}")
        else:
            summary_lines.append(f"\nConfiguration: Generic parsing (no config file)")
        
        self.summary_text.delete(1.0, tk.END)
        self.summary_text.insert(1.0, "\n".join(summary_lines))
    
    def update_sections(self):
        """Update the sections tab."""
        # Clear existing items
        for item in self.sections_tree.get_children():
            self.sections_tree.delete(item)
        
        if not self.current_data:
            return
        
        sections = self.current_data.get('sections', {})
        
        for name, section in sections.items():
            if hasattr(section, 'length'):
                # Regular section
                data_preview = section.data.hex().upper()[:32] + "..." if len(section.data.hex()) > 32 else section.data.hex().upper()
                
                self.sections_tree.insert("", "end", text=name, values=(
                    f"{section.length}",
                    f"{section.order}",
                    f"{section.msdf_id}" if section.msdf_id is not None else "N/A",
                    data_preview
                ))
            elif isinstance(section, dict):
                # Generic section
                self.sections_tree.insert("", "end", text=name, values=(
                    section.get('remaining_bytes', 'N/A'),
                    'N/A',
                    'N/A',
                    str(section.get('note', ''))[:50] + "..." if len(str(section.get('note', ''))) > 50 else str(section.get('note', ''))
                ))

    def update_cdc_format(self):
        """Update the CDC input format tab."""
        if not self.current_data:
            self.cdc_text.delete(1.0, tk.END)
            self.cdc_text.insert(1.0, "No data available")
            return

        try:
            # Format in CDC input style
            formatted_output = format_xldt_cdc_input_style(self.current_data)

            self.cdc_text.delete(1.0, tk.END)
            self.cdc_text.insert(1.0, formatted_output)

        except Exception as e:
            self.cdc_text.delete(1.0, tk.END)
            self.cdc_text.insert(1.0, f"Error generating CDC format: {str(e)}")

    def update_raw_data(self):
        """Update the raw data tab."""
        if not self.current_data:
            return
        
        raw_hex = self.current_data.get('raw_hex', '')
        
        # Format hex data with line breaks every 64 characters
        formatted_hex = ""
        for i in range(0, len(raw_hex), 64):
            line = raw_hex[i:i+64]
            # Add spaces every 8 characters for readability
            spaced_line = ' '.join(line[j:j+8] for j in range(0, len(line), 8))
            formatted_hex += f"{i//2:08X}: {spaced_line}\n"
        
        self.raw_text.delete(1.0, tk.END)
        self.raw_text.insert(1.0, formatted_hex)
    
    def update_json(self):
        """Update the JSON tab with enhanced structured parameters."""
        if not self.current_data or not self.reader:
            return

        try:
            # Get basic export data
            json_data = self.reader.export_to_dict(self.current_data)

            # Add structured parameters
            structured_params = self.reader.get_structured_parameters(self.current_data)
            if structured_params:
                json_data['structured_parameters'] = export_structured_parameters_to_dict(structured_params)

            formatted_json = json.dumps(json_data, indent=2)

            self.json_text.delete(1.0, tk.END)
            self.json_text.insert(1.0, formatted_json)
        except Exception as e:
            self.json_text.delete(1.0, tk.END)
            self.json_text.insert(1.0, f"Error generating JSON: {str(e)}")
    
    def update_status(self, message):
        """Update status bar."""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def export_summary(self):
        """Export summary to text file."""
        if not self.current_data:
            messagebox.showwarning("Warning", "No data to export")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Export Summary",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(self.summary_text.get(1.0, tk.END))
                messagebox.showinfo("Success", f"Summary exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export summary: {str(e)}")
    
    def export_json(self):
        """Export JSON data to file."""
        if not self.current_data or not self.reader:
            messagebox.showwarning("Warning", "No data to export")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Export JSON",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                json_data = self.reader.export_to_dict(self.current_data)
                with open(filename, 'w') as f:
                    json.dump(json_data, f, indent=2)
                messagebox.showinfo("Success", f"JSON exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export JSON: {str(e)}")
    
    def create_sample(self):
        """Create a sample XLDT file."""
        filename = filedialog.asksaveasfilename(
            title="Create Sample XLDT File",
            defaultextension=".xldt",
            filetypes=[("XLDT files", "*.xldt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                # Sample XLDT data
                sample_hex = "00010007ABCD00123456DEADBEEFCAFEBABE12345678"
                create_xldt_file(sample_hex, filename)
                messagebox.showinfo("Success", f"Sample XLDT file created: {filename}")
                
                # Optionally load the created file
                if messagebox.askyesno("Load File", "Would you like to load the created sample file?"):
                    self.file_path_var.set(filename)
                    self.analyze_file()
                    
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create sample file: {str(e)}")
    
    def batch_analysis(self):
        """Perform batch analysis on multiple files."""
        directory = filedialog.askdirectory(title="Select Directory with XLDT Files")
        if not directory:
            return
        
        xldt_files = [f for f in os.listdir(directory) if f.endswith('.xldt')]
        
        if not xldt_files:
            messagebox.showwarning("Warning", "No XLDT files found in selected directory")
            return
        
        # Create results window
        results_window = tk.Toplevel(self.root)
        results_window.title("Batch Analysis Results")
        results_window.geometry("800x600")
        
        results_text = scrolledtext.ScrolledText(results_window, height=30, width=100)
        results_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        results_lines = []
        results_lines.append("Batch Analysis Results")
        results_lines.append("=" * 50)
        results_lines.append(f"Directory: {directory}")
        results_lines.append(f"Files found: {len(xldt_files)}")
        results_lines.append("")
        
        config_path = self.config_path_var.get()
        reader = XLDTReader(config_path if config_path and os.path.exists(config_path) else None)
        
        for i, filename in enumerate(xldt_files, 1):
            file_path = os.path.join(directory, filename)
            results_lines.append(f"{i}. {filename}")
            results_lines.append("-" * len(filename))
            
            try:
                data = reader.read_xldt_file(file_path)
                header = data['header']
                sections = data['sections']
                file_info = data['file_info']
                
                results_lines.append(f"   Size: {file_info['file_size']:,} bytes")
                results_lines.append(f"   Format ID: {header.format_id}")
                results_lines.append(f"   MM Slot: {header.mm_slot}")
                results_lines.append(f"   Sections: {len(sections)}")
                
                # CRC validation
                if 'CRC' in sections:
                    try:
                        is_valid = reader.validate_crc(data['raw_hex'], sections['CRC'])
                        status = "VALID" if is_valid else "INVALID"
                        results_lines.append(f"   CRC: {status}")
                    except:
                        results_lines.append(f"   CRC: Error")
                
            except Exception as e:
                results_lines.append(f"   Error: {str(e)}")
            
            results_lines.append("")
        
        results_text.insert(1.0, "\n".join(results_lines))
    
    def show_about(self):
        """Show about dialog."""
        about_text = """XLDT Reader GUI v1.0

A graphical interface for analyzing XLDT (Transfer Layer Data) files
used in MTG satellite operations.

Features:
• Parse XLDT binary files
• Configuration support for IRS and FCI instruments
• CRC validation
• Multiple output formats
• Batch processing
• Export capabilities

Developed for CDC-S Satellite Operations"""
        
        messagebox.showinfo("About XLDT Reader", about_text)
    
    def show_help(self):
        """Show help dialog."""
        help_text = """XLDT Reader User Guide

1. Select an XLDT file using 'Browse XLDT File'
2. Choose a configuration file (IRS or FCI) or use default
3. Click 'Analyze File' to process the file
4. View results in different tabs:
   - Summary: Overview of file contents
   - Sections: Detailed section information
   - Raw Data: Hexadecimal file contents
   - JSON Export: Machine-readable format

Menu Options:
• File → Export: Save analysis results
• Tools → Create Sample: Generate test XLDT file
• Tools → Batch Analysis: Process multiple files

Tips:
• Enable 'Validate CRC' to check data integrity
• Use IRS config for IRS instrument files
• Use FCI config for FCI instrument files"""
        
        messagebox.showinfo("User Guide", help_text)


def main():
    """Main function to run the GUI."""
    root = tk.Tk()
    app = XLDTReaderGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
