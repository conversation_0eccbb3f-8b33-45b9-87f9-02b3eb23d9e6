# Standard library imports
import datetime
import math
import numpy as np
import pandas as pd
import os

# Local imports
from src.utils.import_utils import config
from src.logger_wrapper import logger
from src.utils.excel_utils import Read_Sheet
from src.utils.netcdf_utils import read_netCDF
from src.utils.conversion_utils import dec2hex, hex2dec
from src.utils.activity_params import ActivityParams

def FCI_SELUT(act_params: ActivityParams, satellite: str):
    """FCI Scan Encoder LUT Main Function"""
    # Load Configuration
    dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]

    # Debug log the configuration
    logger.info(f"Using instrument configuration: {dict_config}")
    logger.info(f"Slicer dict type: {type(dict_config['slicer_dict'])}")
    logger.info(f"Slicer dict content: {dict_config['slicer_dict']}")

    df_SELUT_config = pd.read_csv(
        os.path.join(
            config.CONFIG_VALUES["config_folder"], act_params.instrument, "FCI_SELUT_Conf.csv"
        ),
        keep_default_na=False,
        index_col="Section",
    )

    # Initialize Variables
    new_dict = {}
    lst_DWs = []
    lst_arguments = []

    # Default fixed parameters
    Max_DW_per_TC = 475
    TC_ID = "FASCS03J"
    TC_Arg_SCAE_ID = "FASHS6ZZ"
    TC_Arg_LUT_ID = "FASHS70X"
    TC_Arg_Offset_ID = "FASHS99X"
    TC_Arg_Len_ID = "FASHS73U"
    TC_Arg_Data_ID = "FASHS9AX"

    # Read netCDF file with correct parameter order and explicit use of scan_encode_correction_lut_id
    dict_netCDF = read_netCDF(
        act_params=act_params,
        satellite=satellite,
        main_files=["SCANENC"],
        used_files=["SCANENC"],
        instrument_conf=dict_config
    )

    # Generate dictionary of parameter values
    df_file_tab = Read_Sheet(dict_config["vsm_path"], "SCANENC")

    # Find only variables needed for the specific Section
    for sections in df_SELUT_config.index:
        df_file_tab_proc = df_file_tab[
            df_file_tab["TC_Section"].str.contains(sections)
        ]
        logger.info(f"Processing section: {sections}")
        # logger.info(f"DataFrame for section: \n{df_file_tab_proc}")
        # logger.info(f"Dataframe file tab: \n{df_file_tab}")
        logger.info(f"vector to iterate: {df_file_tab_proc['Name']}")
        
        for variable in df_file_tab_proc["Name"]:
            logger.info(f"Variable: {variable}")
            logger.info(f"Variable type: {type(variable)}")
            logger.info(f"Variable value: {dict_netCDF[variable]}")
        
        
            new_dict[sections] = dict_netCDF[variable]

    
    logger.info(f"New dictionary: {new_dict}")
    logger.info(f"length of new_dict: {len(new_dict)}")
    logger.info(f"length of new_dict[NS]: {len(new_dict['NS'])}")
    logger.info(f"length of new_dict[EW]: {len(new_dict['EW'])}")

    full_dict = np.concatenate([new_dict["NS"], new_dict["EW"]])

    logger.info(f"Full dictionary: {full_dict}")
    logger.info(f"length of full_dict: {len(full_dict)}")

    # Generate Lists of arguments
    int_parameters = len(full_dict)
    int_num_DW = int(int_parameters / 2)

    logger.info(f"Number of DW: {int_num_DW}")
    logger.info(f"Number of parameters: {int_parameters}")

    # Change case of the memory requested by the user
    str_SCAE_ID = act_params.side.upper()

    for entries in range(int_num_DW):
        lst_DWs.append(
            hex2dec(
                dec2hex(full_dict[entries * 2]).zfill(2)[-2:]
                + dec2hex(full_dict[entries * 2 + 1]).zfill(2)[-2:]
            )
        )

    int_num_TC = math.ceil(int_num_DW / Max_DW_per_TC)

    for int_TC in range(int_num_TC):
        if (int_TC + 1) * Max_DW_per_TC < int_num_DW:
            int_Len = Max_DW_per_TC
        else:
            int_Len = int_num_DW - Max_DW_per_TC * int_TC

        lst_arguments.append(
            [
                str_SCAE_ID,
                new_dict["Index"].item(),
                int_TC * Max_DW_per_TC * 2,
                int_Len,
                lst_DWs[int_TC * Max_DW_per_TC : int_TC * Max_DW_per_TC + int_Len],
            ]
        )

    # Create TC filename and path
    str_full_path = os.path.join(
        config.CONFIG_VALUES["output_folder"],
        satellite,
        f"{satellite.replace('MT','MTG')}_SCANENCLUT_TC_Stack_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_ID{str(act_params.scan_encode_correction_lut_id).zfill(4)}"
    )

    # Generate TC Stack
    create_TCStack(
        TC_ID,
        TC_Arg_SCAE_ID,
        TC_Arg_LUT_ID,
        TC_Arg_Offset_ID,
        TC_Arg_Len_ID,
        TC_Arg_Data_ID,
        lst_arguments,
        satellite[-1],
        int_num_TC,
        str_full_path,
    )


def create_TCStack(
    str_tc_id: str,
    str_tc_arg_SCAE: str,
    str_tc_arg_LUT: str,
    str_tc_arg_offset: str,
    str_tc_arg_len: str,
    str_tc_arg_data: str,
    lst_arguments: list,
    str_sat: str,
    int_num_TC: int,
    str_full_path: str,
):
    """Writing a TC Stack for the Scan Encoder LUT Update
    TC Stack created manually with the condition required (interlock, etc...) then used it as a template.
    Manual to check structure EGOS-MCS-S2K-ICD-0002
    """
    # Initialize  the list of commands with the base record,1685705431 is the generating time too lazy to calculate and pytest more difficult
    lst_TC_Stack = [f"2|OPIT|1685705431|0|OD-MI{str_sat}|0000000000|"]

    # Create the commands
    for int_TC in range(int_num_TC):
        # Reset Variables
        lst_step = []

        # Command Header Record
        lst_step.append(
            f"C|{str_tc_id}|0|1|0|0|0|0|1|5|0|0|0|{4 + lst_arguments[int_TC][3]}|0|1|0|0|||1||||0|1|9|"
        )

        # 1st Argument
        lst_step.append(f"{str_tc_arg_SCAE}|0|4|2|1|{lst_arguments[int_TC][0]}|1|")

        # 2nd Argument
        lst_step.append(f"{str_tc_arg_LUT}|0|0|2|0|{lst_arguments[int_TC][1]}|1|")

        # 3rd Argument
        lst_step.append(f"{str_tc_arg_offset}|0|0|2|0|{lst_arguments[int_TC][2]}|1|")

        # 4th Argument
        lst_step.append(f"{str_tc_arg_len}|0|0|2|0|{lst_arguments[int_TC][3]}|0|")

        for int_DW in range(lst_arguments[int_TC][3]):
            # 5th Argument
            lst_step.append(
                f"{str_tc_arg_data}|0|0|2|0|{lst_arguments[int_TC][4][int_DW]}|1|"
            )

        # Add the TC to the TC Stack
        lst_TC_Stack += lst_step

    # Prepare the file lines
    str_TC_stack = "\n".join(lst_TC_Stack)

    # Create the output file
    with open(str_full_path, "w+b") as obj_TC_Stack:
        obj_TC_Stack.write(bytes("".join(str_TC_stack), "utf-8"))
