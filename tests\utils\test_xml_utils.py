# Standard library imports
import os
import pytest
from unittest.mock import patch

# Local imports
from src.utils import xml_utils
from src.utils.import_utils import config

@pytest.fixture
def xml_test_files():
    base_path = os.path.normpath(os.path.join(config.CONFIG_VALUES['test_files'], "XML"))
    return {
        "valid_xml": os.path.normpath(os.path.join(base_path, "xml_file.xml")),
        "invalid_xml": os.path.normpath(os.path.join(base_path, "xml_file_error.xml")),
        "schema": os.path.normpath(os.path.join(base_path, "xml_schema.xsd"))
    }

@pytest.fixture(autouse=True)
def verify_test_files(xml_test_files):
    """Verify test files exist before running tests"""
    for file_type, file_path in xml_test_files.items():
        if not os.path.exists(file_path):
            pytest.fail(f"Test file missing: {file_path}")


class TestCheckXML:
    def test_valid_xml(self, xml_test_files, caplog):
        """Test check_xml with valid XML file"""
        xml_utils.check_xml(xml_test_files["valid_xml"], xml_test_files["schema"], True)
        
        # Verify success log message
        assert "Success: Generated PAF" in caplog.text
        assert "complies to XML schema" in caplog.text


    @patch('src.utils.import_utils.basics.pop_up_message')
    @patch('src.utils.import_utils.basics.xml_validation_schema')
    def test_non_test_mode(self, mock_validation, mock_popup, xml_test_files):
        """Test check_xml in non-test mode"""
        # Mock validation to return a single error
        mock_validation.return_value = (False, {
            "Element": "test_element",
            "Error": "test error message"
        })
        
        xml_utils.check_xml(xml_test_files["invalid_xml"], xml_test_files["schema"], False)
        
        # Verify popup was called with expected message
        expected_message = (
            "Error: Generated PAF xml_file_error.xml DOES NOT comply to XML schema. "
            "DO NOT push to Git repository. Check results are:\n"
            "Element: e\n"
            "Error: e"
        )
        mock_popup.assert_called_with("Error", expected_message, "error")

    @patch('src.utils.import_utils.basics.xml_validation_schema')
    def test_validation_error(self, mock_validation, xml_test_files):
        """Test check_xml with validation error"""
        mock_validation.return_value = (False, {"Element": "test", "Error": "Test error"})
        xml_utils.check_xml(xml_test_files["invalid_xml"], xml_test_files["schema"], True)
        
        # Verify error handling
        mock_validation.assert_called_once()


# @pytest.fixture(scope="session")
# def create_create_paf_file(tmp_path_factory):
#     # Create a temp path for the file
#     str_full_path = tmp_path_factory.mktemp("data") / "STR_paf.xml"

#     dict_paf_input = {
#         "STR": "STR1",
#         "Start_Time": "09.10.1989",
#         "End_Time": "09.10.1989",
#     }

#     basics.create_paf(
#         os.path.join(os.path.dirname(__file__), r"Test\PAF\STR_paf_template.xml"),
#         str_full_path,
#         "OPIT",
#         "MTG-I1",
#         "MTI1",
#         "N_STR_0701",
#         dict_paf_input,
#     )

#     return str_full_path


# class Test_create_paf:
#     def test_file_created(self, create_create_paf_file):
#         # Test the file was created properly
#         assert basics.path_valid(create_create_paf_file)

#     def test_file_content(self, create_create_paf_file):
#         # Read results
#         tree = etree.parse(create_create_paf_file)
#         root = tree.getroot()
#         obj_step_body = root.findall(".//date")
#         assert obj_step_body[0][1].text == "09.10.1989"
#         obj_step_body = root.findall(".//enumeration")
#         assert obj_step_body[0][1].text == "0"
