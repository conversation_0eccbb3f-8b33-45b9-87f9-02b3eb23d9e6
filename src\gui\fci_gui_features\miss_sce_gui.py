import tkinter as tk
from tkinter import ttk
from src.utils.import_utils import basics
from src import functions
from ..frames import BaseFrame
from ..theme_manager import ThemeManager
from ..custom_widgets import PrimaryCard, SecondaryCard
from src.logger_wrapper import logger
from src.utils.netcdf_utils import get_netcdf_variables_value

class MISS_SCE_GUI(BaseFrame):
    """Frame for FCI Mission Scenario configuration"""
    def __init__(self, parent, act_params, *args, **kwargs): # MODIFIED: Removed app=None
        self.act_params = act_params
        # self.app = app  # MODIFIED: Removed this line, BaseFrame should handle self.app
        # Initialize widget variables to None, they will be created in _create_body
        self.input_scan_law_id = None
        self.str_MM_Option = tk.StringVar() # Initialize StringVar here
        super().__init__(parent, *args, **kwargs)


    def create_widgets(self):
        """Create mission scenario GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Use grid for the main_frame within self (BaseFrame already configures row/col 0)
        # Remove bottom padding: padding=5 -> padding=(5, 5, 5, 0)
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure rows/columns for main_frame's internal layout
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body (expands)
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1) # Allow content to expand horizontally

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)


    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1) # Allow label/separator to fill

        ttk.Label(
            title_frame,
            text=f"FCI Mission Scenario Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew") # Use grid

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5) # Use grid


    def _create_body(self, parent):
        """Creates the body section with input cards, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)

        # Configure body_frame grid to center content vertically and horizontally
        body_frame.columnconfigure(0, weight=1) # Left Spacer
        body_frame.columnconfigure(1, weight=0) # Content Column (no extra space)
        body_frame.columnconfigure(2, weight=1) # Right Spacer
        body_frame.rowconfigure(0, weight=1) # Top Spacer
        body_frame.rowconfigure(1, weight=0) # Scan Law Card
        body_frame.rowconfigure(2, weight=0) # Memory Card
        body_frame.rowconfigure(3, weight=1) # Bottom Spacer        
        # Scan Law ID Card - Place in middle column
        scan_law_card = PrimaryCard(body_frame, title="Scan Law Configuration", padding=5)
        scan_law_card.grid(row=1, column=1, pady=10, sticky="ew") # sticky='ew' for horizontal expansion
        
        scan_law_content = scan_law_card.get_content_frame()
        frame_scan_id = ttk.Frame(scan_law_content, style="PrimaryCard.TFrame")
        frame_scan_id.pack(anchor="center", pady=5, padx=5) # Pack inside card is fine for simple content
        ttk.Label(frame_scan_id, text="Scan Law ID:", style="PrimaryCard.TLabel").pack(side=tk.LEFT, padx=5)

        scan_law_id_values= list(get_netcdf_variables_value('scan_law_id',self.act_params, "SCANLAW"))
        scan_law_id_values.sort()

        self.input_scan_law_id = ttk.Combobox(
            frame_scan_id,
            width=5,
            values=scan_law_id_values,
            state="readonly"
        )
        self.input_scan_law_id.pack(side=tk.LEFT, padx=5)
        self.input_scan_law_id.current(0)        # Mass Memory Slot ID Card - Place in middle column
        memory_card = SecondaryCard(body_frame, title="Memory Configuration", padding=5)
        memory_card.grid(row=2, column=1, pady=10, sticky="ew") # sticky='ew'
        
        memory_content = memory_card.get_content_frame()
        frame_mem_id = ttk.Frame(memory_content, style="SecondaryCard.TFrame")
        frame_mem_id.pack(anchor="center", pady=5, padx=5)
        ttk.Label(frame_mem_id, text="Mass Memory Slot ID:", style="SecondaryCard.TLabel").pack(side=tk.LEFT, padx=5)
        MM_Choice = ttk.Combobox(frame_mem_id, width=5, textvariable=self.str_MM_Option, state="readonly")
        MM_Choice["values"] = ("1", "2", "3", "4", "5", "6", "7", "8") # Removed empty option
        MM_Choice.current(0) # Default to "1"
        MM_Choice.pack(side=tk.LEFT, padx=5)


    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)        
        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=2)
        
        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5)
        controls_frame.columnconfigure(0, weight=1)  # Ensure the frame expands properly

        ThemeManager.create_action_buttons(
            parent=controls_frame, # Pass the frame where buttons should be packed/gridded
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )


    def execute(self):
        """Handle execution of the mission scenario with validation."""
        # Ensure widgets are created before accessing them
        if self.input_scan_law_id.get() is None:
            logger.error("Execute called before widgets created in MISS_SCE_GUI")
            basics.pop_up_message("Error", "GUI not fully initialized.", "error")
            return

        scan_law_id_val = self.input_scan_law_id.get()
        mm_slot_val = self.str_MM_Option.get()

        if not scan_law_id_val:
            basics.pop_up_message("Error", "Scan Law ID cannot be empty.", "error")
            return

        int(scan_law_id_val) # This will raise ValueError if not an integer

        if not mm_slot_val:
            # This shouldn't happen with readonly combobox with default, but check anyway
            basics.pop_up_message("Error", "Mass Memory Slot ID not selected.", "error")
            return

        self.act_params.scan_law_id = scan_law_id_val
        self.act_params.mm_slot = mm_slot_val

        functions.generate_outputs(act_params=self.act_params)
        basics.pop_up_message("Success", "Outputs generated successfully.", "info")

    def back(self):
        """Handle back navigation"""
        if self.app is not None and hasattr(self.app, "back"):
            self.app.back()
        else:
            logger.error("No app instance with 'back' method available in MISS_SCE_GUI")
            basics.pop_up_message("Navigation Error", "No previous screen to return to.", "warning")
