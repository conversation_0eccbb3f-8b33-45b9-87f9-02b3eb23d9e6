<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<paf>
    <header>
        <COMMENT>PAF_N_LIR_I031.xml updated by CDC for MTG-I_1</COMMENT>
        <CREATION_DATE>2024-091T11:43:54.557Z</CREATION_DATE>
        <ORIGINATOR>OPIT</ORIGINATOR>
    </header>
    <body>
        <segment>
            <metadata>
                <OBJECT_NAME>MTG-I1</OBJECT_NAME>
                <OBJECT_ID>MTI1</OBJECT_ID>
            </metadata>
            <data>
                <parameters count="24">
                    <integer>
                        <name>ICIDVer</name>
                        <value>1</value>
                        <minValue>0</minValue>
                        <maxValue>65534</maxValue>
                    </integer>
                    <integer>
                        <name>ICID</name>
                        <value>1</value>
                        <minValue>0</minValue>
                        <maxValue>65534</maxValue>
                    </integer>		
                    <integer>
                        <COMMENT>Number of (T_EXP, N_CAL_IMAGES) tuplets</COMMENT>
                        <name>CAL_N_EXP</name>
                        <value>1</value>
                        <minValue>1</minValue>
                        <maxValue>1024</maxValue>
                    </integer>
                    <integer>
                        <COMMENT>Number of CALIBRATION images requiring each T_EXP</COMMENT>
                        <name>CAL1_N_IMG</name>
                        <value>10</value>
                        <minValue>1</minValue>
                        <maxValue>1024</maxValue>
                    </integer>
                    <integer>
                        <COMMENT>Exposure Time </COMMENT>
                        <name>CAL1_T_EXP</name>
                        <value>10098790</value>
                        <minValue>0</minValue>
                        <maxValue>16777216</maxValue>
                    </integer>
                    <enumeration>
                        <COMMENT>FEE Id</COMMENT>
                        <name>FEE_ID</name>
                        <value>1</value>
                        <enumValue>
                            <key>1</key>
                            <value>FEE_1</value>
                        </enumValue>
                        <enumValue>
                            <key>2</key>
                            <value>FEE_2</value>
                        </enumValue>
                        <enumValue>
                            <key>3</key>
                            <value>FEE_3</value>
                        </enumValue>
                        <enumValue>
                            <key>4</key>
                            <value>FEE_4</value>
                        </enumValue>
                    </enumeration>
                </parameters>
            </data>
        </segment>
    </body>
</paf>