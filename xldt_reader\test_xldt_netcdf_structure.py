#!/usr/bin/env python3
"""
Test that XLDT output shows the same structure as NetCDF data.
"""

import sys
sys.path.append('.')
from xldt_reader_standalone import XLDTReader, format_xldt_cdc_input_style

def test_xldt_netcdf_structure():
    """Test that XLDT displays the same structure as NetCDF."""
    
    print("🔍 Testing XLDT vs NetCDF Structure Match")
    print("=" * 60)
    
    # Test file
    xldt_file = "inputs/ScanLaw_East_Summer_16386.xldt"
    config_file = "config/IRS/IRS_SL_Conf.csv"
    
    try:
        # Read XLDT file
        reader = XLDTReader(config_file)
        parsed_data = reader.read_xldt_file(xldt_file)
        
        if parsed_data:
            print("✅ XLDT file parsed successfully")
            
            # Format in CDC style (should match NetCDF structure)
            cdc_output = format_xldt_cdc_input_style(parsed_data)
            
            print("\n📊 XLDT Data in NetCDF Structure:")
            print("=" * 50)
            print(cdc_output)
            
            # Check if the output contains the expected sections
            expected_sections = [
                "🔹 FDA Variables:",
                "🔹 Dwell Position Variables:",
                "🔹 LAC Pointer Variables:",
                "🔹 MPA Profile Variables:"
            ]
            
            print("\n🎯 Structure Validation:")
            print("=" * 30)
            
            for section in expected_sections:
                if section in cdc_output:
                    print(f"✅ Found: {section}")
                else:
                    print(f"❌ Missing: {section}")
            
            # Check for specific variables
            expected_vars = [
                "fda_mp_pointer_alpha",
                "fda_mp_pointer_epsilon", 
                "dwell_position_alpha",
                "dwell_position_epsilon",
                "lac_start",
                "lac_end",
                "mpa_profile_param1",
                "mpa_profile_type"
            ]
            
            print("\n🔍 Variable Validation:")
            print("=" * 25)
            
            for var in expected_vars:
                if var in cdc_output:
                    print(f"✅ Found: {var}")
                else:
                    print(f"❌ Missing: {var}")
            
            # Check if values match expected pattern
            if "fda_mp_pointer_alpha = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]" in cdc_output:
                print("\n🎉 SUCCESS: FDA alpha values match expected pattern!")
            else:
                print("\n❌ ISSUE: FDA alpha values don't match expected pattern")
                
        else:
            print("❌ Failed to parse XLDT file")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_xldt_netcdf_structure()
