# Standard library imports
import os

import pytest
# Local imports
from src.algorithms.uvn_functions.converter.netcdf_Converter import \
    NetcdfConverter
from tests.helpers import assert_ncd_files, load_input_files
from tests.utils.test_uvn_utils import Helper


@pytest.fixture(scope="class")
def suspend_capture(pytestconfig):
    """Use to make user interactions in the test or order stops for modify configurations file, HW..."""

    class suspend_guard:
        def __init__(self):
            self.capmanager = pytestconfig.pluginmanager.getplugin("capturemanager")

        def __enter__(self):
            self.capmanager.suspend_global_capture(in_=True)

        def __exit__(self, _1, _2, _3):
            self.capmanager.resume_global_capture()

    yield suspend_guard()


@pytest.fixture()
def load_UVN_test_files(request):
    """load test configurations

    Args:
        request (_type_): use to attach instance variable
    """

    # Set global  variables for testing
    global satellite
    global instrument
    satellite = "MTG-S 1"
    instrument = "UVN"
    # Load input files
    load_input_files(instrument)

    test_files = Helper.get_test_directory()

    # Read config file to get the location of the input and output directory
    input_directory = os.path.join(test_files, "Input")

    output_directory = os.path.join(test_files, "Output")

    # Set the paths of the input and output file
    test_file_name = "MTS1_UVN_PTD_ref.nc"
    output_netcdf_file = os.path.join(output_directory, test_file_name)
    input_netcdf_file = os.path.join(input_directory, test_file_name)

    # attach the instance variable to the request
    request.cls.input_netcdf_file = input_netcdf_file
    request.cls.output_netcdf_file = output_netcdf_file

    # clean up
    Helper.clear_directory(os.path.join(test_files, "Output"))

    yield

    # clean up
    Helper.clear_directory(os.path.join(test_files, "Output"))


@pytest.mark.usefixtures("load_UVN_test_files")
class TestNetcdfConverter:
    def test_convert_netcdf(self):
        """
        This test verifies that:
        1. A netcdf file is correctly converted to a ptd table dictionary
        2. The output ptd table dictionary is correctly converted to a netcdf file.
        3. The original netcdf file has the same content as the generated netcdf file.

        Parameters
        ----------

        :file_name: NetCDF file path
        :expected: result of the comparison of the original netcdf file against the generated one
        """

        # Execute the method  to generate a ptd dictionary format
        netcdf_conv = NetcdfConverter()
        input_netcdf_ptd_dict = netcdf_conv.convert_netcdf_to_dict(
            self.input_netcdf_file
        )

        # from the ptd dictionary table generate the netcdf file
        netcdf_conv.convert_dict_to_netcdf(
            dict_ptd=input_netcdf_ptd_dict, filename_ncd=self.output_netcdf_file
        )
        # from the generated netcdf file , convert to a ptd dictionary
        output_netcdf_ptd_dict = netcdf_conv.convert_netcdf_to_dict(
            self.output_netcdf_file
        )

        # compare content of both dictionaries
        assert input_netcdf_ptd_dict == output_netcdf_ptd_dict
        assert assert_ncd_files([self.input_netcdf_file], [self.output_netcdf_file])
