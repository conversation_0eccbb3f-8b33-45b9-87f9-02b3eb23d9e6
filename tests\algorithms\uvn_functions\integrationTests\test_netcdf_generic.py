# Standard library imports
import os

import pytest
from src.algorithms.uvn_functions.converter.excel_converter import \
    ExcelConverter
# Local imports
from src.algorithms.uvn_functions.converter.memory_image_converter import \
    MemoryImageConverter
from src.algorithms.uvn_functions.converter.netcdf_Converter import \
    NetcdfConverter
from src.utils.import_utils import config
from tests.helpers import (assert_excel_files, assert_img_files, assert_obj,
                           load_input_files)
from tests.utils.test_uvn_utils import Helper


@pytest.fixture(scope="class")
def suspend_capture(pytestconfig):
    """Use to make user interactions in the test or order stops for modify configurations file, HW..."""

    class suspend_guard:
        def __init__(self):
            self.capmanager = pytestconfig.pluginmanager.getplugin("capturemanager")

        def __enter__(self):
            self.capmanager.suspend_global_capture(in_=True)

        def __exit__(self, _1, _2, _3):
            self.capmanager.resume_global_capture()

    yield suspend_guard()


@pytest.fixture()
def load_UVN_test_files(request):

    # Set global  variables for testing
    global satellite
    global instrument
    satellite = "MTG-S 1"
    instrument = "UVN"

    # Load input files
    load_input_files(instrument)

    # Setup test files directory
    test_directory = os.path.join(config.CONFIG_VALUES["test_files"])
    request.cls.test_directory = test_directory

    # Get the following test inputs files format  xls , ncdf and image memory
    input_file_xls, input_file_ncd, input_file_img = Helper.get_test_files(
        os.path.join(test_directory, instrument, "Input")
    )

    # Get the following test output files format  xls , ncdf and image memory
    output_file_xls, output_file_ncd, output_file_img = Helper.get_test_files(
        os.path.join(test_directory, instrument, "Output")
    )

    # Attach the variables to the request, thus they can be used as instance variables
    Helper.attach_input_instance_variable(
        request, input_file_xls, input_file_ncd, input_file_img
    )
    Helper.attach_output_instance_variable(
        request, output_file_xls, output_file_ncd, output_file_img
    )

    # Get netcdf  file ptd dictionary used for testing
    netcdf_conv = NetcdfConverter()

    # Attach the variables to the request, thus they can be used as instance variables
    request.cls.input_netcdf_ptd_dict = netcdf_conv.convert_netcdf_to_dict(
        input_file_ncd
    )

    # Delete test files from previous validation runs
    Helper.tear_down(output_file_xls, output_file_ncd, output_file_img)

    yield

    # clean up
    Helper.tear_down(output_file_xls, output_file_ncd, output_file_img)


@pytest.mark.usefixtures("load_UVN_test_files")
class TestNetcdfIntegration:
    def test_excel(self):
        """
        The purpose of the test is to verify that
        the PTD dictionary of an excel file can be used to generate a PTD netcdf file.

        """

        # Set the excel input test file
        excel_conv = ExcelConverter()

        # Generate an excel file with  a netcdf PTD dictionary
        excel_conv.convert_dict_to_excel(
            self.input_netcdf_ptd_dict, self.output_file_xls
        )

        # Convert the excel  generated file formats back to ptd dictionary:
        dict_ptd_xls = excel_conv.convert_excel_to_dict(self.output_file_xls)

        # Compare resulting ptd dictionaries :
        assert dict_ptd_xls == self.input_netcdf_ptd_dict

        assert_excel_files(self.output_file_xls, self.input_file_xls)

    def test_img_memory(self):
        """
        Test file conversion to CDC UVN memory_image_file
        """

        # Output files to be generated during the test:
        file_path_img = os.path.join(
            self.test_directory, "UVN", "Output", "MTS1_UVN_PTD_test_ASW_SDRAM.img"
        )
        # Generate sdram memory image
        mem_img = MemoryImageConverter("SDRAM")

        # Generate image memories
        mem_img.convert_dict_to_image(
            dict_ptd=self.input_netcdf_ptd_dict,
            filename_img=file_path_img,
            icu_id="ICU_A",
            software_id="ASW",
        )

        dict_ptd_img1 = mem_img.convert_image_to_dict(filename_img=file_path_img)

        assert assert_obj(self.input_netcdf_ptd_dict, dict_ptd_img1)
        assert assert_img_files([self.input_file_img], [file_path_img])
