
"""
Custom-styled widgets for the MTG CDC application.
Provides modern alternatives to standard tkinter widgets.
"""

from tkinter import ttk
from typing import Optional

from .gui_constants import (
    # Modern colors
    PRIMARY_COLOR, SECONDARY_COLOR, ACCENT_COLOR, SUCCE<PERSON>_COLOR,
    HEADER_PRIMARY, HEADER_SECONDARY, HEADER_SUCCESS, HEADER_ACCENT,
    # Card-specific colors  
    CARD_PRIMARY_BG, CARD_PRIMARY_HEADER, CARD_PRIMARY_BORDER,
    CARD_SECONDARY_BG, CARD_SECONDARY_HEADER, CARD_SECONDARY_BORDER,
    CARD_ACCENT_BG, CARD_ACCENT_HEADER, CARD_ACCENT_BORDER,
    CARD_SUCCESS_BG, CARD_SUCCESS_HEADER, CARD_SUCCESS_BORDER,
    CARD_ERROR_BG, CARD_ERROR_HEADER, CARD_ERROR_BORDER,
    CARD_WARNING_BG, CARD_WARNING_HEADER, CARD_WARNING_BORDER,
    # Base colors
    CARD_BACKGROUND, CARD_SHADOW, BORDER_COLOR,
    TEXT_PRIMARY, TEXT_ON_PRIMARY, DEFAULT_FONT
)


class CardFrame(ttk.Frame):
    """
    Modern card-like widget with optional header/footer, border color, and padding.
    Uses the new comprehensive color system for better visual hierarchy.
    """

    def __init__(
        self,
        parent,
        title: str,
        header_color: str = PRIMARY_COLOR,
        content_bg: str = CARD_BACKGROUND,
        border_color: str = None,
        padding: int = 10,
        show_header: bool = True,
        show_footer: bool = False,
        fixed_height: Optional[int] = None,
        fixed_width: Optional[int] = None,
        **kwargs
    ):
        """Create a new CardFrame widget with enhanced color options."""
        kwargs.setdefault("style", "Card.TFrame")
        super().__init__(parent, **kwargs)

        self.title = title
        self.header_color = header_color
        self.content_bg = content_bg
        self.border_color = border_color or header_color
        self.fixed_height = fixed_height
        self.fixed_width = fixed_width
        self._container = None
        self._header = None
        self._footer = None
        self._content = None

        # Build the card structure
        self._build_card(title, padding, show_header, show_footer)
        
    def _build_card(self, title: str, padding: int, show_header: bool, show_footer: bool):
        """Build the card structure with enhanced styling."""
        style = ttk.Style()
        
        # Shadow frame with subtle shadow
        shadow_frame = ttk.Frame(self, style="CardShadow.TFrame")
        shadow_frame.pack(fill="both", expand=True)

        # Border frame with custom border color
        border_style_name = f"CardBorder_{id(self)}.TFrame"
        style.configure(border_style_name, background=self.border_color, borderwidth=0)
        border_frame = ttk.Frame(shadow_frame, style=border_style_name)
        border_frame.pack(fill="both", expand=True, ipadx=0, ipady=0)

        # Inner frame with custom background
        inner_style_name = f"CardInner_{id(self)}.TFrame"
        style.configure(inner_style_name, background=self.content_bg)
        inner_frame = ttk.Frame(border_frame, style=inner_style_name)
        inner_frame.pack(fill="both", expand=True, padx=0, pady=0)

        if show_header:
            self._create_header(inner_frame, title, padding)

        # Content frame with custom background
        content_style_name = f"CardContent_{id(self)}.TFrame"
        style.configure(content_style_name, background=self.content_bg)
        self._content = ttk.Frame(inner_frame, style=content_style_name)
        self._content.pack(fill="both", expand=True, padx=padding, pady=padding)

        if self.fixed_height is not None:
            self._content.configure(height=self.fixed_height)
        if self.fixed_width is not None:
            self._content.configure(width=self.fixed_width)
        if self.fixed_height is not None or self.fixed_width is not None:
            self._content.pack_propagate(False)

        if show_footer:
            self._create_footer(inner_frame, padding)

    def _create_header(self, parent, title, padding):
        """Create header with enhanced styling."""
        style = ttk.Style()
        
        # Header frame with custom background color
        header_style_name = f"CardHeader_{id(self)}.TFrame"
        style.configure(header_style_name, background=self.header_color)
        self._header = ttk.Frame(parent, style=header_style_name)
        self._header.pack(fill="x", side="top")

        # Header label with enhanced styling
        header_label_style = f"CardHeaderLabel_{id(self)}.TLabel"
        style.configure(
            header_label_style, 
            background=self.header_color, 
            foreground="white", 
            font=(DEFAULT_FONT[0], DEFAULT_FONT[1], "bold")
        )
        title_label = ttk.Label(self._header, text=title, style=header_label_style)
        title_label.pack(padx=padding, pady=padding//2)

    def _create_footer(self, parent, padding):
        separator = ttk.Separator(parent, orient="horizontal")
        separator.pack(fill="x", padx=1)
        self._footer = ttk.Frame(parent, style="CardFooter.TFrame")
        self._footer.pack(fill="x", side="bottom", padx=padding, pady=padding//2)


    def get_content_frame(self) -> ttk.Frame:
        """Return the content frame for placing widgets."""
        if self._content is None:
            raise RuntimeError("Content frame not initialized")
        return self._content


    def get_header_frame(self) -> Optional[ttk.Frame]:
        """Return the header frame if it exists."""
        return self._header


    def get_footer_frame(self) -> Optional[ttk.Frame]:
        """Return the footer frame if it exists."""
        return self._footer


class TitledCard(CardFrame):
    """CardFrame with just a title and content area."""
    def __init__(self, parent, title: str, header_color: str = PRIMARY_COLOR,
                 content_bg: str = CARD_BACKGROUND, padding: int = 10, **kwargs):
        super().__init__(
            parent,
            title=title,
            header_color=header_color,
            content_bg=content_bg,
            padding=padding,
            show_header=True,
            show_footer=False,
            **kwargs
        )


class PrimaryCard(TitledCard):
    """Blue-themed card for primary content (VCU Memory Side, Channel Selection)."""
    def __init__(self, parent, title: str, padding: int = 10, **kwargs):
        super().__init__(
            parent, 
            title, 
            header_color=HEADER_PRIMARY,  # Blue 600 - Professional blue
            content_bg=CARD_PRIMARY_BG,
            padding=padding, 
            **kwargs
        )

class SecondaryCard(TitledCard):
    """Indigo-themed card for secondary content (Memory Image Type, Activity Selection)."""
    def __init__(self, parent, title: str, padding: int = 10, **kwargs):
        super().__init__(
            parent, 
            title, 
            header_color=HEADER_SECONDARY,  # Indigo 600 - Complementary
            content_bg=CARD_SECONDARY_BG,
            padding=padding, 
            **kwargs
        )

class AccentCard(TitledCard):
    """Violet-themed card for accent content with subtle background."""
    def __init__(self, parent, title: str, padding: int = 10, **kwargs):
        super().__init__(
            parent, 
            title, 
            header_color=HEADER_ACCENT,  # Violet 600 - Innovation
            content_bg=CARD_ACCENT_BG,
            padding=padding, 
            **kwargs
        )

class SuccessCard(TitledCard):
    """Emerald-themed card for success content (ICID Configuration)."""
    def __init__(self, parent, title: str, padding: int = 10, **kwargs):
        super().__init__(
            parent, 
            title, 
            header_color=HEADER_SUCCESS,  # Emerald 600 - Success/config
            content_bg=CARD_SUCCESS_BG,
            padding=padding, 
            **kwargs
        )

class ErrorCard(TitledCard):
    """Red-themed card for error content with subtle background."""
    def __init__(self, parent, title: str, padding: int = 10, **kwargs):
        super().__init__(
            parent, 
            title, 
            header_color=CARD_ERROR_HEADER,
            content_bg=CARD_ERROR_BG,
            padding=padding, 
            **kwargs
        )

class WarningCard(TitledCard):
    """Amber-themed card for warning content with subtle background."""
    def __init__(self, parent, title: str, padding: int = 10, **kwargs):
        super().__init__(
            parent, 
            title, 
            header_color=CARD_WARNING_HEADER,
            content_bg=CARD_WARNING_BG,
            padding=padding, 
            **kwargs
        )
