"""
CDC GUI Package

This package contains the graphical user interface components for the CDC project:
- Main GUI framework and common utilities
- Instrument-specific GUI tabs:
  * FCI (Flexible Combined Imager)
  * IRS (Infrared Sounder)
  * LI (Lightning Imager)
  * UVN (Ultraviolet Visible Near-infrared)

The GUI is built using tkinter and follows a modern, unified interface
structure with consistent styling and error handling.
"""

__version__ = "4.0"

# Import basics setup first
from ..utils import import_utils

# Then other imports
from ..logger_wrapper import logger
logger.info("CDC GUI Package initialized")

# Import core frames and utilities
from .frames import (
    BaseFrame,
    AppWindow,
    InstrumentHandler,
    UnifiedFrame,
    create_app
)

# Import and register instrument GUIs
from .FCI_GUI import FCI_GUI
from .IRS_GUI import IRS_GUI
from .LI_GUI import LI_GUI
from .UVN_GUI import UVN_GUI

# Register instrument activities with InstrumentHandler
for gui in [FCI_GUI, IRS_GUI, LI_GUI, UVN_GUI]:
    gui.register_activities(InstrumentHandler)

# Define public API
__all__ = [
    # Core components
    'BaseFrame',
    'AppWindow',
    'UnifiedFrame',
    'create_app',
    'InstrumentHandler',

    # Instrument GUI modules
    'FCI_GUI',
    'LI_GUI',
    'IRS_GUI',
    'UVN_GUI',
]
