# Standard library imports
from datetime import datetime
from typing import Dict, List
import math
import os
import pandas as pd

# Local imports
from src.utils.import_utils import config
from src.utils.conversion_utils import dec2bin, dec2hex, hex2dec
from src.logger_wrapper import logger
from src.utils.netcdf_utils import read_netCDF
from src.utils.activity_params import ActivityParams
from .memory_image_file_FCI import memory_image_file_FCI

# Type aliases
MemoryMapType = List[List[str]]

# Global variables for storing NOF (Numerical Offset) data
glob_NOF_StartAdd: Dict[str, int] = {}  # Maps channel names to their starting memory addresses
glob_NOF_BlkLen: Dict[str, int] = {}    # Maps channel names to their block lengths in memory

def _process_ir_channels(VSM_dict: Dict[str, List[int]], group: int, dw_start: int, num_channels: int, direction: int = 1) -> int:
    """Process IR channel mapping for a specific group.
    
    Args:
        VSM_dict: Dictionary containing VSM values
        group: IR group number (1-3)
        dw_start: Starting data word position
        num_channels: Number of channels to process
        direction: Direction to adjust DW (1 for increasing, -1 for decreasing)
        
    Returns:
        Updated dw_start value
    """
    logger.debug(f"Processing IR{group} channels starting at DW{dw_start}")
    for int_channel in range(1, num_channels + 1):
        name = f"IR{group}_MAPPING_CH{int_channel}"
        if name in VSM_dict:
            for pixels in range(len(VSM_dict[name])):
                dw = dw_start + (direction * int(pixels / 8))
                VSM_dict[f"IR{group}_DA_MAPPING_DW{dw}_{pixels + 1}"] = VSM_dict[name][pixels]
            
            # Special handling for IR3 CH1
            if group == 3 and int_channel == 1:
                dw_start = dw + 29
            else:
                dw_start = dw + direction
                
            del VSM_dict[name]
            
    return dw_start

def _process_nir_vis_channels(VSM_dict: Dict[str, List[int]], channel_type: str, dw_start: int, num_channels: int, direction: int = 1) -> None:
    """Process NIR or VIS channel mapping.
    
    Args:
        VSM_dict: Dictionary containing VSM values
        channel_type: Channel type ('NIR' or 'VIS')
        dw_start: Starting data word position
        num_channels: Number of channels to process
        direction: Direction to adjust DW (1 for increasing, -1 for decreasing)
    """
    logger.debug(f"Processing {channel_type} channels starting at DW{dw_start}")
    for int_channel in range(1, num_channels + 1):
        name = f"{channel_type}_MAPPING_CH{int_channel}"
        if name in VSM_dict:
            for pixels in range(len(VSM_dict[name])):
                dw = dw_start + (direction * int(pixels / 8))
                VSM_dict[f"{channel_type}_DA_MAPPING_DW{dw}_{pixels + 1}"] = VSM_dict[name][pixels]
            dw_start = dw + direction
            del VSM_dict[name]

def FCI_VCU_MEM_PIXMAP(
    act_params: ActivityParams,
    satellite: str,
    used_files: List[str],
    Mem_Map: pd.DataFrame,
    df_VSM: pd.DataFrame,
    lst_channels: List[str],
    n_channel: int,
) -> None:
    """Generate FCI VCU Pixel Mapping Memory configuration.
    
    Args:
        act_params: Activity parameters containing instrument configuration
        used_files: Dictionary mapping file types to their paths
        Mem_Map: Memory map dataframe
        df_VSM: VSM configuration dataframe
        lst_channels: List of channels to process
        n_channel: Channel number being processed
        
    The function performs these main steps:
    1. Read and process netCDF input files
    2. Map channel configurations to memory addresses
    3. Generate memory image files
    """
    try:
        # Initialize variables and get config
        VSM_dict: Dict[str, List[int]] = {}
        dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
        str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
        main_files=["INSTCONF"]

        # STEP 1: Read netCDF files and prepare VSM mappings
        logger.info("Reading and processing netCDF files")
        dict_netCDF = read_netCDF(
            act_params,
            satellite,
            main_files,
            used_files,
            dict_config
        )

        # Get VSM variables for VCU mapping
        VSM_Clean = df_VSM[df_VSM["Activity"].str.contains("VCU_MAPP")]
        VSM_Clean.set_index("Name", inplace=True)

        # Map netCDF values to mnemonics
        for variable in VSM_Clean.index:
            VSM_dict[VSM_Clean.loc[variable, "Mnemonic in Map"]] = dict_netCDF[variable].tolist()

        # Process different channel types
        _process_ir_channels(VSM_dict, 1, 5, 4, 1)      # IR1 channels (forward)
        _process_ir_channels(VSM_dict, 2, 88, 2, -1)    # IR2 channels (reverse)
        _process_ir_channels(VSM_dict, 3, 5, 3, 1)      # IR3 channels (forward)
        _process_nir_vis_channels(VSM_dict, "NIR", 4, 3, 1)    # NIR channels (forward)
        _process_nir_vis_channels(VSM_dict, "VIS", 167, 5, -1) # VIS channels (reverse)

        # STEP 2: Create memory map with values
        logger.info("Creating memory map")
        Map_Clean = Mem_Map[
            Mem_Map["Parameter Logical Identifier"].isin(list(VSM_dict.keys()))
        ].copy()
        Map_Clean["Value"] = Map_Clean["Parameter Logical Identifier"].map(VSM_dict)

        if len(lst_channels) == 17:
            # logger.info("Processing multiple channels for TEEEST: " + str(len(lst_channels)))
            lst_channels = ["All"]

        if lst_channels == ["All"]:
            logger.info("Processing all channels for TEEEST")

        # Filter for selected channels
        Map_Final = Map_Clean if lst_channels == ["All"] else Map_Clean[Map_Clean["Channel"].isin(lst_channels)]

        # STEP 3: Generate memory image content
        logger.info("Generating memory image content")
        Bytes_Dict_RAM: MemoryMapType = [["Start", "Count", "Data"]]

        for add in sorted(set(Map_Final["RAM Addr (Hex)"].to_list())):
            # Process each memory address
            Map_Add = Map_Final[Map_Final["RAM Addr (Hex)"] == add]
            
            # Combine all bits for this address
            Bytes_Bin_All = ""
            AddLen_Bits = 0
            
            for sb in sorted(Map_Add["Start Bit"].to_list()):
                Map_Bit = Map_Add[Map_Add["Start Bit"] == sb]
                Val = int(Map_Bit["Value"].values[0])
                Len = int(Map_Bit["Length (bits)"].values[0])
                Bytes_Bin_All += dec2bin(Val).zfill(Len)
                AddLen_Bits += Len

            # Pad to 16-bit boundary (SAU=2)
            Bits_Left = 16 - AddLen_Bits
            if Bits_Left > 0:
                Bytes_Content_Bin = Bytes_Bin_All + "0" * Bits_Left
            else:
                Bytes_Content_Bin = Bytes_Bin_All

            # Convert to bytes
            AddLen = len(Bytes_Content_Bin) // 8
            Bytes = dec2hex(int(Bytes_Content_Bin, 2)).zfill(2 * AddLen)
            AddLenSAU = AddLen // 2

            # Split into blocks if needed
            if AddLenSAU > dict_config["count_max"]:
                logger.info("Splitting large data block into multiple memory units")
                # First block
                Bytes_Dict_RAM.append([
                    add[2:],
                    dec2hex(dict_config["count_max"]),
                    Bytes[0 : dict_config["count_max"] * 2]
                ])
                
                # Calculate remaining blocks
                line_slices = math.floor(AddLenSAU / dict_config["count_max"])
                
                for slices in range(line_slices):
                    add_2 = dec2hex(hex2dec(add) + dict_config["count_max"] * (slices + 1))
                    
                    # Handle last block
                    if slices == line_slices - 1:
                        Number_Bytes = AddLenSAU % dict_config["count_max"]
                        End_index = AddLenSAU * 2
                    else:
                        Number_Bytes = dict_config["count_max"]
                        End_index = ((dict_config["count_max"] * (slices + 1)) * 2) + dict_config["count_max"] * 2
                    
                    Start_index = (dict_config["count_max"] * (slices + 1)) * 2
                    
                    if Number_Bytes != 0:
                        Bytes_Dict_RAM.append([
                            add_2,
                            dec2hex(Number_Bytes),
                            Bytes[Start_index:End_index]
                        ])
            else:
                Bytes_Dict_RAM.append([add[2:], dec2hex(AddLen), Bytes])

        # STEP 4: Store memory range information for PAF
        NOF_start_add = hex2dec(Bytes_Dict_RAM[1][0])
        NOF_end_add = hex2dec(Bytes_Dict_RAM[-1][0]) + int(int(Bytes_Dict_RAM[-1][1]) / 2)
        LenSAU = NOF_end_add - NOF_start_add

        # Store for global access
        glob_NOF_StartAdd[lst_channels[0]] = NOF_start_add
        glob_NOF_BlkLen[lst_channels[0]] = LenSAU

        # STEP 5: Create memory image file
        logger.info("Creating memory image file")
        device = f"VCU_RAM{'N' if act_params.side == 'Nominal' else 'R'}"
        part = f"PART{n_channel}_" if n_channel != 0 else ""
        
        # Generate filename
        str_filename = (
            f"{satellite[0:4]}{satellite[6]}_"
            f"{dict_config['file_class']}_OBS_MIMG_000_{device}_"
            f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{part}"
            f"{str(act_params.icid).zfill(2)[-2:]}{str(act_params.icid_ver).zfill(2)[-2:]}.IMG"
        )
        str_path_full = os.path.join(str_output_folder, str_filename)

        # Create memory image
        memory_image_file_FCI(
            Bytes_Dict_RAM,
            "VCU_PIXMAPPING",
            str_path_full,
            satellite,
            device,
            lst_channels
        )
        
        logger.info(f"Successfully generated pixel mapping memory image: {str_filename}")
        
    except Exception as e:
        logger.error(f"Error in FCI VCU pixel mapping generation: {str(e)}")
        raise
