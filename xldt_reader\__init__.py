"""
XLDT Reader Package
==================

A comprehensive XLDT (Transfer Layer Data) file reader for MTG satellite operations.

This package provides tools to read, parse, and analyze XLDT binary files used in
satellite operations, particularly for scan law data.

Main Components:
- XLDTReader: Core reader class for parsing XLDT files
- Command-line tools for batch processing
- Configuration support for IRS and FCI instruments
- CRC validation for data integrity

Usage:
    from xldt_reader_standalone import XLDTReader
    
    reader = XLDTReader(config_path="config/IRS/IRS_SL_Conf.csv")
    data = reader.read_xldt_file("inputs/ScanLaw_Central_Summer_16384.xldt")
"""

__version__ = "1.0.0"
__author__ = "CDC-S Development Team"
__description__ = "XLDT File Reader for MTG Satellite Operations"

# Import main classes for easy access
try:
    from .xldt_reader_standalone import XLDTReader, XLDTHeader, XLDTSection, create_xldt_file
    __all__ = ['XLDTReader', 'XLDTHeader', 'XLDTSection', 'create_xldt_file']
except ImportError:
    # Fallback if imports fail
    __all__ = []

# Package metadata
PACKAGE_INFO = {
    'name': 'xldt_reader',
    'version': __version__,
    'description': __description__,
    'author': __author__,
    'supported_formats': ['XLDT'],
    'supported_instruments': ['IRS', 'FCI'],
    'features': [
        'Binary XLDT file parsing',
        'Configuration-driven section extraction',
        'CRC validation',
        'Multiple output formats (JSON, summary, detailed)',
        'Command-line interface',
        'Batch processing support'
    ]
}
