# Standard library imports
import datetime
import math
import os
from typing import List, Any
import numpy as np
import pandas as pd
# Local imports
from src.utils.import_utils import config  # Removed floppy
from src.logger_wrapper import logger
from src.utils.conversion_utils import dec2hex, hex2dec
from src.utils.netcdf_utils import read_netCDF
from src.utils.activity_params import ActivityParams
from src.utils.excel_utils import Read_Sheet

def IRS_SELUT(act_params: ActivityParams, satellite: str) -> None:
    """Generate IRS Scan Encoder LUT TC Stack."""
    # Read SELUT configuration
    dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
    
    # Debug log the configuration
    logger.info(f"Using instrument configuration: {dict_config}")
    logger.info(f"Slicer dict type: {type(dict_config['slicer_dict'])}")
    logger.info(f"Slicer dict content: {dict_config['slicer_dict']}")

    df_SELUT_config = pd.read_csv(
        os.path.join(
            config.CONFIG_VALUES["config_folder"], act_params.instrument, "IRS_SELUT_Conf.csv"
        ),
        keep_default_na=False,
        index_col="Section"
    )

    # Initialize Variables
    new_dict = {}
    lst_DWs = []
    lst_arguments = []

    # Default fixed parameters
    Max_DW_per_TC = 475
    TC_ID = "DSWC0048"
    TC_Arg_SCAE_ID = "DSWH082X"
    TC_Arg_LUT_ID = "DSWH083X"
    TC_Arg_Offset_ID = "DSWH084X"
    TC_Arg_Len_ID = "DSWH085X"
    TC_Arg_Data_ID = "DSWH01XX"

    # Read netCDF data
    dict_netCDF = read_netCDF(
        act_params=act_params,
        satellite=satellite,
        main_files=["SCANENC"],
        used_files=["SCANENC"],
        instrument_conf=dict_config
    )

    count = 0
    to_iterate = ["scae_lut_index"]
    for sections in df_SELUT_config.index:
        logger.info(f"Processing section: {sections}")
        
        for variable in to_iterate:
            
            logger.info(f"Variable: {variable}")
            logger.info(f"Variable type: {type(variable)}")
            logger.info(f"Variable value: {dict_netCDF[variable]}")
            
            new_dict[sections] = dict_netCDF[variable]

        if count == 0:
            to_iterate = ["ns_scan_encoder_correction_onboard_lut"]
        elif count == 1:
            to_iterate = ["ew_scan_encoder_correction_onboard_lut"]

        count+=1

    logger.info(f"New dictionary: {new_dict}")
    logger.info(f"length of new_dict: {len(new_dict)}")
    logger.info(f"length of new_dict[NS]: {len(new_dict['NS'])}")
    
    
    kount = 0
    for val in new_dict['NS']:
        if val != 0: 
            kount += 1 
    
    logger.info(f"Number of non-zero values: {kount}")
    
    logger.info(f"length of new_dict[EW]: {len(new_dict['EW'])}")


    full_dict = np.concatenate([new_dict["NS"], new_dict["EW"]])

    logger.info(f"Full dictionary: {full_dict}")
    logger.info(f"length of full_dict: {len(full_dict)}")

    # Generate Lists of arguments
    int_parameters = len(full_dict)
    int_num_DW = int(int_parameters / 2)

    logger.info(f"Number of DW: {int_num_DW}")
    logger.info(f"Number of parameters: {int_parameters}")

    # Determine numeric SCAE ID (0 for Nominal, 1 for Redundant)
    scae_id_str = act_params.side.upper()
    if scae_id_str == "NOMINAL":
        scae_id_val = 0
    elif scae_id_str == "REDUNDANT":
        scae_id_val = 1
    else:
        logger.warning(
            f"Invalid SCAE side: {act_params.side}. Defaulting to Nominal (0)."
        )
        scae_id_val = 0

    for entries in range(int_num_DW):
        lst_DWs.append(
            hex2dec(
                dec2hex(full_dict[entries * 2]).zfill(2)[-2:]
                + dec2hex(full_dict[entries * 2 + 1]).zfill(2)[-2:]
            )
        )

    int_num_TC = math.ceil(int_num_DW / Max_DW_per_TC)

    for int_TC in range(int_num_TC):
        if (int_TC + 1) * Max_DW_per_TC < int_num_DW:
            int_Len = Max_DW_per_TC
        else:
            int_Len = int_num_DW - Max_DW_per_TC * int_TC

        lst_arguments.append(
            [
                scae_id_val,  # Use numeric SCAE ID
                new_dict["Index"].item(),
                int_TC * Max_DW_per_TC * 2,
                int_Len,
                lst_DWs[int_TC * Max_DW_per_TC : int_TC * Max_DW_per_TC + int_Len],
            ]
        )

    # Create TC filename and path
    str_full_path = os.path.join(
        config.CONFIG_VALUES["output_folder"],
        satellite,
        f"MTS{satellite.split(' ')[1]}_SCANENCLUT_TC_Stack_"
        f"{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_"
        f"ID{str(act_params.scan_encode_correction_lut_id).zfill(4)}.ssf"
    )

    # Generate TC Stack
    create_TCStack(
        TC_ID,
        TC_Arg_SCAE_ID,
        TC_Arg_LUT_ID,
        TC_Arg_Offset_ID,
        TC_Arg_Len_ID,
        TC_Arg_Data_ID,
        lst_arguments,
        satellite[-1],
        int_num_TC,
        str_full_path,
        scae_id_val_for_checksum=scae_id_val,  # Pass numeric SCAE ID for checksum TC
        total_16bit_words_for_checksum_len=int_num_DW # Pass total 16-bit words for checksum length calculation
    )


def create_TCStack(
    str_tc_id: str,
    str_tc_arg_SCAE: str,
    str_tc_arg_LUT: str,
    str_tc_arg_offset: str,
    str_tc_arg_len: str,
    str_tc_arg_data: str,
    lst_arguments: list,
    str_sat: str,
    int_num_TC: int,
    str_full_path: str,
    scae_id_val_for_checksum: int,  # New parameter for checksum TC's SCAE ID
    total_16bit_words_for_checksum_len: int  # Total 16-bit words from LUT update
):
    """Writing a TC Stack for the Scan Encoder LUT Update
    TC Stack created manually with the condition required (interlock, etc...) then used it as a template.
    Manual to check structure EGOS-MCS-S2K-ICD-0002
    """
    # Initialize  the list of commands with the base record,1685705431 is the generating time too lazy to calculate and pytest more difficult
    lst_TC_Stack = [f"2|OPIT|1685705431|0|OD-MI{str_sat}|0000000000|"]

    # Create the commands for DSWC0048 (Scan Encoder LUT Update)
    for int_TC in range(int_num_TC):
        # Reset Variables
        lst_step = []

        # Command Header Record
        # Note: lst_arguments[int_TC][0] is now the numeric SCAE ID
        lst_step.append(
            f"C|{str_tc_id}|0|1|0|0|0|0|1|5|0|0|0|{4 + lst_arguments[int_TC][3]}|0|1|0|0|||1||||0|1|9|"
        )

        # 1st Argument (SCAE_ID)
        lst_step.append(f"{str_tc_arg_SCAE}|0|4|2|1|{lst_arguments[int_TC][0]}|1|")

        # 2nd Argument (LUT_ID)
        lst_step.append(f"{str_tc_arg_LUT}|0|0|2|0|{lst_arguments[int_TC][1]}|1|")

        # 3rd Argument (Offset)
        lst_step.append(f"{str_tc_arg_offset}|0|0|2|0|{lst_arguments[int_TC][2]}|1|")

        # 4th Argument (Length)
        lst_step.append(f"{str_tc_arg_len}|0|0|2|0|{lst_arguments[int_TC][3]}|0|")

        for int_DW in range(lst_arguments[int_TC][3]):
            # 5th Argument (Data)
            lst_step.append(
                f"{str_tc_arg_data}|0|0|2|0|{lst_arguments[int_TC][4][int_DW]}|1|"
            )

        # Add the TC to the TC Stack
        lst_TC_Stack += lst_step

    # Add the new DSWC004S (Memory Checksum TC) command at the end
    # Define new command parameters
    tc_id_checksum = "DSWC004S"
    param_scae_id_checksum = "DSWH08WX"  # SCAE_ID_19
    param_addr_chksum = "DSWH08XX"       # Addr Chksum
    param_chksum_len = "DSWH08YX"        # Checksum Length

    # START ADDRESS for checksum (DSWH08XX):
    # This needs to be the 24-bit absolute start address of the memory region
    # that was just updated by the DSWC0048 TCs (e.g., the base address of the LUT
    # corresponding to lst_arguments[0][1] plus any initial offset if not starting from 0).
    # This value depends on the SCAE memory map and is not derivable from current script inputs.
    # Placeholder value 0 used. Ensure this is updated with the correct 24-bit address.
    # The SCAE ignores the last 2 bits of this address for 32-bit alignment.
    addr_chksum_val = 0  # Placeholder - MUST BE REPLACED with actual 24-bit start address

    # Checksum Length (DSWH08YX):
    # This is the length of the block of data on which to compute the checksum,
    # in number of 32-bit words (24-bit value).
    # Calculated from the total number of 16-bit words written by DSWC0048 TCs.
    chksum_len_val = total_16bit_words_for_checksum_len // 2

    # Command Header Record for DSWC004S (3 parameters)
    # Format: C|TC_ID|...|NumParamsFollow|...|TotalParamFields|...
    # NumParamsFollow = 3, TotalParamFields = 3
    lst_TC_Stack.append(
        f"C|{tc_id_checksum}|0|1|0|0|0|0|1|3|0|0|0|3|0|1|0|0|||1||||0|1|9|"
    )

    # Parameter 1: SCAE_ID_19 (DSWH08WX) - Type Enum (like existing SCAE_ID)
    # Format: PNAME | 0 | PFC=4 | PTC=2 | Unknown=1 | VALUE | PAF_UPDATERAW=1 (intermediate) |
    lst_TC_Stack.append(f"{param_scae_id_checksum}|0|4|2|1|{scae_id_val_for_checksum}|1|")

    # Parameter 2: Addr Chksum (DSWH08XX) - Type Unsigned Integer (24-bit)
    # Format: PNAME | 0 | PFC=0 | PTC=2 | Unknown=0 | VALUE | PAF_UPDATERAW=1 (intermediate) |
    lst_TC_Stack.append(f"{param_addr_chksum}|0|0|2|0|{addr_chksum_val}|1|")

    # Parameter 3: Checksum Length (DSWH08YX) - Type Unsigned Integer (24-bit)
    # Format: PNAME | 0 | PFC=0 | PTC=2 | Unknown=0 | VALUE | PAF_UPDATERAW=0 (last param) |
    lst_TC_Stack.append(f"{param_chksum_len}|0|0|2|0|{chksum_len_val}|0|")
    

    # Prepare the file lines
    str_TC_stack = "\n".join(lst_TC_Stack)

    # Create the output file
    with open(str_full_path, "w+b") as obj_TC_Stack:
        obj_TC_Stack.write(bytes("".join(str_TC_stack), "utf-8"))
