""" This class contains the implementation of the generation of memory image generator.
"""
from .activity_generator import ActivityGenerator
from .converter.memory_image_converter import MemoryImageConverter
from .converter.netcdf_Converter import NetcdfConverter
from .utils.file_utils import FileUtils

from src.logger_wrapper import logger

class MemoryImageGenerator(ActivityGenerator):
    def __init__(self, act_params, lst_parameters, sat, base_input_folder=None):
        """
         This is used to initialize a memory image generator

         Parameters
         ----------
        instrument_name: name of the instrument
        lst_parameters: list of selected parameter
        sat: Name of the satellite
        base_input_folder: Optional base folder for input files.
        """
        super().__init__()

        self.netcdf4_filepath = FileUtils.get_input_file_name(
            act_params.instrument, base_input_folder=base_input_folder
        )
        self.memory_image_filepath = FileUtils.set_output_fileName(
            sat, act_params, lst_par=lst_parameters, file_type="MEMORY_IMAGE"
        )

        self.software_id = lst_parameters[0]
        self.memory_id = lst_parameters[1]
        self.icu_id = lst_parameters[2]
        self.lst_ptd = lst_parameters[3]

    def generate(self):
        """Function to generate the memory image file.

        Parameters
        ----------

        """
        netcdf_conv = NetcdfConverter()
        netcdf_to_dict = netcdf_conv.convert_netcdf_to_dict(self.netcdf4_filepath)

        memory_image_converter = MemoryImageConverter(self.memory_id)
        memory_image_converter.convert_dict_to_image(
            netcdf_to_dict, self.memory_image_filepath, self.icu_id, self.software_id
        )
