"""
Theme manager for the CDC-S application

This module provides styling and theming functions for the CDC-S GUI components.
It centralizes visual styling to ensure consistency across the application.
"""

import tkinter as tk
from tkinter import ttk
import os
from typing import Optional, Callable, Literal
from .gui_constants import (
    BASE_FONT_SIZE, BUTTON_FONT_SIZE, DEFAULT_FONT, TITLE_FONT, HEADER_FONT,
    PRIMARY_COLOR, PRIMARY_HOVER, SECONDARY_COLOR, SECONDARY_HOVER, SECONDARY_TEXT,
    BACKGROUND_COLOR, ACCENT_COLOR, CARD_BACKGROUND, CARD_SHADOW, BORDER_COLOR,
    TEXT_PRIMARY, TEXT_SECONDARY, TEXT_ON_PRIMARY
)

class ThemeManager:
    """Manages application theming and styling"""    
    @staticmethod
    def setup_theme() -> None:
        """Initialize and apply modern application theme."""
        style = ttk.Style()
        available_themes = style.theme_names()
        for theme in ['clam', 'vista', 'xpnative', 'winnative']:
            if theme in available_themes:
                style.theme_use(theme)
                break
                
        # Modern base widget styles with enhanced contrast and spacing
        style.configure(".", font=DEFAULT_FONT)
        style.configure(
            "TLabel", 
            font=DEFAULT_FONT, 
            foreground=TEXT_PRIMARY, 
            background=CARD_BACKGROUND
        )
        style.configure(
            "TButton", 
            font=DEFAULT_FONT, 
            relief="flat", 
            borderwidth=0,
            focuscolor="none"
        )
        style.configure(
            "TCheckbutton", 
            font=DEFAULT_FONT, 
            foreground=TEXT_PRIMARY, 
            background=CARD_BACKGROUND, 
            padding=(8, 4),
            focuscolor="none"
        )
        style.configure(
            "TRadiobutton", 
            font=DEFAULT_FONT, 
            foreground=TEXT_PRIMARY, 
            background=CARD_BACKGROUND, 
            padding=(8, 4),
            focuscolor="none"
        )
        style.configure(
            "TEntry", 
            font=DEFAULT_FONT, 
            fieldbackground=CARD_BACKGROUND, 
            bordercolor=BORDER_COLOR,
            relief="solid", 
            borderwidth=1,
            focuscolor="none",
            padding=(8, 6)
        )
        style.map(
            "TEntry",
            bordercolor=[("focus", PRIMARY_COLOR)],
            lightcolor=[("focus", PRIMARY_COLOR)],
            darkcolor=[("focus", PRIMARY_COLOR)]
        )
        style.configure("TFrame", background=BACKGROUND_COLOR)
        style.configure("TLabelframe", background=BACKGROUND_COLOR)
        style.configure("TLabelframe.Label", foreground=TEXT_PRIMARY, font=DEFAULT_FONT)
        style.configure("TCombobox", padding=(8, 6))        
        ThemeManager._setup_card_styles(style)
        ThemeManager._setup_button_styles(style)
        style.configure("Horizontal.TSeparator", background=BORDER_COLOR, relief="flat")

    @staticmethod
    def _setup_card_styles(style: ttk.Style) -> None:
        """Configure modern card styles with clean, professional appearance."""
        # Base card styles with subtle shadows and clean backgrounds
        style.configure("Card.TFrame", background=BACKGROUND_COLOR, relief="flat")
        style.configure("CardShadow.TFrame", background="#F3F4F6", borderwidth=0, relief="flat")  # Gray 100 for subtle shadow
        style.configure("CardBorder.TFrame", borderwidth=1, relief="solid")
        style.configure("CardInner.TFrame", background=CARD_BACKGROUND, relief="flat")
        style.configure("CardHeader.TFrame", relief="flat")
        style.configure("CardHeader.TLabel", foreground=TEXT_ON_PRIMARY, font=(DEFAULT_FONT[0], DEFAULT_FONT[1], "bold"))
        style.configure("CardFooter.TFrame", background=CARD_BACKGROUND, borderwidth=0, relief="flat")        
        style.configure("Card.TLabel", background=CARD_BACKGROUND, font=(DEFAULT_FONT[0], DEFAULT_FONT[1], "bold"), foreground="#000000")
        style.configure("Card.TCheckbutton", background=CARD_BACKGROUND, font=(DEFAULT_FONT[0], DEFAULT_FONT[1], "bold"), foreground="#000000", padding=(5, 2))
        style.configure("Card.TRadiobutton", background=CARD_BACKGROUND, font=(DEFAULT_FONT[0], DEFAULT_FONT[1], "bold"), foreground="#000000", padding=(5, 2))
        style.map("Card.TCheckbutton", foreground=[('active', PRIMARY_COLOR)])
        style.map("Card.TRadiobutton", foreground=[('active', PRIMARY_COLOR)])

        # Card-specific widget styles to match card backgrounds
        from .gui_constants import (
            CARD_PRIMARY_BG, CARD_SECONDARY_BG, CARD_ACCENT_BG, CARD_SUCCESS_BG
        )
          # Primary card widgets (blue background)
        style.configure("PrimaryCard.TRadiobutton", background=CARD_PRIMARY_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY, padding=(5, 2))
        style.configure("PrimaryCard.TCheckbutton", background=CARD_PRIMARY_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY, padding=(5, 2))
        style.configure("PrimaryCard.TLabel", background=CARD_PRIMARY_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY)
        style.configure("PrimaryCard.TFrame", background=CARD_PRIMARY_BG)
        style.configure("PrimaryCard.TCombobox", fieldbackground=CARD_PRIMARY_BG, background=CARD_PRIMARY_BG, foreground=TEXT_PRIMARY)
        style.map("PrimaryCard.TRadiobutton", foreground=[('active', PRIMARY_COLOR)])
        style.map("PrimaryCard.TCheckbutton", foreground=[('active', PRIMARY_COLOR)])
          # Secondary card widgets (indigo background)
        style.configure("SecondaryCard.TRadiobutton", background=CARD_SECONDARY_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY, padding=(5, 2))
        style.configure("SecondaryCard.TCheckbutton", background=CARD_SECONDARY_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY, padding=(5, 2))
        style.configure("SecondaryCard.TLabel", background=CARD_SECONDARY_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY)
        style.configure("SecondaryCard.TFrame", background=CARD_SECONDARY_BG)
        style.configure("SecondaryCard.TCombobox", fieldbackground=CARD_SECONDARY_BG, background=CARD_SECONDARY_BG, foreground=TEXT_PRIMARY)
        style.map("SecondaryCard.TRadiobutton", foreground=[('active', PRIMARY_COLOR)])
        style.map("SecondaryCard.TCheckbutton", foreground=[('active', PRIMARY_COLOR)])
          # Accent card widgets (violet background)
        style.configure("AccentCard.TRadiobutton", background=CARD_ACCENT_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY, padding=(5, 2))
        style.configure("AccentCard.TCheckbutton", background=CARD_ACCENT_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY, padding=(5, 2))
        style.configure("AccentCard.TLabel", background=CARD_ACCENT_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY)
        style.configure("AccentCard.TFrame", background=CARD_ACCENT_BG)
        style.configure("AccentCard.TCombobox", fieldbackground=CARD_ACCENT_BG, background=CARD_ACCENT_BG, foreground=TEXT_PRIMARY)
        style.map("AccentCard.TRadiobutton", foreground=[('active', PRIMARY_COLOR)])
        style.map("AccentCard.TCheckbutton", foreground=[('active', PRIMARY_COLOR)])
          # Success card widgets (green background)
        style.configure("SuccessCard.TRadiobutton", background=CARD_SUCCESS_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY, padding=(5, 2))
        style.configure("SuccessCard.TCheckbutton", background=CARD_SUCCESS_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY, padding=(5, 2))
        style.configure("SuccessCard.TLabel", background=CARD_SUCCESS_BG, font=(DEFAULT_FONT[0], DEFAULT_FONT[1]), foreground=TEXT_PRIMARY)
        style.configure("SuccessCard.TFrame", background=CARD_SUCCESS_BG)
        style.configure("SuccessCard.TCombobox", fieldbackground=CARD_SUCCESS_BG, background=CARD_SUCCESS_BG, foreground=TEXT_PRIMARY)
        style.map("SuccessCard.TRadiobutton", foreground=[('active', PRIMARY_COLOR)])
        style.map("SuccessCard.TCheckbutton", foreground=[('active', PRIMARY_COLOR)])
        style.configure("Header.TLabel", font=HEADER_FONT, foreground=PRIMARY_COLOR, background=BACKGROUND_COLOR)
        style.configure("Title.TLabel", font=TITLE_FONT, foreground=PRIMARY_COLOR, background=BACKGROUND_COLOR)    
        
        # Scaled card styles (30% larger) for special large cards
        scaled_font_size = int(DEFAULT_FONT[1] * 1.3)  # 30% larger font
        scaled_font = (DEFAULT_FONT[0], scaled_font_size)
        
        style.configure("PrimaryCard.Large.TLabel", background=CARD_PRIMARY_BG, font=scaled_font, foreground=TEXT_PRIMARY)
        style.configure("PrimaryCard.Large.TCombobox", fieldbackground=CARD_PRIMARY_BG, background=CARD_PRIMARY_BG, foreground=TEXT_PRIMARY, font=scaled_font)
        style.configure("SecondaryCard.Large.TLabel", background=CARD_SECONDARY_BG, font=scaled_font, foreground=TEXT_PRIMARY)
        style.configure("SecondaryCard.Large.TCombobox", fieldbackground=CARD_SECONDARY_BG, background=CARD_SECONDARY_BG, foreground=TEXT_PRIMARY, font=scaled_font)
        style.configure("AccentCard.Large.TLabel", background=CARD_ACCENT_BG, font=scaled_font, foreground=TEXT_PRIMARY)
        style.configure("AccentCard.Large.TCombobox", fieldbackground=CARD_ACCENT_BG, background=CARD_ACCENT_BG, foreground=TEXT_PRIMARY, font=scaled_font)
        style.configure("SuccessCard.Large.TLabel", background=CARD_SUCCESS_BG, font=scaled_font, foreground=TEXT_PRIMARY)
        style.configure("SuccessCard.Large.TCombobox", fieldbackground=CARD_SUCCESS_BG, background=CARD_SUCCESS_BG, foreground=TEXT_PRIMARY, font=scaled_font)

    @staticmethod
    def _setup_button_styles(style: ttk.Style) -> None:
        """Configure modern button styles with contemporary design."""
        
        # Primary buttons - Vibrant blue for main actions (Generate)
        style.configure(
            "Primary.TButton", 
            font=DEFAULT_FONT, 
            background=PRIMARY_COLOR, 
            foreground=TEXT_ON_PRIMARY, 
            relief="flat", 
            borderwidth=0, 
            padding=(15, 8),  # More generous padding
            focuscolor="none"
        )
        style.map(
            "Primary.TButton", 
            background=[
                ("active", PRIMARY_HOVER), 
                ("pressed", PRIMARY_HOVER),
                ("disabled", "#BFDBFE")  # Blue 200 for disabled
            ],
            relief=[("active", "flat"), ("pressed", "flat")]
        )
        
        # Secondary buttons - Neutral gray for Back/Close actions
        style.configure(
            "Secondary.TButton", 
            font=DEFAULT_FONT, 
            background=SECONDARY_COLOR, 
            foreground=SECONDARY_TEXT, 
            relief="flat", 
            borderwidth=1,
            padding=(15, 8),
            focuscolor="none"
        )
        style.map(
            "Secondary.TButton", 
            background=[
                ("active", SECONDARY_HOVER), 
                ("pressed", SECONDARY_HOVER),
                ("disabled", "#F3F4F6")  # Gray 100 for disabled
            ],
            foreground=[
                ("disabled", "#9CA3AF")  # Gray 400 for disabled text
            ],
            relief=[("active", "flat"), ("pressed", "flat")]
        )
        
        # Download/IPSSR buttons - Accent color for special actions
        style.configure(
            "Download.TButton", 
            font=(DEFAULT_FONT[0], BUTTON_FONT_SIZE), 
            background=ACCENT_COLOR, 
            foreground=TEXT_ON_PRIMARY, 
            relief="flat", 
            borderwidth=0,
            padding=(12, 6),
            focuscolor="none"
        )
        style.map(
            "Download.TButton", 
            background=[
                ("active", "#6D28D9"),  # Violet 700 for hover
                ("pressed", "#6D28D9")
            ],
            relief=[("active", "flat"), ("pressed", "flat")]
        )
        
        # Small utility buttons
        style.configure(
            "Small.TButton", 
            font=(DEFAULT_FONT[0], BASE_FONT_SIZE), 
            padding=(8, 4),
            relief="flat",
            borderwidth=1
        )

    @staticmethod
    def create_header(parent: tk.Widget, text: str, anchor: Literal['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw', 'center'] = "w") -> ttk.Label:
        """Create a styled header with separator."""
        frame = ttk.Frame(parent)
        frame.pack(fill="x", padx=5, pady=5, ipadx=5, ipady=5)
        header = ttk.Label(frame, text=text, style="Header.TLabel")
        header.pack(anchor=anchor, pady=(3, 2))
        ttk.Separator(frame, orient='horizontal', style="Horizontal.TSeparator").pack(fill='x', pady=(0, 5))
        return header
    
    @staticmethod
    def style_app_window(window: tk.Tk) -> None:
        """Apply background and icon to the main window."""
        window.configure(background=BACKGROUND_COLOR)
        icon_path = os.path.join(os.path.dirname(__file__), "../../assets/icons/app_icon.png")
        if os.path.exists(icon_path):
            icon = tk.PhotoImage(file=icon_path)
            window.iconphoto(True, icon)
    
    @staticmethod
    def create_action_buttons(
        parent: tk.Widget,
        execute_func: Callable,
        next_text: str,
        back_text: str = "",
        back_func: Optional[Callable] = None
    ) -> tk.Frame:        
        """Create a row of action buttons with Next at bottom left, Close at bottom right."""
        button_frame_container = tk.Frame(parent, bg=BACKGROUND_COLOR)
        button_frame_container.grid(sticky="ew", pady=3, column=0, row=0)
        
        # Ensure the parent allows the container to expand
        parent.columnconfigure(0, weight=1)
        
        button_frame_container.columnconfigure(0, weight=1)  # Left side for Next button
        button_frame_container.columnconfigure(1, weight=1)  # Right side for Close button
        
        button_width = 10
        button_padding = 3
        
        # Left side: Next button (and Back button if provided)
        left_buttons_frame = tk.Frame(button_frame_container, bg=BACKGROUND_COLOR)
        left_buttons_frame.grid(row=0, column=0, sticky="w")
        
        current_column = 0
        if back_text and back_func:
            back_button = ttk.Button(
                left_buttons_frame,
                text=back_text,
                command=back_func,
                style="Secondary.TButton",
                width=button_width
            )
            back_button.grid(row=0, column=current_column, padx=button_padding, sticky="w")
            current_column += 1
        
        next_button = ttk.Button(
            left_buttons_frame,
            text=next_text,
            command=execute_func,
            style="Primary.TButton",
            width=button_width
        )
        next_button.grid(row=0, column=current_column, padx=button_padding, sticky="w")
        
        # Right side: Close button
        close_button = ttk.Button(
            button_frame_container,
            text="Close",
            command=parent.winfo_toplevel().quit,
            style="Secondary.TButton",
            width=button_width
        )
        close_button.grid(row=0, column=1, padx=button_padding, sticky="e")
        
        return button_frame_container
