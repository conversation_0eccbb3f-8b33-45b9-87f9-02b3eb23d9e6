import tkinter as tk
from tkinter import ttk
from src.utils.import_utils import basics
from src import functions
from ..frames import BaseFrame
from ..custom_widgets import PrimaryCard, SecondaryCard
from src.logger_wrapper import logger
from ..theme_manager import ThemeManager


class SCA_ENC_GUI(BaseFrame):
    """Frame for FCI Scan Encoder configuration"""
    def __init__(self, parent, act_params, *args, **kwargs):
        self.act_params = act_params
        self.var_Side = tk.StringVar()
        self.input_scan_corr = None
        super().__init__(parent, *args, **kwargs)


    def create_widgets(self):
        """Create scan encoder GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Remove bottom padding: padding=5 -> padding=(5, 5, 5, 0)
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew") # Use grid

        # Configure main layout rows/columns
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)


    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"FCI Scan Encoder Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew")

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5)


    def _create_body(self, parent):
        """Creates the body section with input cards, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)

        # Configure grid to center content
        body_frame.columnconfigure(0, weight=1) # Left spacer
        body_frame.columnconfigure(1, weight=0) # Content column
        body_frame.columnconfigure(2, weight=1) # Right spacer
        body_frame.rowconfigure(0, weight=1) # Top spacer
        body_frame.rowconfigure(1, weight=0) # SCAE Card
        body_frame.rowconfigure(2, weight=0) # LUT Card
        body_frame.rowconfigure(3, weight=1) # Bottom spacer

        # SCAE ID Selection Card - Removed fixed sizes
        scae_card = PrimaryCard(body_frame, title="SCAE Configuration", padding=5)
        scae_card.grid(row=1, column=1, pady=10, sticky="ew") # Center column        
        scae_content = scae_card.get_content_frame()
        frame_scae = ttk.Frame(scae_content, style="PrimaryCard.TFrame")
        frame_scae.pack(anchor="center", pady=5, padx=5) # Pack inside card
        ttk.Label(frame_scae, text="SCAE ID:", style="PrimaryCard.TLabel").pack(side=tk.LEFT, padx=5)
        scae_combo = ttk.Combobox(frame_scae, textvariable=self.var_Side, values=["1", "2"], width=5, state="readonly")
        scae_combo.pack(side=tk.LEFT, padx=5)
        scae_combo.current(0) # Default to "1"

        # Scan Encoder LUT ID Card - Removed fixed sizes
        scan_id_card = SecondaryCard(body_frame, title="LUT Configuration", padding=5)
        scan_id_card.grid(row=2, column=1, pady=10, sticky="ew") # Center column        
        scan_id_content = scan_id_card.get_content_frame()
        frame_scan_id = ttk.Frame(scan_id_content, style="SecondaryCard.TFrame")
        frame_scan_id.pack(anchor="center", pady=5, padx=5) # Pack inside card
        ttk.Label(frame_scan_id, text="LUT Index:", style="SecondaryCard.TLabel").pack(side=tk.LEFT, padx=5)
        self.input_scan_corr = ttk.Entry(frame_scan_id, width=10)
        self.input_scan_corr.pack(side=tk.LEFT, padx=5)
        self.input_scan_corr.delete(0, tk.END)  # Clear any existing content first
        self.input_scan_corr.insert(0, "0")  # Set default value to 0


    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=2)
        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5) 
        controls_frame.columnconfigure(0, weight=1)  # Ensure the frame expands properly

        # Use ThemeManager instead of GUIBuilder
        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )


    def execute(self):
        """Handle execution of scan encoder configuration with validation."""
        if self.input_scan_corr is None:
             logger.error("Execute called before widgets created in SCA_ENC_GUI")
             basics.pop_up_message("Error", "GUI not fully initialized.", "error")
             return

        lut_id_val = self.input_scan_corr.get()
        side_val = self.var_Side.get()

        # Validation
        if not side_val: # Should not happen with readonly combobox with default
            basics.pop_up_message("Error", "SCAE ID not selected.", "error")
            return

        if not lut_id_val:
            basics.pop_up_message("Error", "Scan Encoder Correction LUT ID cannot be empty.", "error")
            return
        lut_id_int = int(lut_id_val) # Check if it's an integer. This will raise ValueError if not.

        # Update parameters
        self.act_params.scan_encode_correction_lut_id = lut_id_int
        self.act_params.side = side_val

        functions.generate_outputs(act_params=self.act_params)

    def back(self):
        """Handle back navigation"""
        self.app.back()
