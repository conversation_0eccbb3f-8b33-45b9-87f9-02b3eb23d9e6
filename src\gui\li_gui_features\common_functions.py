"""Common functions used in multiple LI GUI screens."""
import tkinter as tk
from tkinter import ttk
from typing import List, Dict

def enable_extra_options(
    lst_variables_fee: List[tk.StringVar], 
    merge_radios: Dict[str, ttk.Radiobutton], 
    var_mem: tk.StringVar
):
    """Enable or disable the memory merge option based on FEE selection and memory type."""
    # Count selected FEEs (excluding the "ALL FEEs" meta-option if present)
    # Check if "ALL FEEs" is selected
    all_fees_selected = lst_variables_fee[0].get() == "ALL FEEs" if lst_variables_fee else False
    
    # Count individual FEE selections
    selected_individual_fees = [var.get() for var in lst_variables_fee[1:] if var.get()]
    
    # Determine if multiple FEEs are effectively selected
    multiple_fees_selected = all_fees_selected or len(selected_individual_fees) > 1
    
    # Enable merge options only if multiple FEEs are selected and memory type includes RAM
    # (Handles "RAM" and "RAM and Flash")
    enable_merge = multiple_fees_selected and "RAM" in var_mem.get()
    
    merge_state = tk.NORMAL if enable_merge else tk.DISABLED
    tooltip_suffix = "(enabled if multiple FEEs and RAM selected)" if not enable_merge else ""

    # Directly configure the state of the passed radio buttons
    for option, radio in merge_radios.items():
        if radio: # Check if widget exists
            radio.config(state=merge_state)
