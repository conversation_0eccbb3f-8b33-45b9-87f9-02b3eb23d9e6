"""
Excel Utilities
=============

This module provides utility functions for reading Excel files.
"""


import pandas as pd
import numpy as np
from src.utils.import_utils import logger
from src.utils.data_cache import cache
import io

def Read_Sheet(file_path: str, sheet_name: str) -> pd.DataFrame:
    """
    Read a specific sheet from an Excel file.
    
    Args:
        file_path (str): Path to the Excel file
        sheet_name (str): Name of the sheet to read
        
    Returns:
        pd.DataFrame: DataFrame containing the sheet data
    """
    try:
        # Retrieve file bytes from the in-memory cache
        file_bytes = cache.get(file_path)
        if file_bytes is None:
            raise FileNotFoundError(f"File not found in cache: {file_path}")
        # Read the excel tab into a dataframe from bytes
        with io.BytesIO(file_bytes) as excel_buffer:
            df_content = pd.read_excel(
                excel_buffer,
                sheet_name=sheet_name,
                engine="openpyxl",
            )

        # Change NaN for empty strings
        df_content = df_content.replace(np.nan, "", regex=True)

        return df_content

    except Exception as e:
        logger.error(f"Error reading Excel sheet: {e}")
        raise