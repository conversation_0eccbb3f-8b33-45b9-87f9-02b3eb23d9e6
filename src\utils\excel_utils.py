"""
Excel Utilities
=============

This module provides utility functions for reading Excel files.
"""

import pandas as pd
import numpy as np
from src.utils.import_utils import logger

def Read_Sheet(file_path: str, sheet_name: str) -> pd.DataFrame:
    """
    Read a specific sheet from an Excel file.
    
    Args:
        file_path (str): Path to the Excel file
        sheet_name (str): Name of the sheet to read
        
    Returns:
        pd.DataFrame: DataFrame containing the sheet data
    """
    try:
        # Read the excel tab into a dataframe
        df_content = pd.read_excel(
            file_path,
            sheet_name=sheet_name,
            engine="openpyxl",
        )

        # Change NaN for empty strings
        df_content = df_content.replace(np.nan, "", regex=True)

        return df_content

    except Exception as e:
        logger.error(f"Error reading Excel sheet: {e}")
        raise