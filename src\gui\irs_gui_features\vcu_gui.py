
from typing import List
import tkinter as tk
from tkinter import ttk
from src import functions
from src.utils.import_utils import basics, config
from ...logger_wrapper import logger
from ..frames import BaseFrame
from ..theme_manager import ThemeManager
from ..custom_widgets import PrimaryCard, SecondaryCard, AccentCard, SuccessCard
from src.utils.activity_params import ActivityParams

class VCU_GUI(BaseFrame):
    """Frame for IRS VCU configuration"""

    def __init__(self, parent, act_params: ActivityParams, *args, **kwargs):
        self.act_params = act_params
        self.lst_variables_activity = []
        self.lst_variables_band = [] # Individual band variables (excluding 'All')
        self.lst_variables_video_group = [] # Individual group variables (excluding 'All')
        self.var_all_bands = tk.StringVar() # Variable for 'All' bands checkbox
        self.var_all_video_groups = tk.StringVar() # Variable for 'All' video groups checkbox
        self.var_Side = tk.StringVar()
        self.var_VCUIF = tk.StringVar()
        self.icid_entry = None
        self.icid_ver_entry = None
        super().__init__(parent, *args, **kwargs)

    def create_widgets(self):
        """Create VCU GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Main frame using grid
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew") # Use grid

        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)

    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"IRS VCU Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew")
        
        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5)
        
    def _create_body(self, parent):
        """Creates the body section with two columns for configuration options."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)
        
        body_frame.columnconfigure(0, weight=1, minsize=300) # Left column
        body_frame.columnconfigure(1, weight=1, minsize=300) # Right column
        body_frame.rowconfigure(0, weight=1)

        left_column = ttk.Frame(body_frame)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        left_column.rowconfigure(0, weight=0)
        left_column.rowconfigure(1, weight=0)
        left_column.rowconfigure(2, weight=0)
        left_column.rowconfigure(3, weight=0)
        left_column.rowconfigure(4, weight=1)
        left_column.columnconfigure(0, weight=1)

        right_column = ttk.Frame(body_frame)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        right_column.rowconfigure(0, weight=0) # Band selection row
        right_column.rowconfigure(1, weight=0) # Video group row
        right_column.rowconfigure(2, weight=1) # Spacer row
        right_column.columnconfigure(0, weight=1)

        self._create_side_selection(left_column)
        self._create_image_type_selection(left_column)
        self._create_activity_selection(left_column)
        self._create_icid_config(left_column)
        self._create_new_band_selection(right_column) # Call new band selection method
        self._create_video_group_selection(right_column) # Call video group selection method

    def _create_side_selection(self, parent):
        """Creates the VCU-I Memory Side selection card."""
        side_card = PrimaryCard(parent, title="VCU-I Memory Side", padding=5)
        side_card.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        side_content = side_card.get_content_frame()
        
        for side in ["Nominal", "Redundant"]:
            side_radio = ttk.Radiobutton(side_content, text=side, variable=self.var_Side, value=side, style="PrimaryCard.TRadiobutton")
            side_radio.pack(anchor="w", pady=2, padx=5)

        self.var_Side.set("Nominal")

    def _create_image_type_selection(self, parent):
        """Creates the Memory Image Type selection card."""
        image_type_card = SecondaryCard(parent, title="Memory Image Type", padding=5)
        image_type_card.grid(row=1, column=0, sticky="ew", pady=5)        
        image_type_content = image_type_card.get_content_frame()
        for fmt in ["Image per Band", "Combined Image"]:
            fmt_radio = ttk.Radiobutton(image_type_content, text=fmt, variable=self.var_VCUIF, value=fmt, style="SecondaryCard.TRadiobutton")
            fmt_radio.pack(anchor="w", pady=2, padx=5)
        self.var_VCUIF.set("Image per Band")

    def _create_activity_selection(self, parent):
        """Creates the Activity Selection card."""
        activity_card = AccentCard(parent, title="Activity Selection", padding=5)
        activity_card.grid(row=2, column=0, sticky="ew", pady=5)        
        activity_content = activity_card.get_content_frame()
        for activity in ["Instconf", "Numerical Offset", "Pixel Mapping"]:
            var = tk.StringVar()
            activity_cb = ttk.Checkbutton(activity_content, text=activity, variable=var, style="AccentCard.TCheckbutton")            
            activity_cb.pack(anchor="w", pady=2, padx=5)
            self.lst_variables_activity.append(var)

    def _create_icid_config(self, parent):
        """Creates the ICID Configuration card."""
        icid_card = SuccessCard(parent, title="ICID Configuration", padding=5)
        icid_card.grid(row=3, column=0, sticky="ew", pady=5)
        icid_content = icid_card.get_content_frame()
        icid_container = ttk.Frame(icid_content, style="SuccessCard.TFrame")
        icid_container.pack(fill=tk.X, expand=True, padx=5, pady=5)
        
        ttk.Label(icid_container, text="ICID:", style="SuccessCard.TLabel").pack(side=tk.LEFT, padx=(0, 5))
        self.icid_entry = ttk.Entry(icid_container, width=8)
        self.icid_entry.pack(side=tk.LEFT, padx=(0, 10))
        self.icid_entry.insert(0, "1")
        
        ttk.Label(icid_container, text="Ver:", style="SuccessCard.TLabel").pack(side=tk.LEFT, padx=(0, 5))
        self.icid_ver_entry = ttk.Entry(icid_container, width=8)
        self.icid_ver_entry.pack(side=tk.LEFT, padx=0)
        self.icid_ver_entry.insert(0, "1")

    def _create_new_band_selection(self, parent):
        """Creates the new Band Selection card with two internal columns and 'All' logic."""
        band_card = PrimaryCard(parent, title="Band Selection", padding=5)
        band_card.grid(row=0, column=0, sticky="nsew")
        
        band_content = band_card.get_content_frame()
        band_content.columnconfigure(0, weight=1)
        band_content.columnconfigure(1, weight=1)
        
        band_names = ["All", "LWIR", "MWIR"] # Updated band names
        middle_index = (len(band_names) + 1) // 2

        # Clear the list before adding new variables
        self.lst_variables_band.clear()
        self.var_all_bands = tk.StringVar() # Re-initialize in case method is called again

        for i, band in enumerate(band_names):
            is_all_checkbox = (band == "All")
            var = self.var_all_bands if is_all_checkbox else tk.StringVar()
            cmd = self._toggle_all_bands if is_all_checkbox else self._update_all_band_state

            col = 0 if i < middle_index else 1
            row = i if i < middle_index else i - middle_index

            band_cb = ttk.Checkbutton(band_content, text=band, variable=var, command=cmd, style="PrimaryCard.TCheckbutton")
            band_cb.grid(row=row, column=col, sticky="w", pady=1, padx=5)

            if not is_all_checkbox:
                self.lst_variables_band.append(var) # Add only individual band vars

    def _toggle_all_bands(self):
        """Checks or unchecks all individual band checkboxes based on the 'All' state."""
        is_checked = self.var_all_bands.get() == '1'
        for var in self.lst_variables_band:
            var.set('1' if is_checked else '0')

    def _update_all_band_state(self):
        """Updates the 'All' band checkbox based on the state of individual checkboxes."""
        all_checked = all(var.get() == '1' for var in self.lst_variables_band)
        any_unchecked = any(var.get() == '0' for var in self.lst_variables_band)

        if all_checked:
            self.var_all_bands.set('1')
        elif any_unchecked:
            self.var_all_bands.set('0')
        # If some are checked and some are not, 'All' remains unchecked (or becomes unchecked)

    def _create_video_group_selection(self, parent):
        """Creates the Video Group Selection card with two columns and an 'All' option/logic."""
        video_group_card = SecondaryCard(parent, title="Video Group Selection", padding=5)
        video_group_card.grid(row=1, column=0, sticky="ew", pady=5)

        video_group_content = video_group_card.get_content_frame()
        video_group_content.columnconfigure(0, weight=1)
        video_group_content.columnconfigure(1, weight=1)

        video_group_names = ["All", "video_1", "video_2", "video_3", "video_4", "video_5"] # Example video group names
        num_groups = len(video_group_names)
        items_per_col = (num_groups + 1) // 2  # Calculate items per column

        # Clear the list before adding new variables
        self.lst_variables_video_group.clear()
        self.var_all_video_groups = tk.StringVar() # Re-initialize

        for i, group in enumerate(video_group_names):
            is_all_checkbox = (group == "All")
            var = self.var_all_video_groups if is_all_checkbox else tk.StringVar()
            cmd = self._toggle_all_video_groups if is_all_checkbox else self._update_all_video_group_state

            col = 0 if i < items_per_col else 1
            row = i if i < items_per_col else i - items_per_col

            group_cb = ttk.Checkbutton(video_group_content, text=group, variable=var, command=cmd, style="SecondaryCard.TCheckbutton")
            group_cb.grid(row=row, column=col, sticky="w", pady=1, padx=5)

            if not is_all_checkbox:
                self.lst_variables_video_group.append(var) # Add only individual group vars

    def _toggle_all_video_groups(self):
        """Checks or unchecks all individual video group checkboxes based on the 'All' state."""
        is_checked = self.var_all_video_groups.get() == '1'
        for var in self.lst_variables_video_group:
            var.set('1' if is_checked else '0')

    def _update_all_video_group_state(self):
        """Updates the 'All' video group checkbox based on the state of individual checkboxes."""
        all_checked = all(var.get() == '1' for var in self.lst_variables_video_group)
        any_unchecked = any(var.get() == '0' for var in self.lst_variables_video_group)

        if all_checked:
            self.var_all_video_groups.set('1')
        elif any_unchecked:
            self.var_all_video_groups.set('0')

    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=1)
        
        controls_frame = ttk.Frame(bottom_frame)
        # Remove pady=5
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5)

        # Use ThemeManager instead of GUIBuilder
        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )

    def _get_selected_check_button(self, variables: List[tk.StringVar]) -> List[str]:
        """Helper to get selected values from a list of checkbutton variables.
           Assumes the list does not contain the 'All' variable."""
        # Filter based on the variable's value ('1' means checked) and the text associated with it
        selected_texts = []
        for var in variables:
            if var.get() == '1':
                # Find the widget associated with the variable to get its text
                # This relies on the variable being uniquely associated with one widget.
                # This is a bit fragile; consider storing (var, text) tuples if issues arise.
                for widget in self.winfo_children(): # Search recursively if needed
                    if isinstance(widget, ttk.Checkbutton) and widget.cget('variable') == str(var):
                         selected_texts.append(widget.cget('text'))
                         break # Found the widget for this var
        # Original implementation just returned the variable objects if checked:
        # return [var for var in variables if var.get() == '1']
        # Returning the text seems more useful based on how it's used later.
        # If the variable object itself is needed elsewhere, adjust accordingly.
        return selected_texts # Return the text of selected items

    def execute(self):
        """Validate inputs and handle execution of VCU configuration generation."""
        if self.icid_entry is None or self.icid_ver_entry is None:
             logger.error("Execute called before widgets created in IRS VCU_GUI")
             basics.pop_up_message("Error", "GUI not fully initialized.", "error")
             return

        self.update_idletasks()

        str_VCU_image_format = self.var_VCUIF.get()
        self.act_params.side = self.var_Side.get()
        
        icid_val = self.icid_entry.get()
        icid_ver_val = self.icid_ver_entry.get()

        # Get selected items, excluding the 'All' variable implicitly
        # as the lists passed only contain individual item variables.
        lst_part = self._get_selected_check_button(self.lst_variables_activity)
        lst_band_selected = self._get_selected_check_button(self.lst_variables_band)
        lst_video_group_selected = self._get_selected_check_button(self.lst_variables_video_group)

        # --- Input Validation ---
        if not icid_val:
            raise ValueError("ICID cannot be empty.")
        int_slicer = int(icid_val)

        if not icid_ver_val:
            raise ValueError("ICID Version cannot be empty.")
        int_slicer_2 = int(icid_ver_val)

        # Validation should check if *any* individual item is selected,
        # not rely on the 'All' checkbox state directly.
        if not lst_band_selected:
            # Updated error message to reflect actual options
            raise ValueError("At least one Band (LWIR/MWIR) must be selected.")
        if not lst_video_group_selected:
            raise ValueError("At least one Video Group must be selected.")

        # --- Execute Generation ---
        self.update_idletasks()
        self.act_params.icid = int_slicer
        self.act_params.icid_ver = int_slicer_2
        
        # Pass the list of selected band/group *texts*
        functions.generate_outputs(
            act_params=self.act_params,
            lst_band=lst_band_selected, # Pass the list of selected band texts
            lst_part=lst_part, # Assuming this expects texts too
            str_VCU_image_format=str_VCU_image_format
            # lst_video_group=lst_video_group_selected # Pass video groups if needed by generate_outputs
        )

        basics.pop_up_message("Success", "IRS VCU configuration generated successfully.", "info")


    def back(self):
        """Handle back navigation"""
        self.app.back()
