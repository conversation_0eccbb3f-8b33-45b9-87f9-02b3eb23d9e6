"""
Standalone XLDT File Reader
===========================

This is a standalone version of the XLDT reader that doesn't depend on 
the complex import structure of the main codebase.

XLDT files are binary files with the following structure:
- XLDT Header: Format identifier and MM slot information
- CRC: Checksum for data validation
- Body: Various data sections (Header, LAC_Pointer, Retrace, Rally, FDA, MPA, etc.)
"""

import os
import struct
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
from dataclasses import dataclass
import logging

# Set up basic logging
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)


def hex2dec(hex_str: str) -> int:
    """Convert hex string to decimal integer."""
    return int(hex_str, 16)


def calculate_crc16(data: str) -> str:
    """
    Calculate CRC16 checksum for hex string data.
    This is a simplified implementation.
    """
    total = sum(int(data[i:i+2], 16) for i in range(0, len(data), 2))
    return f"{total & 0xFFFF:04X}"


@dataclass
class FieldDefinition:
    """Definition of a field within an XLDT section"""
    name: str
    data_type: str
    size_bytes: int
    description: str = ""
    long_name: str = ""
    scaling_factor: float = 1.0
    is_filler_check: bool = True


@dataclass
class StructuredParameter:
    """Structured parameter with metadata like CDC system"""
    name: str
    value: Any
    data_type: str
    long_name: str = ""
    dimensions: str = ""
    shape: str = ""
    description: str = ""


@dataclass
class XLDTHeader:
    """XLDT Header structure"""
    format_id: int
    mm_slot: int
    body_length: Optional[int] = None


@dataclass
class XLDTSection:
    """XLDT Section structure"""
    section_name: str
    msdf_id: Optional[int]
    length: int
    order: int
    data: bytes


# Field mapping configuration based on VSM analysis
XLDT_FIELD_MAPPINGS = {
    "FDA": [
        FieldDefinition(
            name="dwell_position_alpha",
            data_type="float32",
            size_bytes=4,
            description="Scan alpha angle in the full disc array",
            long_name="Dwell Position Alpha",
            scaling_factor=1000.0,  # Values are scaled x1000 in binary
            is_filler_check=True
        ),
        FieldDefinition(
            name="dwell_position_epsilon",
            data_type="float32",
            size_bytes=4,
            description="Scan epsilon angle in the full disc array",
            long_name="Dwell Position Epsilon",
            scaling_factor=1000.0,
            is_filler_check=True
        ),
        FieldDefinition(
            name="fda_mp_pointer_alpha",
            data_type="ubyte",
            size_bytes=1,
            description="Alpha motion profile array pointer",
            long_name="FDA Motion Profile Pointer Alpha",
            is_filler_check=True
        ),
        FieldDefinition(
            name="fda_mp_pointer_epsilon",
            data_type="ubyte",
            size_bytes=1,
            description="Epsilon motion profile array pointer",
            long_name="FDA Motion Profile Pointer Epsilon",
            is_filler_check=True
        )
    ],
    "LAC_Pointer": [
        FieldDefinition(
            name="lac_start",
            data_type="ushort",
            size_bytes=2,
            description="LAC start index",
            long_name="LAC Start"
        ),
        FieldDefinition(
            name="lac_end",
            data_type="ushort",
            size_bytes=2,
            description="LAC end index",
            long_name="LAC End"
        ),
        FieldDefinition(
            name="lac_start_delta_time_alpha",
            data_type="float32",
            size_bytes=4,
            description="LAC start delta time alpha",
            long_name="LAC Start Delta Time Alpha",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="lac_start_delta_time_epsilon",
            data_type="float32",
            size_bytes=4,
            description="LAC start delta time epsilon",
            long_name="LAC Start Delta Time Epsilon",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="lac_end_delta_time_alpha",
            data_type="float32",
            size_bytes=4,
            description="LAC end delta time alpha",
            long_name="LAC End Delta Time Alpha",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="lac_end_delta_time_epsilon",
            data_type="float32",
            size_bytes=4,
            description="LAC end delta time epsilon",
            long_name="LAC End Delta Time Epsilon",
            scaling_factor=1000.0
        )
    ],
    "MPA": [
        FieldDefinition(
            name="mpa_profile_type",
            data_type="profile_type",
            size_bytes=4,
            description="Motion profile type identifier",
            long_name="MPA Profile Type"
        ),
        FieldDefinition(
            name="mpa_profile_param1",
            data_type="float32",
            size_bytes=4,
            description="Motion profile parameter 1",
            long_name="MPA Profile Parameter 1",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="mpa_profile_param2",
            data_type="float32",
            size_bytes=4,
            description="Motion profile parameter 2",
            long_name="MPA Profile Parameter 2",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="mpa_profile_param3",
            data_type="float32",
            size_bytes=4,
            description="Motion profile parameter 3",
            long_name="MPA Profile Parameter 3",
            scaling_factor=1000.0
        ),
        FieldDefinition(
            name="mpa_profile_param4",
            data_type="float32",
            size_bytes=4,
            description="Motion profile parameter 4",
            long_name="MPA Profile Parameter 4",
            scaling_factor=1000.0
        )
    ]
}


def is_filler_value(value: Any, data_type: str) -> bool:
    """Check if a value is a filler value (typically very large numbers)."""
    if data_type in ["float32", "float"]:
        return abs(float(value)) > 1e30
    elif data_type in ["ubyte", "ushort", "uint32"]:
        return int(value) == 0xFFFFFFFF or int(value) == 0xFFFF or int(value) == 0xFF
    return False


def parse_binary_field(hex_data: str, field_def: FieldDefinition, offset: int) -> Tuple[Any, int]:
    """
    Parse a single field from binary hex data.
    
    Args:
        hex_data: Hex string data
        field_def: Field definition
        offset: Current offset in hex string (in characters, not bytes)
    
    Returns:
        Tuple of (parsed_value, new_offset)
    """
    # Extract the hex bytes for this field
    field_hex = hex_data[offset:offset + field_def.size_bytes * 2]
    
    if len(field_hex) < field_def.size_bytes * 2:
        raise ValueError(f"Not enough data for field {field_def.name}")
    
    # Convert hex to bytes
    field_bytes = bytes.fromhex(field_hex)
    
    # Parse based on data type
    if field_def.data_type == "float32":
        # IEEE 754 float32, big-endian
        value = struct.unpack('>f', field_bytes)[0]
        # Apply scaling factor if specified
        if field_def.scaling_factor != 1.0:
            value = value / field_def.scaling_factor
    elif field_def.data_type == "ushort":
        # 2-byte unsigned short, big-endian
        value = struct.unpack('>H', field_bytes)[0]
    elif field_def.data_type == "ubyte":
        # 1-byte unsigned byte
        value = struct.unpack('B', field_bytes)[0]
    elif field_def.data_type == "profile_type":
        # Profile type - treat as 4-byte unsigned int
        value = struct.unpack('>I', field_bytes)[0]
    else:
        # Default: treat as raw hex
        value = field_hex
    
    new_offset = offset + field_def.size_bytes * 2
    return value, new_offset


def parse_section_fields(hex_data: str, section_name: str) -> List[StructuredParameter]:
    """
    Parse a section's hex data into structured parameters.

    Args:
        hex_data: Hex string data for the section
        section_name: Name of the section (FDA, LAC_Pointer, MPA)

    Returns:
        List of StructuredParameter objects
    """
    if section_name not in XLDT_FIELD_MAPPINGS:
        logger.warning(f"No field mapping found for section: {section_name}")
        return []

    field_defs = XLDT_FIELD_MAPPINGS[section_name]
    parameters = []

    # Calculate entry size (sum of all field sizes)
    entry_size_bytes = sum(field.size_bytes for field in field_defs)
    entry_size_hex = entry_size_bytes * 2

    # Calculate number of entries
    total_hex_length = len(hex_data)
    num_entries = total_hex_length // entry_size_hex

    logger.info(f"Parsing {section_name}: {num_entries} entries, {entry_size_bytes} bytes per entry")

    # Parse each entry
    for entry_idx in range(num_entries):
        entry_offset = entry_idx * entry_size_hex
        field_offset = entry_offset

        # Check if this entry has any valid (non-filler) data
        entry_has_valid_data = False
        entry_fields = []

        # Parse all fields in this entry
        for field_def in field_defs:
            try:
                value, field_offset = parse_binary_field(hex_data, field_def, field_offset)

                # Check if this is a filler value
                is_filler = field_def.is_filler_check and is_filler_value(value, field_def.data_type)
                if not is_filler:
                    entry_has_valid_data = True

                # Create structured parameter
                param = StructuredParameter(
                    name=f"{field_def.name}[{entry_idx}]",
                    value=value,
                    data_type=field_def.data_type,
                    long_name=field_def.long_name,
                    dimensions=f"({section_name.lower()}_size)",
                    shape=f"({num_entries})",
                    description=field_def.description
                )
                entry_fields.append(param)

            except Exception as e:
                logger.error(f"Error parsing field {field_def.name} in entry {entry_idx}: {e}")
                continue

        # Only include entries with valid data
        if entry_has_valid_data:
            parameters.extend(entry_fields)
        else:
            # Stop at first all-filler entry (like the original code does)
            logger.info(f"Stopping at entry {entry_idx} - all filler values")
            break

    return parameters
