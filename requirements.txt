# Optimized requirements.txt for Python 3.8.6
# All packages pinned to the latest version compatible with Python 3.8.6 (as of June 2024)
# Last updated: June 2024

# ===== CRITICAL SECURITY UPDATES (Python 3.8 compatible) =====
cryptography==41.0.7
Pillow==9.5.0
urllib3==1.26.18
requests==2.31.0
lxml==4.9.3

# ===== WEB FRAMEWORK (Python 3.8 compatible) =====
Flask==2.2.5
Jinja2==3.1.3
MarkupSafe==2.1.3
itsdangerous==2.1.2
Werkzeug==2.2.3

# ===== SCIENTIFIC COMPUTING (Python 3.8) =====
numpy==1.24.4
pandas==1.5.3
matplotlib==3.7.5
scipy==1.10.1
cftime==1.6.3
netCDF4==1.6.5

# ===== DATA PROCESSING =====
openpyxl==3.1.2
xlrd==2.0.1
xlwings==0.31.4
python-docx==1.1.0
et-xmlfile==1.1.0

# ===== TESTING FRAMEWORK =====
pytest==7.4.4
pytest-html==3.2.0
pytest-metadata==3.0.0
pluggy==1.4.0
iniconfig==2.0.0
packaging==23.2
tomli==2.0.1
exceptiongroup==1.2.0

# ===== JUPYTER ECOSYSTEM (Python 3.8 compatible) =====
jupyter==1.0.0
jupyterlab==3.6.8
notebook==6.5.7
ipython==8.12.3
ipykernel==6.29.5
jupyter-client==7.4.9
jupyter-core==5.7.2
nbformat==5.10.4
nbconvert==7.16.4
jupyter-server==2.14.2

# ===== SECURITY & NETWORKING =====
paramiko==3.4.0
pysftp==0.2.9
bcrypt==4.1.3
PyNaCl==1.5.0

# ===== UTILITY LIBRARIES =====
certifi==2024.6.2
chardet==5.2.0
click==8.1.7
colorama==0.4.6
python-dateutil==2.9.0
pytz==2024.1
six==1.16.0
natsort==8.4.0

# ===== DEVELOPMENT TOOLS =====
GitPython==3.1.43
gitdb==4.0.11
smmap==5.0.1

# ===== ASYNC & CONCURRENCY =====
async-generator==1.10
nest-asyncio==1.6.0

# ===== PARSING & VALIDATION =====
attrs==23.2.0
bleach==6.1.0
defusedxml==0.7.1

# ===== WINDOWS-SPECIFIC =====
pywin32==300
pywinpty==2.0.10

# ===== MISC UTILITIES =====
decorator==5.1.1
crcmod==1.7
setuptools==68.2.2
wheel==0.43.0

# ===== OPTIONAL MODERN ADDITIONS (Python 3.8 compatible) =====
rich==13.7.1
typer==0.12.3
