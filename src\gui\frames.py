"""
Core GUI frames and utilities for the MTG CDC application.
This module combines the base frame functionality, common utilities,
instrument handling, and main application frames.
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Type, List, Optional, Literal
import tkinter.scrolledtext
import threading
import os

from src.utils.import_utils import basics, config
from src.utils.data_cache import cache
from src.logger_wrapper import logger
from .theme_manager import ThemeManager
from .custom_widgets import PrimaryCard, SecondaryCard, AccentCard
from src.utils.activity_params import ActivityParams
from src.utils.test_runner import run_fci_tests, run_li_tests, run_irs_tests, run_uvn_tests


class BaseFrame(ttk.Frame):
    """Base class for all application frames"""

    def __init__(self, parent, app=None, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.parent = parent
        self.app = app
        self.config = config
        self.logger = logger

        self.create_widgets()

    def create_widgets(self):
        """Create frame-specific widgets (must be implemented by subclasses)"""
        raise NotImplementedError("Subclasses should implement create_widgets().")

    def create_header(self, parent_frame, text, anchor: Literal['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw', 'center'] = "w"):
        """Create a header with a separator line."""
        return ThemeManager.create_header(parent_frame, text, anchor=anchor)

    def update_status(self, message=None):
        """Update status message (subclasses may override)."""
        pass


class AppWindow:
    """Main application window manager"""

    def __init__(self):
        self.window = tk.Tk()
        self.window.title("MTG CDC")

        self.window.iconbitmap(r'assets/icons/app_icon.ico')  # Use a .ico file

        ThemeManager.setup_theme()
        ThemeManager.style_app_window(self.window)

        # Center window on screen
        window_width = 900
        window_height = 650
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()

        x = (screen_width // 2) - (window_width // 2)
        y = (screen_height // 2) - (window_height // 2)

        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.window.resizable(True, True)  # Allow resizing
        self.current_frame = None
        self.frame_stack = []  # For back button functionality

        # self.status_var = ThemeManager.create_status_bar(self.window) # REMOVED

    def show_frame(self, frame_class, *args, **kwargs):
        """Switch to a new frame."""
        if self.current_frame:
            self.current_frame.destroy()
        self.current_frame = frame_class(self.window, app=self, *args, **kwargs)
        self.current_frame.pack(fill=tk.BOTH, expand=True)
        self.frame_stack.append((frame_class, args, kwargs))

    def back(self):
        """Go back to previous frame."""
        if len(self.frame_stack) > 1:
            if self.current_frame:
                self.current_frame.destroy()
            self.frame_stack.pop()
            if self.frame_stack:
                frame_class, args, kwargs = self.frame_stack[-1]
                self.current_frame = frame_class(self.window, app=self, *args, **kwargs)
                self.current_frame.pack(fill=tk.BOTH, expand=True)

    def run(self):
        """Start the main application loop."""
        self.window.mainloop()

    def close(self):
        """Close the window and return to the main function."""
        logger.info("GUI closed - returning to main function")
        self.window.quit()
        self.window.destroy()


class InstrumentHandler:
    """Handler for instrument-specific activities"""

    _activity_mappings: Dict[str, Dict[str, Type[BaseFrame]]] = {}

    @classmethod
    def register_activity(cls, instrument: str, activity: str, frame_class: Type[BaseFrame]) -> None:
        """Register an activity handler for a specific instrument."""
        if instrument not in cls._activity_mappings:
            cls._activity_mappings[instrument] = {}
        cls._activity_mappings[instrument][activity] = frame_class

    @classmethod
    def handle_activity(cls, app, act_params: ActivityParams) -> None:
        """Handle instrument-specific activity."""
        instrument = act_params.instrument
        activity = act_params.activity
        if not instrument or not activity:
            logger.error("Instrument or activity not specified in ActivityParams.")
            return
        handler = cls._activity_mappings.get(instrument, {}).get(activity)
        if handler:
            app.show_frame(handler, act_params)
        else:
            logger.error(f"No handler registered for instrument '{instrument}' and activity '{activity}'")

    @classmethod
    def _get_activity_handler(cls, instrument: str, activity: str) -> Optional[Type[BaseFrame]]:
        """Get the handler for a specific activity."""
        if instrument in cls._activity_mappings:
            return cls._activity_mappings[instrument].get(activity)
        return None


class UnifiedFrame(BaseFrame):
    """Unified selection frame combining instrument and activity selection"""

    def __init__(self, parent, *args, app, **kwargs):
        self.act_params = ActivityParams()
        self.lst_variables_sat = [] # Stores individual satellite StringVars
        self.var_all_satellites = tk.StringVar() # Stores the state of the 'All' satellite checkbox
        self.activity_var = tk.StringVar()
        self.instrument_var = tk.StringVar() # Initialize here
        self.sat_frame = None
        self.act_frame = None
        # self.download_status_label: Optional[ttk.Label] = None # REMOVED for download status
        super().__init__(parent, *args, app=app, **kwargs)

    def create_widgets(self):
        """Lay out the main widgets for the unified frame."""
        main_container = ttk.Frame(self)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=(10, 0))

        # Configure main container rows
        main_container.rowconfigure(0, weight=0)  # Title - fixed size
        # Row 1 (previously for download buttons) is removed
        main_container.rowconfigure(1, weight=1)  # Body - expands (was row 2)
        main_container.rowconfigure(2, weight=0)  # Footer - fixed size (was row 3)
        main_container.columnconfigure(0, weight=1)
        self._create_title_section(main_container)
        self._create_body_layout(main_container)
        self._create_footer_buttons(main_container)
        if config.CONFIG_VALUES["instruments"]:
            self.instrument_var.set(config.CONFIG_VALUES["instruments"][0])
            self.instrument_changed()

    def _create_title_section(self, parent):
        """Create the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_label = ttk.Label(title_frame, text="MTG Instrument Command Data Converter", style="Title.TLabel")
        title_label.pack(anchor="center")
        ttk.Separator(title_frame, orient='horizontal').pack(fill='x', pady=2, expand=True)

    def _handle_instrument_download(self, instrument_name: str):
        """Download IPSSR data for the selected instrument."""
        import os
        from src.utils.ipssr_utils import download_ipssr_for_instrument
        # 'basics' is available from the global import: from src.utils.import_utils import basics, config
        # 'self.logger' is an instance variable, initialized in BaseFrame

        self.logger.info(f"Attempting to download IPSSR for {instrument_name}")

        download_popup = tk.Toplevel(self.app.window)
        download_popup.title("Downloading")
        download_popup.transient(self.app.window)

        parent_x = self.app.window.winfo_x()
        parent_y = self.app.window.winfo_y()
        parent_width = self.app.window.winfo_width()
        parent_height = self.app.window.winfo_height()
        popup_width = 450
        popup_height = 150

        popup_x = parent_x + (parent_width // 2) - (popup_width // 2)
        popup_y = parent_y + (parent_height // 2) - (popup_height // 2)

        download_popup.geometry(f"{popup_width}x{popup_height}+{popup_x}+{popup_y}")
        download_popup.resizable(False, False)

        ipssr_url = config.CONFIG_VALUES.get("ipssr_url", "N/A")
        status_message = f"Downloading {instrument_name} input files from IPSSR:\n{ipssr_url}"
        
        message_label = ttk.Label(download_popup, text=status_message, padding=(10, 10), wraplength=popup_width-40, justify=tk.CENTER)
        message_label.pack(expand=True, fill=tk.BOTH, pady=(10,10))

        download_popup.update_idletasks() # Process geometry and display tasks for the popup
        download_popup.grab_set() # Make it modal
        download_popup.lift() # Ensure it's on top

        def _perform_download_task():
            base_input_path = os.path.join("assets", "Input_Files")
            try:
                download_ipssr_for_instrument(instrument_name, base_input_path)
                msg = f"IPSSR data for {instrument_name} downloaded/updated successfully."
                self.logger.info(msg)
                if download_popup.winfo_exists():
                    download_popup.destroy() # Close download popup first
                basics.pop_up_message("Success", msg, "info")
            except Exception as e:
                error_msg = f"Failed to download IPSSR for {instrument_name}. Error: {e}"
                self.logger.error(error_msg)
                if download_popup.winfo_exists():
                    download_popup.destroy() # Close download popup first
                basics.pop_up_message("Error", error_msg, "error")
            finally:
                if download_popup.winfo_exists():
                    download_popup.destroy()
        
        # Schedule the download task to run after the GUI has had a chance to update
        download_popup.after(100, _perform_download_task)

    def _create_body_layout(self, parent):
        """Creates the main body layout with instrument, satellite, and activity sections."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5) # Updated row to 1

        # Configure the body frame's grid
        body_frame.columnconfigure(0, weight=1, minsize=350)  # Left column
        body_frame.columnconfigure(1, weight=1, minsize=350)  # Right column
        body_frame.rowconfigure(0, weight=1) # Allow rows within columns to expand if needed

        # Create left and right columns
        left_column = ttk.Frame(body_frame)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        left_column.rowconfigure(0, weight=1) # Instrument selection row
        left_column.rowconfigure(1, weight=1) # Satellite selection row
        left_column.columnconfigure(0, weight=1)

        right_column = ttk.Frame(body_frame)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        right_column.rowconfigure(0, weight=1) # Activity selection row
        right_column.columnconfigure(0, weight=1)

        self._create_instrument_selection(left_column)
        self._create_satellite_selection(left_column)
        self._create_activity_selection(right_column)

    def _create_instrument_selection(self, parent):
        """Creates the instrument selection card with radio buttons and download buttons."""
        instrument_card = PrimaryCard(
            parent,
            title="Instrument Selection",
            padding=5 # Adjusted padding
        )
        # Use grid for card placement within the left column
        instrument_card.grid(row=0, column=0, sticky="nsew", pady=(0, 5))

        instrument_frame = instrument_card.get_content_frame()
        self.instrument_var.trace_add("write", self.instrument_changed) # Use trace_add

        # Configure columns for radio button and download button
        instrument_frame.columnconfigure(0, weight=0)  # Radio button column (no extra space)
        instrument_frame.columnconfigure(1, weight=1)  # Download button column (takes remaining space)

        instruments = config.CONFIG_VALUES.get("instruments", [])
        if not instruments:
            no_instr_label = ttk.Label(instrument_frame, text="No instruments configured.")
            no_instr_label.grid(row=0, column=0, columnspan=2, sticky="w", pady=2, padx=5)
            return

        for i, instrument in enumerate(instruments):
            # Radio Button            
            radio = ttk.Radiobutton(
                instrument_frame,
                text=instrument,
                variable=self.instrument_var,
                value=instrument,
                style="PrimaryCard.TRadiobutton"
            )
            radio.grid(row=i, column=0, sticky="w", pady=2, padx=5)

            # Download Button for the instrument
            download_button = ttk.Button(
                instrument_frame,
                text=f"{instrument} IPSSR Update", # Dynamically set button text
                command=lambda inst=instrument: self._handle_instrument_download(inst),
                style="Download.TButton" # Apply the new style
            )
            download_button.grid(row=i, column=1, padx=5, pady=(0, 5), sticky="e")

    def _create_satellite_selection(self, parent):
        """Creates the satellite selection card."""
        satellite_card = SecondaryCard(
            parent,
            title="Satellite Selection",
            padding=5 # Adjusted padding
        )
        # Use grid for card placement
        satellite_card.grid(row=1, column=0, sticky="nsew")

        # Store the content frame for later population
        self.sat_frame = satellite_card.get_content_frame()
        # Frame resizes based on content

    def _create_activity_selection(self, parent):
        """Creates the activity selection card and test run buttons."""
        # Activity Selection Card
        activity_card = AccentCard(
            parent,
            title="Activity Selection",
            padding=5
        )
        activity_card.grid(row=0, column=0, sticky="nsew", pady=(0, 5)) # Add padding below

        # Store the content frame for later population by instrument_changed
        self.act_frame = activity_card.get_content_frame()

        # New frame for test buttons
        test_buttons_frame = ttk.Frame(parent) # parent is right_column
        test_buttons_frame.grid(row=1, column=0, sticky="ew", pady=(10, 0)) # Place below activity_card, add top padding

        # Configure columns for 2 buttons per row
        test_buttons_frame.columnconfigure(0, weight=1)
        test_buttons_frame.columnconfigure(1, weight=1)

        def execute_instrument_test_in_thread(test_function, instrument_name, button_to_disable):
            # Disable the button before starting the test
            button_to_disable.config(state=tk.DISABLED)
            
            result_window = tk.Toplevel(self.app.window)
            result_window.title(f"{instrument_name} Test Progress")
            result_window.geometry("700x500")
            result_window.transient(self.app.window)
            result_window.grab_set()

            # Use ScrolledText for automatic scrollbars
            text_area = tk.scrolledtext.ScrolledText(result_window, wrap=tk.WORD, font=("Courier New", 10))
            text_area.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)
            text_area.insert(tk.END, f"Starting {instrument_name} tests...\n")
            text_area.config(state=tk.DISABLED)

            def update_text_area(line):
                if result_window.winfo_exists(): # Check if window still exists
                    text_area.config(state=tk.NORMAL)
                    text_area.insert(tk.END, line)
                    text_area.see(tk.END) # Scroll to the end
                    text_area.config(state=tk.DISABLED)
                    result_window.update_idletasks() # Process UI updates

            def test_runner_thread():
                try:
                    success = test_function(output_callback=update_text_area)
                    if result_window.winfo_exists():
                        final_message = f"\n{instrument_name} tests {'completed successfully' if success else 'finished with errors'}.\n"
                        update_text_area(final_message)
                        if success:
                            self.logger.info(f"{instrument_name} tests passed (or no tests found).")
                        else:
                            self.logger.error(f"{instrument_name} tests failed. See details in popup.")
                except Exception as e:
                    if result_window.winfo_exists():
                        update_text_area(f"\nAn error occurred during test execution: {e}\n")
                    self.logger.exception(f"Error in test_runner_thread for {instrument_name}")
                finally:
                    if result_window.winfo_exists():
                        # Add a close button to the results window
                        close_button = ttk.Button(result_window, text="Close", command=result_window.destroy, style="Secondary.TButton") # Ensure style is applied
                        # Pack it at the bottom, after the text area
                        close_button.pack(pady=(5,10)) 
                        # Re-enable the original button
                        button_to_disable.config(state=tk.NORMAL)
                    # Ensure grab_release is called if the window was closed by other means
                    if result_window.winfo_exists():
                        pass # grab_release will be handled by destroy if user closes window
                    else:
                        # If window was destroyed before close button added, ensure grab is released
                        # This case might be rare if grab_set is effective
                        try:
                            self.app.window.grab_release()
                        except tk.TclError: # If grab was not set on app.window
                            pass 

            # Run the test in a separate thread to keep the GUI responsive
            thread = threading.Thread(target=test_runner_thread)
            thread.start()
            
            # Don't use self.app.window.wait_window(result_window) here as it blocks the main thread
            # The grab_set() makes the window modal.

        button_details = [] # To be populated with (text, command, instrument_name)
        # We need to create buttons first to pass them to the command
        buttons = {}

        def create_command(test_func, instr_name, button_key):
            # This lambda will be called when the button is pressed
            # It needs to know which button to disable, hence button_key
            return lambda: execute_instrument_test_in_thread(test_func, instr_name, buttons[button_key])

        button_configs = [
            ("Run FCI self-tests", run_fci_tests, "FCI"),
            ("Run LI self-tests", run_li_tests, "LI"),
            ("Run IRS self-tests", run_irs_tests, "IRS"),
            ("Run UVN self-tests", run_uvn_tests, "UVN")
        ]
        button_style = "Secondary.TButton"

        current_row = 0
        current_col = 0
        for text, test_func, instr_name in button_configs:
            button_key = f"{instr_name}_button"
            button = ttk.Button(
                test_buttons_frame,
                text=text,
                # Command will be set later, after all buttons are created
                style=button_style
            )
            button.grid(row=current_row, column=current_col, padx=3, pady=3, sticky="ew")
            buttons[button_key] = button # Store the button widget
            
            current_col += 1
            if current_col > 1:
                current_col = 0
                current_row += 1
        
        # Now assign commands, as they need a reference to the button itself
        for text, test_func, instr_name in button_configs:
            button_key = f"{instr_name}_button"
            buttons[button_key].config(command=create_command(test_func, instr_name, button_key))


    def _create_footer_buttons(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        # Use grid for the bottom frame placement
        bottom_frame.grid(row=2, column=0, sticky="ew", pady=(5, 0)) # Updated row to 2
        bottom_frame.columnconfigure(0, weight=1) # Allow button frame to expand horizontally

        separator = ttk.Separator(bottom_frame, orient='horizontal')
        # Use grid for separator
        separator.grid(row=0, column=0, sticky="ew", pady=2)        
        controls_frame = ttk.Frame(bottom_frame)
        # Remove pady=(5, 0)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5) 
        controls_frame.columnconfigure(0, weight=1)  # Ensure the frame expands properly

        # Use ThemeManager for standardized buttons
        self.action_buttons_frame = ThemeManager.create_action_buttons(
            parent=controls_frame, # Pass the frame where buttons should be packed/gridded
            execute_func=self.execute_activity,
            next_text="Next",
            # No back button needed on the main frame
        )

    def _clear_frame(self, frame):
        """Clear all widgets from a frame."""
        if frame is not None:
            for widget in frame.winfo_children():
                widget.destroy()

    def _create_satellite_checkboxes(self, satellites):
        """Create satellite selection checkboxes within self.sat_frame, including 'All' logic."""
        self.lst_variables_sat.clear() # Clear previous variables
        self.var_all_satellites.set('0') # Reset 'All' state
        if not self.sat_frame: return # Guard clause

        # Configure grid columns in sat_frame (e.g., 2 columns)
        num_cols = 2
        for i in range(num_cols):
            self.sat_frame.columnconfigure(i, weight=1)

        all_satellites_with_all = ["All"] + satellites
        items_per_col = (len(all_satellites_with_all) + num_cols - 1) // num_cols

        for i, satellite_text in enumerate(all_satellites_with_all):
            is_all_checkbox = (satellite_text == "All")
            var = self.var_all_satellites if is_all_checkbox else tk.StringVar()
            cmd = self._toggle_all_satellites if is_all_checkbox else self._update_all_satellite_state

            # Default select the first *actual* satellite if available and it's not the 'All' checkbox
            # Also check if 'All' should be checked initially (if all individuals are checked)
            if not is_all_checkbox and i == 1: # i==1 corresponds to the first actual satellite
                var.set(satellite_text) # Pre-select the first satellite
                # Check if this pre-selection makes all satellites selected
                # This initial check might be complex if only one is pre-selected. 
                # Let's keep it simple: pre-select first, 'All' remains unchecked initially.            
            check = ttk.Checkbutton(
                self.sat_frame,
                text=satellite_text,
                variable=var,
                onvalue=satellite_text if not is_all_checkbox else '1', # 'All' uses '1'/'0'
                offvalue="" if not is_all_checkbox else '0',
                style="SecondaryCard.TCheckbutton",
                command=cmd
            )

            # Calculate grid position
            row = i // num_cols
            col = i % num_cols
            check.grid(row=row, column=col, sticky="w", pady=1, padx=5)

            if not is_all_checkbox:
                self.lst_variables_sat.append(var)
        
        # After creating all checkboxes, update the initial state of 'All'
        self._update_all_satellite_state() 

    def _toggle_all_satellites(self):
        """Checks or unchecks all individual satellite checkboxes based on the 'All' state."""
        if not self.sat_frame: return  # Guard clause to prevent NoneType error
        
        is_checked = self.var_all_satellites.get() == '1'
        for var in self.lst_variables_sat:
            # Get the text associated with the checkbutton for this var
            widget_text = "" 
            for widget in self.sat_frame.winfo_children():
                 if isinstance(widget, ttk.Checkbutton) and str(widget.cget('variable')) == str(var):
                     widget_text = widget.cget('text')
                     break
            if widget_text:
                 var.set(widget_text if is_checked else "")

    def _update_all_satellite_state(self):
        """Updates the 'All' satellite checkbox based on the state of individual checkboxes."""
        # Check if all individual satellites are selected (their var is not empty)
        all_checked = all(var.get() != "" for var in self.lst_variables_sat)
        # Check if any individual satellite is unselected (its var is empty)
        any_unchecked = any(var.get() == "" for var in self.lst_variables_sat)

        if all_checked:
            self.var_all_satellites.set('1')
        elif any_unchecked:
            self.var_all_satellites.set('0')
        # If the list is empty (no satellites for the instrument), uncheck 'All'
        elif not self.lst_variables_sat:
             self.var_all_satellites.set('0')

    def _create_activity_radiobuttons(self, activities, selected_instrument):
        """Create activity selection radio buttons within self.act_frame."""
        if not self.act_frame: return # Guard clause

        # Configure grid column in act_frame
        self.act_frame.columnconfigure(0, weight=1)

        # Set default activity if list is not empty
        if activities:
            self.activity_var.set(activities[0])
        else:
            self.activity_var.set("") # Clear if no activities

        for i, activity in enumerate(activities):            
            radio = ttk.Radiobutton(
                self.act_frame,
                text=activity,
                variable=self.activity_var,
                value=activity,
                style="AccentCard.TRadiobutton" # Use card style
            )
            # Use grid within the act_frame
            radio.grid(row=i, column=0, sticky="w", pady=2, padx=5)
        # Tooltip creation removed for simplicity

    def instrument_changed(self, *args):
        """Handle instrument selection change by updating satellite and activity options."""
        # Ensure frames exist before clearing
        self._clear_frame(self.sat_frame)
        self._clear_frame(self.act_frame)

        selected_instrument = self.instrument_var.get()
        if not selected_instrument: # Handle case where instrument might be empty initially
             self.update_status("Please select an instrument.")
             return

        self.act_params.instrument = selected_instrument

        # Safely get config, defaulting to empty lists if key missing
        instrument_config = config.CONFIG_INSTRUMENT.get(selected_instrument, {})
        satellites = instrument_config.get("satellites", [])
        activities = instrument_config.get("cdc_options", [])

        self._create_satellite_checkboxes(satellites)
        self._create_activity_radiobuttons(activities, selected_instrument)
        
        
    def _get_selected_satellites(self) -> List[str]:
        """Return selected satellite values."""
        return [var.get() for var in self.lst_variables_sat if var.get() != ""]

    def _validate_selections(self) -> bool:
        """Validate user selections before proceeding."""
        activity = self.activity_var.get()
        satellites = self._get_selected_satellites()
        selected_instrument = self.instrument_var.get()

        if not selected_instrument:
             basics.pop_up_message("Error", "Instrument not selected.", "error")
             return False

        logger.info(f"Selected satellite(s): {satellites}")
        if not satellites:
            basics.pop_up_message("Error", "Satellite not selected.", "error")
            return False

        # Safely get valid activities
        instrument_config = config.CONFIG_INSTRUMENT.get(selected_instrument, {})
        valid_activities = instrument_config.get("cdc_options", [])

        if not activity:
            basics.pop_up_message("Error", "Activity not selected.", "error")
            return False

        if activity not in valid_activities:
            logger.error(f"Activity '{activity}' is not valid for instrument {selected_instrument}")
            basics.pop_up_message("Error",
                f"Activity '{activity}' is not valid for {selected_instrument}.", "error")
            return False

        return True

    def execute_activity(self):
        """Validate selections and proceed to the selected activity frame."""
        if not self._validate_selections():
            logger.warning("Validation failed in execute_activity.")
            return

        # Selections are valid, update act_params and show next frame
        self.act_params.activity = self.activity_var.get()
        self.act_params.satellites = self._get_selected_satellites()
        # Instrument is already set in instrument_changed

        InstrumentHandler.handle_activity(self.app, self.act_params)


def create_app():
    """Create and run the application."""
    # Load all input files and templates into the in-memory cache before launching the GUI
    input_dirs = [
        os.path.join("assets", "Input_Files"),
        os.path.join("assets", "PAF"),
    ]
    cache.load_all(input_dirs)
    app = AppWindow()
    app.show_frame(UnifiedFrame)
    return app
