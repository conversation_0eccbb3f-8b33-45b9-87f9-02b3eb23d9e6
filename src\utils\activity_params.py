"""
Module providing a data structure for activity parameters that are shared between functions.
"""
from dataclasses import dataclass, field
from typing import List, Optional, Dict

# Activity to slicer mapping based on instrument_config.ini
ACTIVITY_SLICERS = {
    "Repeat Sequence": "repeat_sequence_id",
    "VCU Update": "icid",  # Note: icid_ver handled separately when needed
    "Scan Encoder LUT": "scan_encode_correction_lut_id",
    "Mission Scenario (SL+APC+SSL)": "scan_law_id",
    "Activity Table": "activity_table_id",
    # LI activities - no slicers in config
    "LOH BASIC Conf": "",
    "LOH OPER Conf": "",
    "LME Conf (Patch)": "",
    "LME Conf (PAF)": "",
    "LI Calibration": "",
}

@dataclass
class ActivityParams:
    """Data structure holding activity parameters that are commonly shared between functions"""
    
    activity: Optional[str] = None
    satellites: Optional[List[str]] = field(default=None)
    instrument: Optional[str] = None
    icid: Optional[int] = None  # ID for instrument configuration (slicer with icid_ver for INSTCONF)
    icid_ver: Optional[int] = None  # Version for instrument configuration
    repeat_sequence_id: Optional[int] = None  # Slicer for REPSEQ
    scan_encode_correction_lut_id: Optional[int] = None  # Slicer for SCANENC
    scan_law_id: Optional[int] = None  # Slicer for SCANLAW, SSCANLAW, and APCTAB
    activity_table_slot: Optional[int] = None  # Activity Table Slot
    activity_table_id: Optional[int] = None  # Activity Table ID
    mm_slot: Optional[int] = None  # Mass Memory slot
    side: Optional[str] = None     # VCU Memory side
    test_mode: bool = False        # Whether running in test mode
    repeat_sequence_index: Optional[int] = None
    activity_table_checksum: Optional[int] = None
    
    def get_slicer_name(self) -> str:
        """Get appropriate slicer attribute name based on activity type."""
        return ACTIVITY_SLICERS.get(self.activity, "")

    
    @classmethod
    def create(cls, 
              activity: str,
              satellites: List[str],
              instrument: str,
              icid: Optional[int] = None,
              icid_ver: Optional[int] = None,
              repeat_sequence_id: Optional[int] = None,
              scan_encode_correction_lut_id: Optional[int] = None,
              scan_law_id: Optional[int] = None,
              activity_table_id: Optional[int] = None,
              activity_table_slot: Optional[int] = None,
              mm_slot: Optional[int] = None,
              side: Optional[str] = None,
              test_mode: bool = False
    ) -> 'ActivityParams':
        """Factory method to create ActivityParams with default values
        
        Args:
            activity: Activity name from instrument's cdc_options
            satellites: List of satellite names to process
            instrument: Instrument name (FCI, LI, IRS, UVN)
            icid: ID for instrument configuration
            icid_ver: Version number for instrument configuration
            repeat_sequence_id: ID for repeat sequence configurations
            scan_encode_correction_lut_id: ID for scan encoder LUT
            scan_law_id: ID for scan law configurations
            activity_table_id: ID for activity table (IRS)
            mm_slot: Mass Memory slot identifier
            side: VCU Memory side ('Nominal' or 'Redundant')
            
        Returns:
            Initialized ActivityParams instance
        """
        return cls(
            activity=activity,
            satellites=satellites,
            instrument=instrument,
            icid=icid,
            icid_ver=icid_ver,
            repeat_sequence_id=repeat_sequence_id,
            scan_encode_correction_lut_id=scan_encode_correction_lut_id,
            scan_law_id=scan_law_id,
            activity_table_slot=activity_table_slot,
            activity_table_id=activity_table_id,
            mm_slot=mm_slot,
            side=side,
            test_mode=test_mode
        )
