"""This class hold the implementation of the converter memory image files to dictionary and vice versa, containing PTD tables.
"""

import datetime

# User Define Modules
from src.algorithms.uvn_functions.utils.bit_dec_converter import (
    convert_bit_to_dec, convert_dec_to_bit)
from src.utils.import_utils import basics
from src.logger_wrapper import logger
from .file_format_converter import FileFormatConverterInterface


class MemoryImageConverter(FileFormatConverterInterface):

    """

    This is a subclass of the FileFormatConverterInterface and override the methods
     file_to_dict(self,filename_img='')-> dict and
     dict_to_file(self,dict_ptd,output_file_path)

    """

    def __init__(self, memory_id):
        """Instantiate a MemoryImageConverter object

        Parameters
        ----------
        software_id : software id selected by the user
        memory_id : memory id selected by the user

        """

        super().__init__()
        self.memory_id = memory_id

    def convert_image_to_dict(self, filename_img=""):

        """Function to convert the content of memory image file into a dictionary containing PTD tables

        :param filename_img: Path of the memory image file containing PTD tables, defaults to ''
        :type filename_img: str, optional
        :param memory_id: Name of the target memory device, defaults to 'SDRAM'
        :type memory_id: enum['SDRAM','EEPROM_A','EEPROM_B'], optional
        :return: dictionary containing the PTD tables read from the memory image file
        :rtype: dictionary
        """
        dict_ptd = {}

        # Initialise memory image dictionary:
        obj_image = {}
        obj_image["head"] = {}
        obj_image[
            "body"
        ] = {}  # dict of decimal_addr:{"START":'hex',"COUNT":0,"DATA":'hex'}

        #  lst_head_field=['DOMAIN','TYPE','DESCRIPTION','CREATIONDATE','MODEL','MODELVER','DEVICE','STARTADDR','ENDADDR','LENGTH','CHECKSUM','UNIT']

        # Load memory image file:
        logger.info("Reading memory image file ...")
        with open(filename_img, "r") as f_img:
            i_line = 0
            for var_line in f_img:
                i_line += 1
                if var_line.split("=")[0] in self.lst_head_print:
                    obj_image["head"][var_line.split("=")[0]] = var_line.split("=")[1]
                elif var_line.split("=")[0] == "START":
                    try:
                        assert var_line.split(",")[0].split("=")[0] == "START"
                        assert var_line.split(",")[1].split("=")[0] == "COUNT"
                        assert var_line.split(",")[2].split("=")[0] == "DATA"
                        str_addr = var_line.split(",")[0].split("=")[1]
                        N_count = int(var_line.split(",")[1].split("=")[1], 16)
                        hex_data = var_line.split(",")[2].split("=")[1].strip()
                        int_addr = int(str_addr, 16)
                        assert len(hex_data) // 2 == N_count
                        obj_image["body"][int_addr] = {
                            "START": str_addr,
                            "COUNT": N_count,
                            "DATA": hex_data,
                        }
                    except:
                        raise Exception(
                            "Memory image file contains invalid line[%s]:%s"
                            % (i_line, var_line)
                        )
                else:
                    raise Exception(
                        "Memory image file contains invalid line[%s]:%s"
                        % (i_line, var_line)
                    )

        # Concatenate patch data as a single hexstring and verify there is no gap:
        DATA = ""
        lst_addr = sorted([var_addr for var_addr in obj_image["body"]])

        int_addr_ = lst_addr[0]
        for int_addr in lst_addr:

            N_count = obj_image["body"][int_addr]["COUNT"]
            str_addr = obj_image["body"][int_addr]["START"]
            hex_data = obj_image["body"][int_addr]["DATA"]

            if int_addr != int_addr_:
                raise Exception("Gap was detected before address 0x%s" % str_addr)

            int_addr_ = int_addr + N_count
            DATA += hex_data

        # Extract PTD table data:
        int_addr0 = lst_addr[0]
        addr_offset = int(self.ptd_start[self.memory_id][2:], 16) - int(
            self.ptd_start["SDRAM"][2:], 16
        )
        for k_ptd in self.ptd_layout:
            dict_ptd[k_ptd] = {}

            int_addr = int(self.ptd_layout[k_ptd]["addr"][2:], 16) + addr_offset
            N_len = self.ptd_layout[k_ptd]["N_len"]
            N_col = self.ptd_layout[k_ptd]["N_col"]
            N_row = self.ptd_layout[k_ptd]["N_row"]
            str_def = self.ptd_layout[k_ptd]["def"]

            # Extract columns name and type from PTD layout info, and initialise dict_ptd[k_ptd]:
            lst_col = []
            for var_str in str_def.split(","):
                var_name = var_str.split(":")[0]
                var_type = var_str.split(":")[1].split("[")[0]
                lst_col.append({"name": var_name, "type": var_type})

                dict_ptd[k_ptd][var_name] = []

            hex_data = DATA[
                (int_addr - int_addr0) * 2 : (int_addr - int_addr0 + N_len) * 2
            ]

            i_byte = 0
            for i_row in range(N_row):
                for i_col in range(N_col):
                    var_type = self.map_type_xls2img[lst_col[i_col]["type"]][0]
                    len_type = self.map_type_xls2img[lst_col[i_col]["type"]][1]
                    var_name = lst_col[i_col]["name"]
                    dat_bit = hex_data[i_byte * 2 : (i_byte + len_type // 8) * 2]
                    # logger.info('%s[%s,%s]=%s'%(k_ptd,i_row,i_col,dat_bit),lst_col[i_col]["type"],var_type,len_type,i_byte)

                    var = convert_bit_to_dec(
                        dat_bit=dat_bit, type=var_type, flag_hex=True
                    )

                    if var_type == "float":
                        var = float(
                            self.var_format.format(var)
                        )  # enforce fixed precision to prevent rounding errors
                    # if(var_type=='float'): var=str(var)#store as string to prevent rounding errors

                    dict_ptd[k_ptd][var_name].append(var)

                    i_byte += len_type // 8

        return dict_ptd

    def ptd_dict_to_file_format(self, dict_ptd={}, filename_img=""):
        assert True

    def convert_dict_to_image(
        self, dict_ptd={}, filename_img="", icu_id="ICU_A", software_id="ASW"
    ):
        """Function to convert the content of a dictionary containing PTD tables in to a memory image file

        :param dict_ptd: Dictionary containing PTD tables, defaults to {}
        :type dict_ptd: dict, optional
        :param filename_img: Path of the memory image file to be created, defaults to ''
        :type filename_img: str, optional
        :param icu_id: Name of ICU side, defaults to 'ICU_A'
        :type icu_id: enum['ICU_A','ICU_B'], optional
        :param memory_id: Name of the target memory device, defaults to 'SDRAM'
        :type memory_id: enum['SDRAM','EEPROM_A','EEPROM_B'], optional
        :param sw_id: Select the SW that will perform the image patching, defaults to 'ASW'
        :type sw_id: enum['ASW','SSW'], optional
        """

        # ,icu_id='ICU_A',memory_id='SDRAM',sw_id='ASW'
        # lst_head_print=['DOMAIN','TYPE','DESCRIPTION','CREATIONDATE','MODEL','MODELVER','DEVICE','STARTADDR','ENDADDR','LENGTH','CHECKSUM','UNIT']

        # Initialise memory image dictionary:
        obj_image = {}
        obj_image["head"] = {}
        obj_image[
            "body"
        ] = {}  # dict of decimal_addr:{"START":'hex',"COUNT":0,"DATA":'hex'}

        memory_device = "S4"
        if icu_id == "ICU_A":
            memory_device += "A"
        elif icu_id == "ICU_B":
            memory_device += "B"
        else:
            raise Exception('Invalid value for "icu_id"=%s' % (icu_id))
        if software_id == "ASW":
            memory_device += "A"
        elif software_id == "SSW":
            memory_device += "S"
        else:
            raise Exception('Invalid value for "SW_id"=%s' % (software_id))
        if self.memory_id == "SDRAM":
            memory_device += "RAM"
        elif self.memory_id == "EEPROM_A":
            memory_device += "EEPA"
        elif self.memory_id == "EEPROM_B":
            memory_device += "EEPB"
        else:
            raise Exception('Invalid value for "memory_id"=%s' % (self.memory_id))
        memory_model = memory_device + "_C_20230406.MMF"

        # Prepare memory image file header:
        obj_image["head"]["DOMAIN"] = "MTS1"
        obj_image["head"]["TYPE"] = "REFERENCE"
        obj_image["head"]["DESCRIPTION"] = "PTD tables memory image"
        obj_image["head"]["CREATIONDATE"] = datetime.datetime.now(
            datetime.timezone.utc
        ).strftime("%Y-%m-%dT%H:%M:%S.%f")
        obj_image["head"]["MODEL"] = memory_model
        obj_image["head"]["MODELVER"] = "1.0"
        obj_image["head"]["DEVICE"] = memory_device
        obj_image["head"]["STARTADDR"] = "TBD"  #'40000000'
        obj_image["head"]["ENDADDR"] = "TBD"  #'401C685F'
        obj_image["head"]["LENGTH"] = "TBD"  #'1331216'
        obj_image["head"]["CHECKSUM"] = "TBD"  #'7EA1'
        obj_image["head"]["UNIT"] = "1"

        addr_offset = int(self.ptd_start[self.memory_id][2:], 16) - int(
            self.ptd_start["SDRAM"][2:], 16
        )

        # Convert each PTD table into an hexstring:
        for k_ptd in self.ptd_layout:
            str_addr = self.ptd_layout[k_ptd]["addr"]
            N_pad = self.ptd_layout[k_ptd]["N_pad"]
            N_row = self.ptd_layout[k_ptd]["N_row"]
            N_col = self.ptd_layout[k_ptd]["N_col"]
            str_def = self.ptd_layout[k_ptd]["def"]

            int_addr = int(str_addr[2:], 16) + addr_offset
            str_addr_ = format(int_addr, "x").zfill(8).upper()

            assert int_addr not in obj_image["body"]
            obj_image["body"][int_addr] = {"START": str_addr_, "COUNT": 0, "DATA": ""}

            # Extract columns name and type from PTD layout info:
            lst_col = []
            for var_str in str_def.split(","):
                var_name = var_str.split(":")[0]
                var_type = var_str.split(":")[1].split("[")[0]
                lst_col.append({"name": var_name, "type": var_type})

            assert N_col == len(lst_col)

            logger.info("Reading PTD table %s ..." % (k_ptd))
            for i_row in range(N_row):
                for i_col in range(N_col):
                    k_col = lst_col[i_col]["name"]
                    var_type = lst_col[i_col]["type"]

                    k_col_ = k_col
                    # if(k_ptd+'.'+k_col_ in dict_rename):
                    #    k_col_=dict_rename[k_ptd+'.'+k_col_]

                    # var_cell=netcdf_object[k_ptd].variables[k_col_][:].data[i_row]
                    var_type_ = self.map_type_xls2img[var_type]
                    var_cell = dict_ptd[k_ptd][k_col_][i_row]
                    # if(var_type_[1]=='float'):
                    #    var_cell=float(var_cell)

                    var_hex = convert_dec_to_bit(
                        val_dec=var_cell,
                        len_bit=var_type_[1],
                        type=var_type_[0],
                        flag_hex=True,
                    )

                    assert var_type_[1] // 8 == len(var_hex) // 2

                    obj_image["body"][int_addr]["COUNT"] += len(var_hex) // 2
                    obj_image["body"][int_addr]["DATA"] += var_hex.upper()

            # add padding:
            obj_image["body"][int_addr]["COUNT"] += N_pad
            obj_image["body"][int_addr]["DATA"] += "00" * N_pad

            assert (
                obj_image["body"][int_addr]["COUNT"]
                == len(obj_image["body"][int_addr]["DATA"]) / 2
            )

        # Generate memory image file:
        logger.info("Processing collected data ...")
        lst_addr = sorted(
            [var_addr for var_addr in obj_image["body"]]
        )  # re-order lines if necessary
        beg_addr = None
        end_addr = None
        len_addr = None
        len_line = 15  # number of bytes per img file line
        DATA = ""
        for var_addr in lst_addr:

            var_length = obj_image["body"][var_addr]["COUNT"]

            # Check that there is no gap or overlap with previous line:
            if end_addr != None:
                assert end_addr + 1 == var_addr

            # Update patch length and start/end addresses
            if beg_addr == None:
                beg_addr = var_addr
            if end_addr == None:
                end_addr = var_addr + var_length - 1
            if var_addr < beg_addr:
                beg_addr = var_addr
            if var_addr + var_length - 1 > end_addr:
                end_addr = var_addr + var_length - 1
            len_addr = end_addr - beg_addr + 1

            # concatenate patch data in a single string:
            DATA += obj_image["body"][var_addr]["DATA"]

        # Update memory image file header:
        obj_image["head"]["STARTADDR"] = convert_dec_to_bit(
            val_dec=beg_addr, len_bit=32, type="integer", flag_hex=True
        ).upper()
        obj_image["head"]["ENDADDR"] = convert_dec_to_bit(
            val_dec=end_addr, len_bit=32, type="integer", flag_hex=True
        ).upper()
        obj_image["head"]["LENGTH"] = str(len_addr)
        obj_image["head"]["CHECKSUM"] = basics.checksum(DATA, "CRC16").upper()

        # Print memory image file:
        logger.info("Writing memory image file ...")
        with open(filename_img, "w+b") as f_img:
            # Print file header:
            for k_field in self.lst_head_print:
                f_img.write((k_field + "=" + obj_image["head"][k_field] + "\n").encode('utf-8'))

            # Print file body:
            # NB: field 'COUNT' to be printed as hex
            var_addr = beg_addr
            while var_addr <= end_addr:
                str_addr = convert_dec_to_bit(
                    val_dec=var_addr, len_bit=32, type="integer", flag_hex=True
                ).upper()

                i_var = (var_addr - beg_addr) * 2
                str_data = DATA[i_var : (i_var + 2 * len_line)]
                str_count = hex(len(str_data) // 2)[2:].upper()
                f_img.write(
                    ("START=%s,COUNT=%s,DATA=%s\n" % (str_addr, str_count, str_data)).encode('utf-8')
                )

                var_addr += len_line

            logger.info("END ...")
