"""IRS VCU Memory Numerical Offset module"""
from typing import Dict, List, Any
import pandas as pd
import os
import math
from datetime import datetime
# Local imports
from src.utils.import_utils import config
from src.utils.conversion_utils import dec2hex, dec2bin, hex2dec
from src.utils.netcdf_utils import read_netCDF
from src.utils.activity_params import ActivityParams
from src.logger_wrapper import logger
from .memory_image_file_IRS import memory_image_file_IRS

# Type aliases
MemoryMapType = List[List[str]]
VSMDictType = Dict[str, List[int]]
NetCDFDictType = Dict[str, Any]

# Global variables for storing NOF (Numerical Offset) data
glob_NOF_StartAdd: Dict[str, int] = {}  # Maps band names to their starting memory addresses
glob_NOF_BlkLen: Dict[str, int] = {}    # Maps band names to their block lengths in memory

def _process_band_offsets(VSM_dict: VSMDictType) -> None:
    """Process IRS band offsets and map them to individual detector offsets.
    
    Args:
        VSM_dict: Dictionary containing VSM values keyed by mnemonic
        
    Each IRS band has multiple detectors, and this function maps
    band-based offsets to individual detector offsets.
    """
    logger.debug("Processing IRS band offsets to detector offsets")
    for int_band in range(1, 5):  # IRS bands 1-4
        detector_start = 1  # Detector numbering starts at 1
        band_name = f"BAND{int_band}_NUMOFFSET"
        
        if band_name in VSM_dict:
            # Map band offsets to individual detector offsets
            # e.g., BAND1_NUMOFFSET -> [BAND1_NUMOFFSET_D1, BAND1_NUMOFFSET_D2, ...]
            for detector in range(detector_start, detector_start + len(VSM_dict[band_name])):
                VSM_dict[f"{band_name}_D{detector}"] = VSM_dict[band_name][detector - detector_start]
            
            # Update start position for next band
            detector_start = detector + 1
            
            # Remove band entry as it's now split into detectors
            del VSM_dict[band_name]

def _create_memory_blocks(
    address: str,
    binary_data: str,
    byte_length: int,
    dict_config: Dict[str, Any]
) -> List[List[str]]:
    """Create memory blocks for given data, splitting if necessary.
    
    Args:
        address: Memory address in hex
        binary_data: Binary data string
        byte_length: Length in bytes
        dict_config: Configuration dictionary
        
    Returns:
        List of memory blocks [address, count, data]
    """
    blocks: List[List[str]] = []
    addLenSAU = byte_length // 2  # SAU=2 means 16-bit alignment

    if addLenSAU > dict_config["count_max"]:
        logger.info("Splitting large data block into multiple memory units")
        # First block
        blocks.append([
            address[2:],  # Strip '0x' prefix
            dec2hex(dict_config["count_max"]),
            binary_data[0 : dict_config["count_max"] * 2]
        ])

        # Calculate remaining blocks
        line_slices = math.floor(addLenSAU / dict_config["count_max"])
        
        for slices in range(line_slices):
            # Calculate address for this block
            add_2 = dec2hex(hex2dec(address) + dict_config["count_max"] * (slices + 1))
            
            # Handle last block
            if slices == line_slices - 1:
                Number_Bytes = addLenSAU % dict_config["count_max"]
                End_index = byte_length * 2
            else:
                Number_Bytes = dict_config["count_max"]
                End_index = ((dict_config["count_max"] * (slices + 1)) * 2) + dict_config["count_max"] * 2
            
            Start_index = (dict_config["count_max"] * (slices + 1)) * 2
            
            if Number_Bytes != 0:
                blocks.append([
                    add_2,
                    dec2hex(Number_Bytes),
                    binary_data[Start_index:End_index]
                ])
    else:
        blocks.append([address[2:], dec2hex(byte_length), binary_data])

    return blocks

def IRS_VCU_MEM_NUMOFF(
    act_params: ActivityParams,
    satellite: str,
    lst_used_files: List[str],
    Mem_Map: pd.DataFrame,
    df_VSM: pd.DataFrame,
    lst_bands: List[str],
    n_band: int
) -> None:
    """Handles numerical offset updates for IRS VCU memory
    
    Args:
        act_params: Activity parameters containing configuration details
        satellite: Satellite identifier
        lst_used_files: List of configuration files to use
        Mem_Map: Memory map configuration data
        df_VSM: VSM configuration data
        lst_bands: List of bands to process
        n_band: Band number identifier
    """
    try:
        # Initialize variables and get config
        dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
        str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], satellite)
        VSM_dict: VSMDictType = {}
        main_files = ["INSTCONF"]

        # STEP 1: Read netCDF files
        logger.info("Reading netCDF files for IRS VCU numerical offsets")
        dict_netCDF = read_netCDF(
            act_params,
            satellite,
            main_files,
            lst_used_files,
            dict_config
        )

        # STEP 2: Process VSM variables from IRS_VCU_CONF_Config.xlsx
        logger.info("Processing IRS VSM variables from IRS_VCU_CONF_Config.xlsx")
        VSM_Clean = df_VSM[df_VSM["Activity"].str.contains("VCU_NUMOFF")]
        VSM_Clean.set_index("Name", inplace=True)

        # Map netCDF values to mnemonics
        logger.info(f"Mapping netCDF values to VSM mnemonics for bands: {lst_bands}")
        for variable in VSM_Clean.index:
            if variable in dict_netCDF:
                VSM_dict[VSM_Clean.loc[variable, "Mnemonic in Map"]] = dict_netCDF[variable].tolist()
            else:
                logger.warning(f"Variable {variable} not found in netCDF data")

        # Process band offsets
        _process_band_offsets(VSM_dict)

        # STEP 3: Create memory map from IRS configuration
        logger.info("Creating IRS memory map with values from IRS_VCU_CONF_Config.xlsx")
        Map_Clean = Mem_Map[
            Mem_Map["Parameter Logical Identifier"].isin(list(VSM_dict.keys()))
        ].copy()
        Map_Clean["Value"] = Map_Clean["Parameter Logical Identifier"].map(VSM_dict)

        # Filter for selected bands
        Map_Final = Map_Clean if lst_bands == ["All"] else Map_Clean[Map_Clean["Band"].isin(lst_bands)]
        
        if Map_Final.empty:
            logger.warning(f"No memory map entries found for bands: {lst_bands}")
            return

        # STEP 4: Generate memory image content
        logger.info("Generating IRS memory image content")
        Bytes_Dict_RAM: MemoryMapType = [["Start", "Count", "Data"]]

        for add in sorted(set(Map_Final["RAM Addr (Hex)"].to_list())):
            Map_Add = Map_Final[Map_Final["RAM Addr (Hex)"] == add]
            
            # Combine all bits for this address
            Bytes_Bin_All = ""
            AddLen_Bits = 0
            
            for sb in sorted(Map_Add["Start Bit"].to_list()):
                Map_Bit = Map_Add[Map_Add["Start Bit"] == sb]
                Val = int(Map_Bit["Value"].values[0])
                Len = int(Map_Bit["Length (bits)"].values[0])
                Bytes_Bin_All += dec2bin(Val).zfill(Len)
                AddLen_Bits += Len

            # Pad to 16-bit boundary (SAU=2)
            Bits_Left = 16 - (AddLen_Bits % 16) if (AddLen_Bits % 16 != 0) else 0
            Bytes_Content_Bin = f"{Bytes_Bin_All}{'0' * Bits_Left}" if Bits_Left > 0 else Bytes_Bin_All

            # Convert to bytes
            AddLen = len(Bytes_Content_Bin) // 8
            Bytes = dec2hex(int(Bytes_Content_Bin, 2)).zfill(2 * AddLen)

            # Create memory blocks
            blocks = _create_memory_blocks(add, Bytes, AddLen, dict_config)
            Bytes_Dict_RAM.extend(blocks)

        if len(Bytes_Dict_RAM) <= 1:
            logger.warning("No memory blocks generated, check configuration data")
            return
            
        # STEP 5: Create dump data for PAF
        NOF_start_add = hex2dec(Bytes_Dict_RAM[1][0])
        NOF_end_add = hex2dec(Bytes_Dict_RAM[-1][0]) + int(int(Bytes_Dict_RAM[-1][1], 16) / 2)
        LenSAU = NOF_end_add - NOF_start_add

        # Store for global access
        glob_NOF_StartAdd[lst_bands[0]] = NOF_start_add
        glob_NOF_BlkLen[lst_bands[0]] = LenSAU

        # STEP 6: Create memory image file
        logger.info("Creating IRS memory image file")
        device = f"VCU_RAM{'N' if act_params.side == 'Nominal' else 'R'}"
        part = f"PART{n_band}_" if n_band != 0 else ""
          # Generate filename
        str_filename = (
            f"{satellite[0:4]}{satellite[6]}_"
            f"{dict_config['file_class']}_OBS_MIMG_000_{device}_"
            f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{part}"
            f"{str(act_params.icid).zfill(2)[-2:]}{str(act_params.icid_ver).zfill(2)[-2:]}.IMG"
        )
        str_path_full = os.path.join(str_output_folder, str_filename)
        
        logger.info(f"Generating IRS memory image file: {str_filename}")

        # Create memory image
        memory_image_file_IRS(
            Bytes_Dict_RAM,
            "VCU_NUMOFFSET",
            str_path_full,
            satellite,
            device,
            lst_bands,
            n_band
        )
        
        logger.info(f"Successfully generated IRS numerical offset memory image: {str_filename}")
        
    except Exception as e:
        logger.error(f"Error in IRS VCU numerical offset generation: {str(e)}")
        raise
