#!/usr/bin/env python3
"""
Compare IRS vs FCI configurations to show the differences.
"""

import pandas as pd
from xldt_reader_standalone import X<PERSON>TReader


def compare_configurations():
    """Compare IRS and FCI configurations."""
    print("🔍 IRS vs FCI Configuration Comparison")
    print("=" * 50)
    
    # Load configurations
    irs_config = pd.read_csv("config/IRS/IRS_SL_Conf.csv")
    fci_config = pd.read_csv("config/FCI/FCI_SL_Conf.csv")
    
    print("\n📋 IRS Configuration (Infrared Sounder):")
    print("-" * 40)
    print(irs_config.to_string(index=False))
    
    print(f"\n📊 IRS Sections: {len(irs_config)} total")
    for _, row in irs_config.iterrows():
        if pd.notna(row['Length']):
            print(f"   ✅ {row['Section']}: {row['Length']} bytes (Order: {row['Order']})")
        else:
            print(f"   📄 {row['Section']}: Header section (Order: {row['Order']})")
    
    print(f"\n📋 FCI Configuration (Flexible Combined Imager):")
    print("-" * 40)
    print(fci_config.to_string(index=False))
    
    print(f"\n📊 FCI Sections: {len(fci_config)} total")
    for _, row in fci_config.iterrows():
        if pd.notna(row['Length']):
            print(f"   ✅ {row['Section']}: {row['Length']} bytes (Order: {row['Order']})")
        else:
            print(f"   📄 {row['Section']}: Header section (Order: {row['Order']})")
    
    # Show differences
    irs_sections = set(irs_config['Section'].tolist())
    fci_sections = set(fci_config['Section'].tolist())
    
    common_sections = irs_sections & fci_sections
    irs_only = irs_sections - fci_sections
    fci_only = fci_sections - irs_sections
    
    print(f"\n🔄 Section Comparison:")
    print("-" * 30)
    print(f"   📦 Common sections: {len(common_sections)}")
    for section in sorted(common_sections):
        print(f"      ✅ {section}")
    
    if irs_only:
        print(f"   🔴 IRS-only sections: {len(irs_only)}")
        for section in sorted(irs_only):
            print(f"      🎯 {section}")
    
    if fci_only:
        print(f"   🔵 FCI-only sections: {len(fci_only)}")
        for section in sorted(fci_only):
            print(f"      🎯 {section}")
    
    print(f"\n💡 Key Differences:")
    print("-" * 20)
    print(f"   🔴 IRS does NOT use Rally and Retrace sections")
    print(f"   🔵 FCI includes Rally and Retrace for imaging operations")
    print(f"   📊 Both use FDA (main scan law data) and MPA sections")
    print(f"   📄 Both have identical header structure")


def test_with_sample_file():
    """Test both configurations with a sample file."""
    print(f"\n🧪 Testing with Sample File:")
    print("-" * 30)
    
    test_file = "inputs/ScanLaw_Central_Summer_16384.xldt"
    
    # Test IRS configuration
    print(f"   🔴 IRS Analysis:")
    try:
        irs_reader = XLDTReader(config_path="config/IRS/IRS_SL_Conf.csv")
        irs_data = irs_reader.read_xldt_file(test_file)
        irs_sections = list(irs_data['sections'].keys())
        print(f"      Sections found: {len(irs_sections)}")
        for section in irs_sections:
            if hasattr(irs_data['sections'][section], 'length'):
                print(f"         ✅ {section}: {irs_data['sections'][section].length} bytes")
            else:
                print(f"         📄 {section}: Header section")
    except Exception as e:
        print(f"      ❌ Error: {e}")
    
    # Test FCI configuration
    print(f"   🔵 FCI Analysis:")
    try:
        fci_reader = XLDTReader(config_path="config/FCI/FCI_SL_Conf.csv")
        fci_data = fci_reader.read_xldt_file(test_file)
        fci_sections = list(fci_data['sections'].keys())
        print(f"      Sections found: {len(fci_sections)}")
        for section in fci_sections:
            if hasattr(fci_data['sections'][section], 'length'):
                print(f"         ✅ {section}: {fci_data['sections'][section].length} bytes")
            else:
                print(f"         📄 {section}: Header section")
    except Exception as e:
        print(f"      ❌ Error: {e}")


def show_recommendations():
    """Show usage recommendations."""
    print(f"\n📋 Usage Recommendations:")
    print("-" * 30)
    print(f"   🔴 For IRS (Infrared Sounder) files:")
    print(f"      Use: config/IRS/IRS_SL_Conf.csv")
    print(f"      Sections: Header, LAC_Pointer, FDA, MPA")
    print(f"      Purpose: Infrared sounding scan laws")
    
    print(f"   🔵 For FCI (Flexible Combined Imager) files:")
    print(f"      Use: config/FCI/FCI_SL_Conf.csv") 
    print(f"      Sections: Header, LAC_Pointer, Retrace, Rally, FDA, MPA")
    print(f"      Purpose: Imaging scan laws with retrace/rally operations")
    
    print(f"\n⚠️  Important Notes:")
    print(f"   • Rally and Retrace are NOT implemented for IRS")
    print(f"   • Using wrong config may show irrelevant sections")
    print(f"   • IRS config now correctly excludes Rally/Retrace")
    print(f"   • Both configs share FDA (main data) and MPA sections")


if __name__ == "__main__":
    compare_configurations()
    test_with_sample_file()
    show_recommendations()
    
    print(f"\n🎉 Configuration comparison complete!")
    print(f"   ✅ IRS config updated to exclude Rally/Retrace")
    print(f"   ✅ FCI config maintains all sections")
    print(f"   ✅ Both configs tested and working")
