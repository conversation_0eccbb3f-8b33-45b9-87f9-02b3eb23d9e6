"""
FCI GUI module for handling FCI-specific activities
"""

from .frames import BaseFrame

# Import FCI-specific frames
from .fci_gui_features.miss_sce_gui import MISS_SCE_GUI
from .fci_gui_features.vcu_gui import VCU_GUI
from .fci_gui_features.rep_seq_gui import REP_SEQ_GUI
from .fci_gui_features.sca_enc_gui import SCA_ENC_GUI

class FCI_GUI(BaseFrame):
    """Main FCI GUI class"""
    
    @staticmethod
    def register_activities(handler):
        # Register FCI activities with the handler
        handler.register_activity("FCI", "Mission Scenario (SL+APC+SSL)", MISS_SCE_GUI)
        handler.register_activity("FCI", "VCU Update", VCU_GUI)
        handler.register_activity("FCI", "Repeat Sequence", REP_SEQ_GUI)
        handler.register_activity("FCI", "Scan Encoder LUT", SCA_ENC_GUI)
