"""
UVN GUI module for handling UVN-specific activities
"""

from .frames import BaseFrame

# Import UVN-specific frames
from .uvn_gui_features.memory_Image_Generator_gui import MemoryImageGenerator

class UVN_GUI(BaseFrame):
    """Main UVN GUI class"""
    
    @staticmethod
    def register_activities(handler):
        # Register UVN activities with the handler
        handler.register_activity("UVN", "Generate PTD Memory image File", MemoryImageGenerator)
