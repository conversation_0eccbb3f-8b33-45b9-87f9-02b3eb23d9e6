# XLDT File Reader

## Overview

The XLDT (Transfer Layer Data) File Reader is a comprehensive tool for parsing and analyzing XLDT binary files used in MTG (Meteosat Third Generation) satellite operations. This tool can read XLDT files created by the satellite operations system and parse them into structured, readable data.

## Features

- **Binary File Parsing**: Read and parse XLDT binary files
- **Configuration Support**: Use CSV configuration files to define section structures
- **CRC Validation**: Validate file integrity using CRC checksums
- **Multiple Output Formats**: Export data in JSON, summary, or detailed formats
- **Command-Line Interface**: Easy-to-use CLI for batch processing
- **Python API**: Programmatic access for integration into other tools

## XLDT File Structure

XLDT files follow a specific binary structure:

```
┌─────────────────┐
│   XLDT Header   │  (4 bytes: Format ID + MM Slot)
├─────────────────┤
│      CRC        │  (2 bytes: Checksum)
├─────────────────┤
│     Header      │  (Variable: Section header)
├─────────────────┤
│  LAC_Pointer    │  (Variable: LAC pointer data)
├─────────────────┤
│    Retrace      │  (Variable: Retrace data)
├─────────────────┤
│     Rally       │  (Variable: Rally data)
├─────────────────┤
│      FDA        │  (Variable: FDA data)
├─────────────────┤
│      MPA        │  (Variable: MPA data)
└─────────────────┘
```

## Installation

The XLDT reader is part of the CDC-S project. Ensure you have the required dependencies:

```bash
pip install pandas numpy
```

## Usage

### Python API

```python
from src.utils.xldt_reader import XLDTReader

# Initialize reader with configuration
reader = XLDTReader(config_path="config/IRS/IRS_SL_Conf.csv")

# Read XLDT file
parsed_data = reader.read_xldt_file("path/to/your/file.bin")

# Access parsed data
header = parsed_data['header']
print(f"Format ID: {header.format_id}")
print(f"MM Slot: {header.mm_slot}")

# Validate CRC
if 'CRC' in parsed_data['sections']:
    crc_valid = reader.validate_crc(
        parsed_data['raw_hex'],
        parsed_data['sections']['CRC']
    )
    print(f"CRC Valid: {crc_valid}")

# Export to readable format
readable_data = reader.export_to_dict(parsed_data)
```

### Command-Line Interface

```bash
# Basic usage
python src/utils/xldt_cli.py input.bin

# With configuration
python src/utils/xldt_cli.py input.bin --config config/IRS/IRS_SL_Conf.csv

# Export to JSON
python src/utils/xldt_cli.py input.bin --output output.json --format json

# Validate CRC
python src/utils/xldt_cli.py input.bin --validate-crc

# Detailed output
python src/utils/xldt_cli.py input.bin --format detailed --verbose
```

## Configuration Files

Configuration files define the structure of XLDT sections. They are CSV files with the following format:

```csv
Section,Length,MSDF_ID_Hex,Order
XLDT_Header,,,1
CRC,,,2
Header,4,0,3
LAC_Pointer,20,3,4
Retrace,40,7,5
Rally,40,17,6
FDA,1680,1,7
MPA,4160,2,8
```

### Configuration Fields

- **Section**: Name of the section
- **Length**: Length in bytes (empty for header/CRC)
- **MSDF_ID_Hex**: Message Specification Data Format ID in hex
- **Order**: Processing order of sections

### Available Configurations

- `config/IRS/IRS_SL_Conf.csv`: IRS (Infrared Sounder) Scan Law configuration
- `config/FCI/FCI_SL_Conf.csv`: FCI (Flexible Combined Imager) Scan Law configuration

## Examples

### Basic File Reading

```python
from src.utils.xldt_reader import XLDTReader

# Read without configuration (generic parsing)
reader = XLDTReader()
data = reader.read_xldt_file("sample.bin")

print(f"File size: {data['file_info']['file_size']} bytes")
print(f"Format ID: {data['header'].format_id}")
print(f"MM Slot: {data['header'].mm_slot}")
```

### Batch Processing

```python
import os
from src.utils.xldt_reader import XLDTReader

reader = XLDTReader(config_path="config/IRS/IRS_SL_Conf.csv")

# Process all .bin files in directory
for filename in os.listdir("xldt_files/"):
    if filename.endswith('.bin'):
        try:
            data = reader.read_xldt_file(f"xldt_files/{filename}")
            print(f"✓ {filename}: {data['file_info']['file_size']} bytes")
        except Exception as e:
            print(f"✗ {filename}: {e}")
```

### CRC Validation

```python
from src.utils.xldt_reader import XLDTReader

reader = XLDTReader(config_path="config/IRS/IRS_SL_Conf.csv")
data = reader.read_xldt_file("file.bin")

if 'CRC' in data['sections']:
    is_valid = reader.validate_crc(data['raw_hex'], data['sections']['CRC'])
    print(f"File integrity: {'VALID' if is_valid else 'INVALID'}")
```

## Output Formats

### Summary Format
```
XLDT File Summary
==================================================
File: sample.bin
Size: 1024 bytes
Format ID: 1
MM Slot: 5
CRC Status: VALID

Sections (6):
------------------------------
  Header: 4 bytes
  LAC_Pointer: 20 bytes
  Retrace: 40 bytes
  Rally: 40 bytes
  FDA: 1680 bytes
  MPA: 4160 bytes
```

### JSON Format
```json
{
  "file_info": {
    "file_path": "sample.bin",
    "file_size": 1024
  },
  "header": {
    "format_id": 1,
    "mm_slot": 5
  },
  "sections": {
    "Header": {
      "length": 4,
      "data_hex": "12345678",
      "order": 3
    }
  }
}
```

## Testing

Run the test suite to verify functionality:

```bash
python tests/test_xldt_reader.py
```

## Error Handling

The XLDT reader includes comprehensive error handling:

- **File Not Found**: Clear error message for missing files
- **Invalid Configuration**: Validation of configuration file format
- **Corrupted Data**: Graceful handling of malformed binary data
- **CRC Mismatch**: Detection and reporting of data integrity issues

## Integration

The XLDT reader integrates seamlessly with the existing CDC-S codebase:

- Uses existing logging infrastructure
- Compatible with conversion utilities
- Follows project coding standards
- Supports both IRS and FCI configurations

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the `src` directory is in your Python path
2. **Configuration Not Found**: Check the path to your CSV configuration file
3. **Binary Data Issues**: Verify the XLDT file is not corrupted
4. **CRC Validation Fails**: May indicate file corruption or incorrect parsing

### Debug Mode

Enable verbose logging for detailed debugging:

```bash
python src/utils/xldt_cli.py input.bin --verbose
```

## Contributing

When extending the XLDT reader:

1. Follow the existing code structure
2. Add appropriate tests for new functionality
3. Update documentation
4. Ensure compatibility with both IRS and FCI configurations

## Related Files

- `src/functions.py`: Contains `create_XLDT()` function for file creation
- `src/algorithms/irs_functions/IRS_SL.py`: IRS scan law implementation
- `src/algorithms/fci_functions/FCI_SL.py`: FCI scan law implementation
- `config/IRS/IRS_SL_Conf.csv`: IRS configuration
- `config/FCI/FCI_SL_Conf.csv`: FCI configuration
