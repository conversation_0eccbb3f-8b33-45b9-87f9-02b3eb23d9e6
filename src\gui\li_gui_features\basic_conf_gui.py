import tkinter as tk
from tkinter import ttk
from typing import List, Dict
from src import functions
from src.utils.import_utils import basics, config
from src.utils.activity_params import ActivityParams
from ...logger_wrapper import logger
from .common_functions import enable_extra_options
from ..frames import BaseFrame
from ..theme_manager import ThemeManager
from ..custom_widgets import PrimaryCard, SecondaryCard, AccentCard, SuccessCard

class BASIC_CONF_GUI(BaseFrame):
    """GUI for LI Basic Configuration"""

    def __init__(self, parent: tk.Widget, act_params: ActivityParams, *args, **kwargs):
        """Initialize Basic Configuration GUI frame."""
        self.act_params = act_params
        self.lst_variables_fee = []
        self.lst_variables_tc = []
        self.var_Mem = tk.StringVar()
        self.var_format = tk.StringVar()
        self.var_Mer = tk.StringVar()
        self.input_icid = None
        self.input_icid_ver = None
        self.merge_radios: Dict[str, ttk.Radiobutton] = {} # Initialize merge_radios dict
        super().__init__(parent, *args, **kwargs)
    
    
    def create_widgets(self):
        """Create and layout GUI components using grid."""
        logger.info("MTG-I Imagers CDC - MTG-I LI CDC Input Selection - Basic Conf")

        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Main frame using grid
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)
        # Initial call to set merge radio state correctly after widgets are created
        enable_extra_options(self.lst_variables_fee, self.merge_radios, self.var_Mem)


    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"LI Basic Configuration - {', '.join(self.act_params.satellites or [])}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew")
        
        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5)


    def _create_body(self, parent):
        """Creates the body section with two columns."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)
        body_frame.columnconfigure(0, weight=1, minsize=250) # Left column
        body_frame.columnconfigure(1, weight=1, minsize=250) # Right column
        body_frame.rowconfigure(0, weight=1) # Allow columns to expand vertically

        self._create_left_column(body_frame)
        self._create_right_column(body_frame)


    def _create_left_column(self, parent):
        """Creates the left column widgets."""
        left_column = ttk.Frame(parent)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        # Configure rows for proper spacing and expansion
        left_column.rowconfigure(0, weight=0) # Top row (FEE/Mem Type)
        left_column.rowconfigure(1, weight=0) # Format Card
        left_column.rowconfigure(2, weight=0) # ICID Card
        left_column.rowconfigure(3, weight=1) # Spacer/Expander
        left_column.columnconfigure(0, weight=1)

        # Top row frame for FEE and Memory Type
        top_row_frame = ttk.Frame(left_column)
        top_row_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        top_row_frame.columnconfigure(0, weight=1)
        top_row_frame.columnconfigure(1, weight=1)

        self._create_fee_selection(top_row_frame)
        self._create_memory_type(top_row_frame)
        self._create_format_selection(left_column)
        self._create_icid_config(left_column)


    def _create_right_column(self, parent):
        """Creates the right column widgets."""        
        right_column = ttk.Frame(parent)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        # Configure rows
        right_column.rowconfigure(0, weight=0) # TC Selection
        right_column.rowconfigure(1, weight=0) # Merge Card
        right_column.rowconfigure(2, weight=1) # Spacer/Expander
        right_column.columnconfigure(0, weight=1)

        self._create_tc_selection(right_column)
        self._create_merge_options(right_column)


    def _create_fee_selection(self, parent):
        """Creates the FEE Selection card."""
        fee_card = PrimaryCard(parent, title="FEE Selection", padding=5)
        fee_card.grid(row=0, column=0, sticky="nsew", padx=(0, 3))
        
        fee_content = fee_card.get_content_frame()
        fee_content.columnconfigure(0, weight=1) # Configure grid inside card
        lst_fee_name = ["ALL FEEs", "FEE 1", "FEE 2", "FEE 3", "FEE 4"]
        self.lst_variables_fee = []

        def toggle_all_fees():
            all_selected = self.lst_variables_fee[0].get() == lst_fee_name[0]
            for i in range(1, len(lst_fee_name)):
                self.lst_variables_fee[i].set(lst_fee_name[i] if all_selected else "")
            # Pass self.merge_radios dictionary
            enable_extra_options(self.lst_variables_fee, self.merge_radios, self.var_Mem)

        def fee_checkbox_clicked(index):
            if self.lst_variables_fee[index].get() == "":
                self.lst_variables_fee[0].set("")
            else:
                all_checked = all(self.lst_variables_fee[i].get() != "" for i in range(1, len(lst_fee_name)))
                if all_checked:
                    self.lst_variables_fee[0].set(lst_fee_name[0])
            # Pass self.merge_radios dictionary
            enable_extra_options(self.lst_variables_fee, self.merge_radios, self.var_Mem)

        # Create checkboxes using grid
        for i, fee_name in enumerate(lst_fee_name):
            str_variable = tk.StringVar()
            command = toggle_all_fees if i == 0 else lambda idx=i: fee_checkbox_clicked(idx)            
            fee_cb = ttk.Checkbutton(
                fee_content, text=fee_name, variable=str_variable,
                onvalue=fee_name, offvalue="", command=command,
                style="PrimaryCard.TCheckbutton" # Apply card style
            )
            fee_cb.grid(row=i, column=0, sticky="w", pady=1, padx=5)
            self.lst_variables_fee.append(str_variable)


    def _create_memory_type(self, parent):
        """Creates the Memory Type selection card."""
        mem_card = SecondaryCard(parent, title="Memory Type", padding=5)
        mem_card.grid(row=0, column=1, sticky="nsew", padx=(3, 0))
        
        mem_content = mem_card.get_content_frame()
        mem_content.columnconfigure(0, weight=1) # Configure grid inside card
        lst_memory_types = ["RAM", "Flash", "RAM and Flash"]
        for i, mem_type in enumerate(lst_memory_types):
            mem_radio = ttk.Radiobutton(
                mem_content, text=mem_type, variable=self.var_Mem, value=mem_type,                
                # Pass self.merge_radios dictionary
                command=lambda: enable_extra_options(self.lst_variables_fee, self.merge_radios, self.var_Mem),
                style="SecondaryCard.TRadiobutton" # Apply card style
            )
            mem_radio.grid(row=i, column=0, sticky="w", padx=5, pady=1)
        self.var_Mem.set(lst_memory_types[0]) # Set default


    def _create_format_selection(self, parent):
        """Creates the Memory File Format selection card."""
        format_card = AccentCard(parent, title="Memory File Format", padding=5)
        format_card.grid(row=1, column=0, sticky="ew", pady=5)
        
        format_content = format_card.get_content_frame()
        format_content.columnconfigure(0, weight=1) # Configure grid inside card
        lst_file_formats = ["Single TC", "Combined TCs"]
        for i, option in enumerate(lst_file_formats):            
            format_radio = ttk.Radiobutton(
                format_content, text=option, variable=self.var_format, value=option,
                style="AccentCard.TRadiobutton" # Apply card style
            )
            format_radio.grid(row=i, column=0, sticky="w", padx=5, pady=1)
        self.var_format.set(lst_file_formats[0]) # Set default


    def _create_icid_config(self, parent):
        """Creates the ICID Configuration card."""
        icid_card = SuccessCard(parent, title="ICID Configuration", padding=5)
        icid_card.grid(row=2, column=0, sticky="ew", pady=5)
        
        icid_content = icid_card.get_content_frame()
        # Use grid for labels and entries
        icid_content.columnconfigure(1, weight=1) # Allow entry to expand slightly        
        ttk.Label(icid_content, text="ICID:", style="SuccessCard.TLabel").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.input_icid = ttk.Entry(icid_content, width=8)
        self.input_icid.grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        self.input_icid.insert(0, "1")
        
        ttk.Label(icid_content, text="Ver:", style="SuccessCard.TLabel").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.input_icid_ver = ttk.Entry(icid_content, width=8)
        self.input_icid_ver.grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        self.input_icid_ver.insert(0, "1")


    def _create_tc_selection(self, parent):
        """Creates the TC Selection card."""
        tc_card = PrimaryCard(parent, title="TC Selection", padding=5) 
        tc_card.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        
        tc_content = tc_card.get_content_frame()
        tc_content.columnconfigure(0, weight=1) # Configure grid inside card
        lst_tc_name = config.CONFIG_INSTRUMENT["LI"]["tc_name"]
        self.lst_variables_tc = []
        for i, tc_name in enumerate(lst_tc_name):
            str_variable = tk.StringVar()
            tc_cb = ttk.Checkbutton(
                tc_content, text=tc_name, variable=str_variable,
                onvalue=tc_name, offvalue="",
                style="PrimaryCard.TCheckbutton" # Apply card style
            )
            tc_cb.grid(row=i, column=0, sticky="w", pady=1, padx=5)
            self.lst_variables_tc.append(str_variable)


    def _create_merge_options(self, parent):
        """Creates the Memory File Merge options card."""
        merge_card = SecondaryCard(parent, title="Memory File Merge (If multiple FEE & RAM)", padding=5)
        merge_card.grid(row=1, column=0, sticky="ew", pady=5)
        
        merge_content = merge_card.get_content_frame()
        merge_content.columnconfigure(0, weight=1) # Configure grid inside card
        lst_options = ["FEE Single", "FEE Merge"]
        # self.merge_radios initialized in __init__

        for i, option in enumerate(lst_options):            
            merge_radio = ttk.Radiobutton(
                merge_content, text=option, variable=self.var_Mer,
                value=option, state="disabled", # Initially disabled
                style="SecondaryCard.TRadiobutton" # Apply card style
            )
            merge_radio.grid(row=i, column=0, sticky="w", pady=1, padx=5)
            self.merge_radios[option] = merge_radio # Store reference
        # Initialize merge option based on initial state
        self.var_Mer.set(lst_options[0])
        # Initial state set by call in create_widgets after all widgets exist


    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""        
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=1)

        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5) # Use row 1 for buttons

        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )


    def _get_selected_check_button(self, variables: List[tk.StringVar]) -> List[str]:
        """Helper to get selected values from a list of checkbutton variables."""
        return [var.get() for var in variables if var.get()]


    def execute(self):
        """Validate user inputs before proceeding."""
        if self.input_icid is None or self.input_icid_ver is None:
            logger.error("Execute called before widgets created in LI BASIC_CONF_GUI")
            basics.pop_up_message("Error", "GUI not fully initialized.", "error")
            return False

        # Get selected values
        selected_fees = self._get_selected_check_button(self.lst_variables_fee)
        selected_tcs = self._get_selected_check_button(self.lst_variables_tc)
        mem_type = self.var_Mem.get()
        file_format = self.var_format.get()
        merge_option = self.var_Mer.get()
        icid_val = self.input_icid.get()
        icid_ver_val = self.input_icid_ver.get()

        # --- Input Validation ---
        if not selected_fees:
            raise ValueError("No FEE selected.")
        if not selected_tcs:
            raise ValueError("No TC selected.")
        if not mem_type:
            raise ValueError("Memory Type not selected.") # Should not happen
        if not file_format:
            raise ValueError("Memory File Format not selected.") # Should not happen

        if not icid_val:
            raise ValueError("ICID cannot be empty.")
        icid_int = int(icid_val)

        if not icid_ver_val:
            raise ValueError("ICID Version cannot be empty.")
        icid_ver_int = int(icid_ver_val)

        # Check merge option validity based on selections
        multiple_fees = len(selected_fees) > 1 or "ALL FEEs" in selected_fees
        is_ram = mem_type == "RAM" or mem_type == "RAM and Flash"
        merge_should_be_enabled = multiple_fees and is_ram
        merge_is_enabled = self.merge_radios["FEE Merge"].cget("state") == tk.NORMAL

        if merge_should_be_enabled and not merge_option:
             raise ValueError("Merge option must be selected for multiple FEEs and RAM.")
        # Only consider merge option if it's relevant/enabled
        actual_merge_option = merge_option if merge_is_enabled else None

        # Handle "All FEEs" selection
        if "ALL FEEs" in selected_fees:
            fees_to_process = [f"FEE {i}" for i in range(1, 5)]
        else:
            fees_to_process = selected_fees

        # --- Execute Generation ---
        self.update_idletasks()
        self.act_params.icid = icid_int
        self.act_params.icid_ver = icid_ver_int
        self.act_params.mem_type = mem_type
        self.act_params.file_format = file_format
        self.act_params.merge_option = actual_merge_option # Use validated merge option

        functions.generate_outputs(
            act_params=self.act_params,
            lst_fee=fees_to_process,
            lst_tc=selected_tcs
        )

        self.update_status("LI Basic Configuration generated successfully")
        basics.pop_up_message("Success", "LI Basic Configuration generated successfully.", "info")


    def back(self):
        """Handle back navigation"""
        self.app.back()
