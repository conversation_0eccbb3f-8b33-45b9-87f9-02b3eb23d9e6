{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d6a214b5-a6d5-4a02-84f0-87bb8f4ae0de", "metadata": {}, "outputs": [], "source": ["from hexdump import hexdump\n", "from icecream import ic"]}, {"cell_type": "code", "execution_count": 2, "id": "d608e95d-0ba3-4d9c-8ad6-7920401fa61a", "metadata": {}, "outputs": [], "source": ["def isochecksum(msg: bytes) -> bytes:\n", "    \"\"\"Calculates a 16-bit checksum as per Annex A.2 of [ECSS-E-70-41A].\n", "    \n", "    The returned bytes object contains the byte CK1 at index 0 and the byte CK2 at index 1.\"\"\"\n", "    \n", "    c0 = 0\n", "    c1 = 0\n", "    for byte in msg:\n", "        c0 = (c0 + byte) % 255\n", "        c1 = (c1 + c0) % 255\n", "    ck1 = -(c0 + c1) % 255 or 255\n", "    ck2 = c1 or 255\n", "    return bytes([ck1, ck2])\n", "\n", "assert isochecksum(b'\\x00\\x00') == b'\\xFF\\xFF'\n", "assert isochecksum(b'\\x00\\x00\\x00') == b'\\xFF\\xFF'\n", "assert isochecksum(b'\\xAB\\xCD\\xEF\\x01') == b'\\x9C\\xF8'\n", "assert isochecksum(b'\\x14\\x56\\xF8\\x9A\\x00\\x01') == b'\\x24\\xDC'\n", "\n", "def verifyisochecksum(msg: bytes, checksum: bytes) -> bool:\n", "    \"\"\"Checks if the message `msg` with checksum `checksum` appears to be vaild.\n", "    \n", "    Returns True if no errors have been detected and False if errors have been detected.\n", "    The expected type of checksum is the one output by `isochecksum`.\"\"\"\n", "    \n", "    if (checksum[0] == 0) ^ (checksum[1] == 0):\n", "        return False\n", "\n", "    c0 = 0\n", "    c1 = 0\n", "    for byte in (msg + checksum):\n", "        c0 = (c0 + byte) % 255\n", "        c1 = (c1 + c0) % 255\n", "    return c0 == 0 and c1 == 0\n", "\n", "assert verifyisochecksum(b'\\x00\\x00', b'\\xFF\\xFF')\n", "assert verifyisochecksum(b'\\x00\\x00\\x00', b'\\xFF\\xFF')\n", "assert verifyisochecksum(b'\\x00\\x00\\x00', b'\\xFF\\xFE') == False\n", "assert verifyisochecksum(b'\\xAB\\xCD\\xEF\\x01', b'\\x9C\\xF8')\n", "assert verifyisochecksum(b'\\x14\\x56\\xF8\\x9A\\x00\\x01', b'\\x24\\xDC')\n", "assert verifyisochecksum(b'\\x14\\x56\\xF8\\x9A\\x00\\x01', b'\\x24\\xDD') == False"]}, {"cell_type": "code", "execution_count": 3, "id": "24d0de8e-56eb-40e7-97a2-895d2b8a9315", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: crcmod in /luser/fehmann/mambaforge/envs/irs-spt/lib/python3.10/site-packages (1.7)\n"]}], "source": ["! pip install crcmod"]}, {"cell_type": "code", "execution_count": 4, "id": "c24344c2-d63c-41aa-ab01-ea40c4bc3279", "metadata": {}, "outputs": [], "source": ["def bytecrc(data, syndrome):\n", "    \"\"\"Incorporates the new byte `data` into the ongoing calculation of the CRC checksum in `syndrome`\"\"\"\n", "    for _ in range(8):\n", "        if ((data & 0x80) ^ ((syndrome & 0x8000) >> 8)):\n", "            syndrome = ((syndrome << 1) ^ 0x1021) & 0xffff\n", "        else:\n", "            syndrome = (syndrome << 1) & 0xffff\n", "        data = data << 1\n", "    return syndrome\n", "\n", "def crc(data: bytes) -> bytes:\n", "    \"\"\"Calculates a 16-bit CRC checksum as per Annex A.1 of [ECSS-E-70-41A]\"\"\"\n", "    syndrome = 0xffff\n", "    for byte in data:\n", "        syndrome = bytecrc(byte, syndrome)\n", "    return syndrome.to_bytes(2, 'big')\n", "\n", "from crcmod import mkCrcFun\n", "_crc_fun = mkCrcFun(poly=0x11021, rev=False, initCrc=0xffff)\n", "def crc_using_crcmod(data: bytes) -> bytes:\n", "    \"\"\"Calculates the 16-bit CRC checksum defined in Annex A.1 of [ECSS-E-70-41A] using a third-party library and\n", "    identifying the CRC by its polynomial and initializer\"\"\"\n", "    return _crc_fun(data).to_bytes(2, 'big')\n", "\n", "crc = crc_using_crcmod\n", "\n", "assert crc(bytes.fromhex('00 00')) == bytes.fromhex('1d 0f')\n", "assert crc(bytes.fromhex('00 00 00')) == bytes.fromhex('cc 9c')\n", "assert crc(bytes.fromhex('ab cd ef 01')) == bytes.fromhex('04 a2')\n", "assert crc(bytes.fromhex('14 56 f8 9a 00 01')) == bytes.fromhex('7f d5')"]}, {"cell_type": "code", "execution_count": 5, "id": "a04efaad-b747-4098-aec8-43fc98ed3975", "metadata": {}, "outputs": [], "source": ["with open('packet_raw_data.bin', 'rb') as f:\n", "    packet_raw_data = f.read()\n", "\n", "with open('packet_header.bin', 'rb') as f:\n", "    packet_header = f.read()"]}, {"cell_type": "code", "execution_count": 6, "id": "eba875cd-41f1-4160-b035-869dee83fd97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["00000000: CA 1B                                             ..\n"]}], "source": ["# hexdump(packet_header)\n", "hexdump(crc(packet_raw_data[:-2]))"]}, {"cell_type": "code", "execution_count": 7, "id": "d7d5b24d-cab5-4771-9c69-aa094a317b1d", "metadata": {}, "outputs": [{"data": {"text/plain": ["51739"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["int.from_bytes(crc(packet_raw_data[:-2]), 'big')"]}, {"cell_type": "code", "execution_count": 8, "id": "78b89a7f-e489-4f5b-ac01-987de2005ed5", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["int.from_bytes(crc(packet_raw_data), 'little')"]}, {"cell_type": "markdown", "id": "04c24bc5-b3b4-4415-bd7f-dbc5c727798b", "metadata": {}, "source": ["# Example from <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 19, "id": "927d1f32-6fd8-4b30-980e-2245feb5545d", "metadata": {}, "outputs": [], "source": ["assert isochecksum(bytes.fromhex('01 00 01 01 03 01 00 02 00 01 02 80 00 00 00')) == bytes.fromhex('FD 75')\n", "assert isochecksum(bytes.fromhex('02 00 01 01 03 01 00 02 00 01 02 80 00 00 00')) == bytes.fromhex('ED 84')"]}, {"cell_type": "code", "execution_count": null, "id": "2269ba0c-2665-4fc2-854f-83580712a671", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}