#!/usr/bin/env python3
"""
XLDT Reader Demonstration Script
===============================

This script demonstrates the capabilities of the XLDT reader package
by analyzing all the sample XLDT files in the inputs directory.
"""

import os
import sys
from xldt_reader_standalone import XLDTReader


def main():
    """Main demonstration function."""
    print("🚀 XLDT Reader Package Demonstration")
    print("=" * 50)
    
    # Configuration file path
    config_path = "config/IRS/IRS_SL_Conf.csv"
    
    # Initialize reader
    print(f"📋 Initializing XLDT Reader with config: {config_path}")
    reader = XLDTReader(config_path=config_path)
    print("✅ Reader initialized successfully\n")
    
    # Find all XLDT files in inputs directory
    inputs_dir = "inputs"
    xldt_files = [f for f in os.listdir(inputs_dir) if f.endswith('.xldt')]
    
    print(f"📁 Found {len(xldt_files)} XLDT files in {inputs_dir}/")
    print("-" * 30)
    
    # Analyze each file
    for i, filename in enumerate(xldt_files, 1):
        file_path = os.path.join(inputs_dir, filename)
        
        print(f"\n{i}. Analyzing: {filename}")
        print("   " + "=" * (len(filename) + 12))
        
        try:
            # Read the file
            data = reader.read_xldt_file(file_path)
            
            # Display basic info
            header = data['header']
            sections = data['sections']
            file_info = data['file_info']
            
            print(f"   📊 File Size: {file_info['file_size']:,} bytes")
            print(f"   🔢 Format ID: {header.format_id}")
            print(f"   🎯 MM Slot: {header.mm_slot}")
            print(f"   📦 Sections: {len(sections)}")
            
            # Show section summary
            print("   📋 Section Details:")
            for section_name, section in sections.items():
                if hasattr(section, 'length'):
                    print(f"      • {section_name}: {section.length} bytes")
                else:
                    print(f"      • {section_name}: {type(section).__name__}")
            
            # Validate CRC if available
            if 'CRC' in sections:
                try:
                    is_valid = reader.validate_crc(data['raw_hex'], sections['CRC'])
                    status = "✅ VALID" if is_valid else "❌ INVALID"
                    print(f"   🔐 CRC Status: {status}")
                except Exception as e:
                    print(f"   🔐 CRC Status: ⚠️ Error - {e}")
            
            # Show first few bytes of raw data
            raw_hex = data['raw_hex']
            if len(raw_hex) > 32:
                preview = raw_hex[:32] + "..."
            else:
                preview = raw_hex
            print(f"   🔍 Raw Data: {preview}")
            
        except Exception as e:
            print(f"   ❌ Error reading file: {e}")
    
    print(f"\n🎉 Analysis complete! Processed {len(xldt_files)} files.")
    print("\n💡 Usage Tips:")
    print("   • Use --config to specify configuration file")
    print("   • Use --format detailed for complete analysis")
    print("   • Use --validate-crc to check data integrity")
    print("   • Use --output to save results to file")
    
    print("\n📚 Example Commands:")
    print("   python xldt_cli_standalone.py inputs/ScanLaw_Central_Summer_16384.xldt --config config/IRS/IRS_SL_Conf.csv")
    print("   python xldt_cli_standalone.py inputs/ScanLaw_East_Summer_16386.xldt --format detailed --validate-crc")
    print("   python xldt_cli_standalone.py inputs/ScanLaw_West_Winter_1.xldt --output analysis.json --format json")


def compare_files():
    """Compare different XLDT files to show variations."""
    print("\n🔍 File Comparison Analysis")
    print("=" * 30)
    
    config_path = "config/IRS/IRS_SL_Conf.csv"
    reader = XLDTReader(config_path=config_path)
    
    inputs_dir = "inputs"
    xldt_files = [f for f in os.listdir(inputs_dir) if f.endswith('.xldt')][:3]  # Limit to first 3
    
    comparison_data = []
    
    for filename in xldt_files:
        file_path = os.path.join(inputs_dir, filename)
        try:
            data = reader.read_xldt_file(file_path)
            comparison_data.append({
                'filename': filename,
                'size': data['file_info']['file_size'],
                'format_id': data['header'].format_id,
                'mm_slot': data['header'].mm_slot,
                'sections': len(data['sections'])
            })
        except Exception as e:
            print(f"Error reading {filename}: {e}")
    
    # Display comparison table
    if comparison_data:
        print(f"{'Filename':<35} {'Size':<8} {'Format':<8} {'MM Slot':<8} {'Sections':<8}")
        print("-" * 75)
        for item in comparison_data:
            print(f"{item['filename']:<35} {item['size']:<8} {item['format_id']:<8} {item['mm_slot']:<8} {item['sections']:<8}")


if __name__ == "__main__":
    # Check if we're in the right directory
    if not os.path.exists("xldt_reader_standalone.py"):
        print("❌ Error: Please run this script from the xldt_reader directory")
        print("   cd xldt_reader")
        print("   python demo.py")
        sys.exit(1)
    
    main()
    compare_files()
