# Standard library imports
from src.algorithms.uvn_functions.utils.bit_dec_converter import \
    convert_dec_to_bit, convert_bit_to_dec

class TestBitDecimalConverter:
   def test_convert_dec_to_bit(self):
        """Unit test to convert decimal to hexadecimal """

        assert(convert_dec_to_bit(val_dec=False,len_bit=16,type='boolean',flag_hex=True)=='0000')
        assert(convert_dec_to_bit(val_dec=0,len_bit=16,type='enumerated',flag_hex=True)=='0000')
        assert(convert_dec_to_bit(val_dec=0,len_bit=16,type='integer',flag_hex=True)=='0000')
        assert(convert_dec_to_bit(val_dec=0,len_bit=16,type='unsigned integer',flag_hex=True)=='0000')
        assert(convert_dec_to_bit(val_dec=0,len_bit=16,type='signed integer',flag_hex=True)=='0000')
        assert(convert_dec_to_bit(val_dec=0.0,type='float',flag_hex=True)=='00000000')
        assert(convert_dec_to_bit(val_dec=0.0,type='double',flag_hex=True)=='0000000000000000')

        assert(convert_dec_to_bit(val_dec=False,len_bit=16,type='boolean',flag_hex=False)=='0000000000000000')
        assert(convert_dec_to_bit(val_dec=0,len_bit=16,type='enumerated',flag_hex=False)=='0000000000000000')
        assert(convert_dec_to_bit(val_dec=0,len_bit=16,type='integer',flag_hex=False)=='0000000000000000')
        assert(convert_dec_to_bit(val_dec=0,len_bit=16,type='unsigned integer',flag_hex=False)=='0000000000000000')
        assert(convert_dec_to_bit(val_dec=0,len_bit=16,type='signed integer',flag_hex=False)=='0000000000000000')
        assert(convert_dec_to_bit(val_dec=0.0,type='float',flag_hex=False)=='00000000000000000000000000000000')
        assert(convert_dec_to_bit(val_dec=0.0,type='double',flag_hex=False)=='0000000000000000000000000000000000000000000000000000000000000000')

   def test_convert_bit_to_dec(self):
        """Unit test to convert of hexadecimal to dec """

        assert(convert_bit_to_dec(dat_bit='0000',type='boolean',flag_hex=True)==False)
        assert(convert_bit_to_dec(dat_bit='0000',type='enumerated',flag_hex=True)==0)
        assert(convert_bit_to_dec(dat_bit='0000',type='integer',flag_hex=True)==0)
        assert(convert_bit_to_dec(dat_bit='0000',type='unsigned integer',flag_hex=True)==0)
        assert(convert_bit_to_dec(dat_bit='0000',type='signed integer',flag_hex=True)==0)
        assert(convert_bit_to_dec(dat_bit='00000000',type='float',flag_hex=True)==0.0)
        assert(convert_bit_to_dec(dat_bit='0000000000000000',type='double',flag_hex=True)==0.0)

        assert(convert_bit_to_dec(dat_bit='0000000000000000',type='boolean',flag_hex=False)==False)
        assert(convert_bit_to_dec(dat_bit='0000000000000000',type='enumerated',flag_hex=False)==0)
        assert(convert_bit_to_dec(dat_bit='0000000000000000',type='integer',flag_hex=False)==0)
        assert(convert_bit_to_dec(dat_bit='0000000000000000',type='unsigned integer',flag_hex=False)==0)
        assert(convert_bit_to_dec(dat_bit='0000000000000000',type='signed integer',flag_hex=False)==0)
        assert(convert_bit_to_dec(dat_bit='00000000000000000000000000000000',type='float',flag_hex=False)==0.0)
        assert(convert_bit_to_dec(dat_bit='0000000000000000000000000000000000000000000000000000000000000000',type='double',flag_hex=False)==0.0)

   def test_convert_list_dec_to_bit(self):
        """Unit test to convert of list of decimals to hexadecimal """

        lst_test_dec=[
            {'flag_hex':False,'type':'boolean','len_bit':1,'val_dec':False},
            {'flag_hex':False,'type':'boolean','len_bit':1,'val_dec':True},
            {'flag_hex':False,'type':'enumerated','len_bit':1,'val_dec':0},
            {'flag_hex':False,'type':'enumerated','len_bit':1,'val_dec':1},
            {'flag_hex':False,'type':'integer','len_bit':1,'val_dec':0},
            {'flag_hex':False,'type':'integer','len_bit':1,'val_dec':1},
            {'flag_hex':False,'type':'unsigned integer','len_bit':1,'val_dec':0},
            {'flag_hex':False,'type':'unsigned integer','len_bit':1,'val_dec':1},
            {'flag_hex':False,'type':'signed integer','len_bit':2,'val_dec':0},
            {'flag_hex':False,'type':'signed integer','len_bit':2,'val_dec':1},
            {'flag_hex':False,'type':'float','len_bit':None,'val_dec':0.123456789123456789123456789},
            {'flag_hex':False,'type':'double','len_bit':None,'val_dec':0.123456789123456789123456789},
            {'flag_hex':False,'type':'float','len_bit':None,'val_dec':-0.123456789123456789123456789},
            {'flag_hex':False,'type':'double','len_bit':None,'val_dec':-0.123456789123456789123456789},

            {'flag_hex':True,'type':'boolean','len_bit':8,'val_dec':False},
            {'flag_hex':True,'type':'boolean','len_bit':8,'val_dec':True},
            {'flag_hex':True,'type':'enumerated','len_bit':8,'val_dec':0},
            {'flag_hex':True,'type':'enumerated','len_bit':8,'val_dec':1},
            {'flag_hex':True,'type':'integer','len_bit':8,'val_dec':0},
            {'flag_hex':True,'type':'integer','len_bit':8,'val_dec':1},
            {'flag_hex':True,'type':'unsigned integer','len_bit':8,'val_dec':0},
            {'flag_hex':True,'type':'unsigned integer','len_bit':8,'val_dec':1},
            {'flag_hex':True,'type':'signed integer','len_bit':8,'val_dec':0},
            {'flag_hex':True,'type':'signed integer','len_bit':8,'val_dec':1},
            {'flag_hex':True,'type':'float','len_bit':None,'val_dec':0.123456789123456789123456789},
            {'flag_hex':True,'type':'double','len_bit':None,'val_dec':0.123456789123456789123456789},
            {'flag_hex':True,'type':'float','len_bit':None,'val_dec':-0.123456789123456789123456789},
            {'flag_hex':True,'type':'double','len_bit':None,'val_dec':-0.123456789123456789123456789},
        ]

        for test in lst_test_dec:
            if(test["type"]=='float'):
                val_dec_=convert_bit_to_dec(dat_bit=convert_dec_to_bit(val_dec=test['val_dec'],len_bit=test['len_bit'],type=test['type'],flag_hex=test['flag_hex']),type=test['type'],flag_hex=test['flag_hex'])
                assert(float("{:.7e}".format(val_dec_))==float("{:.7e}".format(test['val_dec'])))#floats have at least 7 significant decimals
            elif(test["type"]=='double'):
                val_dec_=convert_bit_to_dec(dat_bit=convert_dec_to_bit(val_dec=test['val_dec'],len_bit=test['len_bit'],type=test['type'],flag_hex=test['flag_hex']),type=test['type'],flag_hex=test['flag_hex'])
                assert(float("{:.15e}".format(val_dec_))==float("{:.15e}".format(test['val_dec'])))#doubles have at least 15 significant decimals
            else:
                assert(convert_bit_to_dec(dat_bit=convert_dec_to_bit(val_dec=test['val_dec'],len_bit=test['len_bit'],type=test['type'],flag_hex=test['flag_hex']),type=test['type'],flag_hex=test['flag_hex'])==test['val_dec'])

   def test_convert_list_bit_to_dec(self):
        """Unit test to convert of list of hexadecimals to decimal """

        lst_test_bit=[
            {'flag_hex':False,'type':'boolean','len_bit':1,'val_bit':'0'},
            {'flag_hex':False,'type':'boolean','len_bit':1,'val_bit':'1'},
            {'flag_hex':False,'type':'enumerated','len_bit':1,'val_bit':'0'},
            {'flag_hex':False,'type':'enumerated','len_bit':1,'val_bit':'1'},
            {'flag_hex':False,'type':'integer','len_bit':1,'val_bit':'0'},
            {'flag_hex':False,'type':'integer','len_bit':1,'val_bit':'1'},
            {'flag_hex':False,'type':'unsigned integer','len_bit':1,'val_bit':'0'},
            {'flag_hex':False,'type':'unsigned integer','len_bit':1,'val_bit':'1'},
            {'flag_hex':False,'type':'signed integer','len_bit':2,'val_bit':'00'},
            {'flag_hex':False,'type':'signed integer','len_bit':2,'val_bit':'11'},
            {'flag_hex':False,'type':'float','len_bit':32,'val_bit':'00000000000000000000000000000000'},
            {'flag_hex':False,'type':'float','len_bit':32,'val_bit':'00111111111111111111111111111111'},
            {'flag_hex':False,'type':'double','len_bit':64,'val_bit':'0000000000000000000000000000000000000000000000000000000000000000'},
            {'flag_hex':False,'type':'double','len_bit':64,'val_bit':'0011111111111111111111111111111111111111111111111111111111111111'},

            {'flag_hex':True,'type':'boolean','len_bit':8,'val_bit':'00'},
            {'flag_hex':True,'type':'boolean','len_bit':8,'val_bit':'01'},
            {'flag_hex':True,'type':'enumerated','len_bit':8,'val_bit':'00'},
            {'flag_hex':True,'type':'enumerated','len_bit':8,'val_bit':'ff'},
            {'flag_hex':True,'type':'integer','len_bit':8,'val_bit':'00'},
            {'flag_hex':True,'type':'integer','len_bit':8,'val_bit':'ff'},
            {'flag_hex':True,'type':'unsigned integer','len_bit':8,'val_bit':'00'},
            {'flag_hex':True,'type':'unsigned integer','len_bit':8,'val_bit':'ff'},
            {'flag_hex':True,'type':'signed integer','len_bit':8,'val_bit':'00'},
            {'flag_hex':True,'type':'signed integer','len_bit':8,'val_bit':'ff'},
            {'flag_hex':True,'type':'float','len_bit':32,'val_bit':'00000000'},
            {'flag_hex':True,'type':'float','len_bit':32,'val_bit':'11111111'},
            {'flag_hex':True,'type':'double','len_bit':64,'val_bit':'0000000000000000'},
            {'flag_hex':True,'type':'double','len_bit':64,'val_bit':'1111111111111111'},
        ]

        for test in lst_test_bit:
            val_dec_=convert_bit_to_dec(dat_bit=test['val_bit'],type=test['type'],flag_hex=test['flag_hex'])
            assert(convert_dec_to_bit(val_dec=val_dec_,len_bit=test['len_bit'],type=test['type'],flag_hex=test['flag_hex'])==test['val_bit'])
