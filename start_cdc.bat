@echo off
setlocal

REM --- Configuration ---
set "VENV_PATH=venv"         REM Path to your virtual environment directory
set "PYTHON_EXECUTABLE=%VENV_PATH%\Scripts\python.exe" REM Path to the Python executable in the venv
set "YOUR_SCRIPT=CDC.py"   REM Name of your Python script to run
set "SCRIPT_ARGS="          REM Optional arguments to pass to your Python script

REM --- Ensure Virtual Environment Exists ---
if not exist "%VENV_PATH%" (
    echo Virtual environment folder not found. Attempting to unzip venv.zip...
    if exist "venv.zip" (
        powershell -Command "Expand-Archive -Path 'venv.zip' -DestinationPath '%VENV_PATH%' -Force"
        if exist "%VENV_PATH%" (
            echo venv.zip extracted successfully.
        ) else (
            echo Error: Failed to extract venv.zip.
            goto :eof
        )
    ) else (
        echo Error: venv.zip not found. Cannot create virtual environment.
        goto :eof
    )
)

REM --- Activate Virtual Environment (if necessary) ---
if exist "%VENV_PATH%\Scripts\activate.bat" (
    call "%VENV_PATH%\Scripts\activate.bat"
    echo Virtual environment activated.
) else (
    echo Error: Virtual environment not found at "%VENV_PATH%".
    goto :eof
)

REM --- Run the Python script ---
if exist "%PYTHON_EXECUTABLE%" (
    echo Running Python script: "%PYTHON_EXECUTABLE%" "%YOUR_SCRIPT%" %SCRIPT_ARGS%
    "%PYTHON_EXECUTABLE%" "%YOUR_SCRIPT%" %SCRIPT_ARGS%
) else (
    echo Error: Python executable not found in the virtual environment.
)

REM --- Deactivate Virtual Environment (optional, but recommended) ---
if exist "%VENV_PATH%\Scripts\deactivate.bat" (
    call "%VENV_PATH%\Scripts\deactivate.bat"
    echo Virtual environment deactivated.
)

endlocal
pause