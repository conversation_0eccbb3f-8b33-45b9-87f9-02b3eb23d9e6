# Standard library imports
import os
import datetime

# Third-party imports

from lxml import etree
import io
from src.utils.data_cache import cache

# Local imports
from src.utils.import_utils import basics, config
from src.logger_wrapper import logger


def check_xml(str_path: str, str_path_schema: str, bln_test: bool = False):
    """check the xml and logging errors if any"""

    bln_xml_valid, df_results = basics.xml_validation_schema(
        str_path,
        str_path_schema,
    )

    str_file_name = os.path.basename(str_path)

    if bln_xml_valid:
        logger.info(f"Success: Generated PAF {str_file_name} complies to XML schema.")
    else:
        for int_step in range(len(df_results)):
            error_entry = (
                f"Element: {df_results['Element'][int_step]}"
                + "\n"
                + f"Error: {df_results['Error'][int_step]}"
            )

            logger.error(error_entry)

            # Skip if is a pytest
            if not bln_test:
                basics.pop_up_message(
                    "Error",
                    f"Error: Generated PAF {str_file_name} DOES NOT comply to XML schema. DO NOT push to Git repository. Check results are:"
                    + "\n"
                    + error_entry,
                    "error",
                )


# UPDATE TEMPLATE PAF FILES WITH THE VALUES REQUIRED FOR PARAMETERS DEFINED IN VSM
def PAF_Update(
    str_path_file,
    Dictionary,
    Output_Folder,
    sc,
    fee_id,
    instrument,
    identifier,
    Which_MM_ID,
    channel,
    calibrations,
    bln_test: bool = False,
    paf_type: str = "standard",
):
    str_path_template = str_path_file
    str_author = "OPIT"
    str_file_name = os.path.basename(str_path_file)
    str_comment = f"{os.path.basename(str_path_file)} updated by CDC for {sc}"
    # Use correct satellite name format based on instrument
    if instrument == "IRS":
        str_object_name = f"MTG-S{sc[-1]}"
        str_object_id = f"MTS{sc[-1]}"
    else:
        str_object_name = f"MTG-I{sc[-1]}"
        str_object_id = f"MTI{sc[-1]}"

    # Define output filename as per documentation
    str_time_now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")

    if fee_id == "":
        if instrument == "IRS":
            str_filename = f"PAF_MTS{sc[-1]}+{instrument}_{paf_type}-ID{identifier}{channel}_{str_time_now}.xml"
        else:
            str_filename = f"PAF_MTI{sc[-1]}+{instrument}_{str_file_name[4:-4]}{Which_MM_ID}-ID{identifier}{channel}_{str_time_now}.xml"
    else:
        if fee_id[-1] == "s":
            if fee_id[0] == "L":
                str_filename = f"PAF_MTI{sc[-1]}+{instrument}_{str_time_now}_{str_file_name[4:-4]}-{fee_id}-ID{identifier}.xml"
            else:
                str_filename = f"PAF_MTI{sc[-1]}+{instrument}_{str_time_now}_{str_file_name[4:-4]}-FEE{fee_id[-1]}-ID{identifier}.xml"

        else:
            if fee_id[0] == "L":
                str_filename = f"PAF_MTI{sc[-1]}+{instrument}_{str_time_now}_{str_file_name[4:-4]}-{fee_id}-ID{identifier}.xml"
            else:
                str_filename = f"PAF_MTI{sc[-1]}+{instrument}_{str_time_now}_{str_file_name[4:-4]}-FEE{fee_id[-1]}-ID{identifier}.xml"

    filenamexml = os.path.join(Output_Folder, str_filename)

    # initialize Variables
    rows_temp = []
    rows_new = []

    # Open template PAF xml file from in-memory cache
    file_bytes = cache.get(str_path_template)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {str_path_template}")
    tree = etree.parse(io.BytesIO(file_bytes))

    # Get whole tree
    root = tree.getroot()

    # Update Header
    header = root.findall(".//header")[0]
    header.find("CREATION_DATE").text = (
        datetime.datetime.now().strftime("%Y-%jT%H:%M:%S.%f")[0:-3] + "Z"
    )
    header.find("ORIGINATOR").text = str_author
    header.find("COMMENT").text = str_comment

    # Update Metadata
    metadata = root.findall(".//metadata")[0]
    metadata.find("OBJECT_NAME").text = str_object_name
    metadata.find("OBJECT_ID").text = str_object_id

    # --- Set <date> field if present in Dictionary ---
    if "T_SAV_RS" in Dictionary:
        for date_elem in root.findall(".//date"):
            name_elem = date_elem.find("name")
            if name_elem is not None and name_elem.text == "T_SAV_RS":
                date_elem.find("value").text = Dictionary["T_SAV_RS"]
                logger.info(f"Setting T_SAV_RS in {str_file_name} to {Dictionary['T_SAV_RS']} from netCDF")

    # Update Parameters
    for parameter in root.findall(".//integer"):
        str_name = parameter.find("name").text
        if str_name in Dictionary:
            # HARDCODED, PAF SHOULD NOT CHANGE
            if "T_EXP" in str_name or "N_IMG" in str_name:
                if str_name[4] == "0":
                    int_element = int(str_name[3:5])
                else:
                    int_element = int(str_name[3])

                if int_element > int(calibrations):
                    parameter.getparent().remove(parameter)
                else:
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {int(Dictionary[str_name])} from netCDF"
                    )

                    parameter.find("value").text = str(int(Dictionary[str_name]))
            else:
                str_value = str(Dictionary[str_name])
                if str_value == "":
                    parameter.getparent().remove(parameter)
                # HARDCODED, PAF SHOULD NOT CHANGE 
                elif "T_EXP" in str_name or "N_IMG" in str_name:
                        if str_name[4] == "0":
                            int_element = int(str_name[3:5])
                        else:
                            int_element = int(str_name[3])
                        if int_element > int(calibrations):
                            parameter.getparent().remove(parameter)
                else:
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                    )
                    parameter.find("value").text = str_value


    lst_parameters = ["long", "double", "string", "boolean", "enumeration"]

    # Loop through all the parameter types to be modify
    for str_step in lst_parameters:
        for obj_parameter in root.findall(f".//{str_step}"):
            str_name = obj_parameter.find("name").text
            if str_name in Dictionary:
                if str_step == "enumeration":
                    dict_enum = {}
                    # Create dictionary of enumValues
                    for obj_enum in obj_parameter.findall(".//enumValue"):
                        dict_enum[obj_enum.find("value").text] = obj_enum.find(
                            "key"
                        ).text

                    key_to_lookup = str(Dictionary[str_name])
                    if key_to_lookup not in dict_enum:
                        logger.warning(f"Value '{key_to_lookup}' for '{str_name}' not in enum, using default '{list(dict_enum.keys())[0]}'")
                        str_value = dict_enum[list(dict_enum.keys())[0]]
                    else:
                        str_value = dict_enum[key_to_lookup]
                else:
                    str_value = str(Dictionary[str_name])

                if str_value == "":
                    obj_parameter.getparent().remove(obj_parameter)
                else:
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                    )

                    obj_parameter.find("value").text = str_value

    for parameter in root.findall(".//hexstring"):
        str_name = parameter.find("name").text
        if str_name in Dictionary:
            str_value = str(Dictionary[str_name])
            if str_value == "":
                parameter.getparent().remove(parameter)
            else:
                logger.info(f"Setting {str_name} in {str_file_name} to {str_value}")

                if (len(str_value) % 2) == 0:
                    parameter.find("value").text = str_value
                else:
                    parameter.find("value").text = f"0{str_value}"

    # Extra steps to keep the XML Declaration the brute force way (to avoid to add further modules)
    # Write the draft output - without XML Declaration
    os.makedirs(os.path.dirname(filenamexml), exist_ok=True)
    tree.write(filenamexml)

    # Read the template from in-memory cache
    file_bytes = cache.get(str_path_template)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {str_path_template}")
    with io.BytesIO(file_bytes) as obj_file:
        for row in obj_file:
            rows_temp.append(row.decode("utf-8"))

    XMLDeclaration = rows_temp[0]

    # Read the draft output
    with open(filenamexml, "r+") as obj_file:
        for row_new in obj_file:
            rows_new.append(row_new)

    # Put XML Declaration from template together with draft output
    total = [XMLDeclaration] + rows_new

    # Rewrite the output
    output = open(filenamexml, "w+b")
    output.close()

    for rows in total:
        output_final = open(filenamexml, "a")
        output_final.write(rows)
    output_final.close()

    # Lastly, verify the PAF against the xml schema
    check_xml(
        filenamexml,
        f"{config.CONFIG_VALUES['schema']}",
        bln_test,
    )


# UPDATE TEMPLATE PAF FILE FOR Simplified Scan Law WITH THE VALUES REQUIRED FOR PARAMETERS DEFINED IN VSM - FCI
# Specificities requiring a dedicated functions for simplicity
def PAF_Update_SSL(
    str_path_file,
    Dictionary,
    Output_Folder,
    sc,
    instrument,
    identifier,
    Which_MM_ID,
    bln_test: bool = False,
):
    str_author = "OPIT"
    str_object_name = f"MTG-I{sc[-1]}"
    str_object_id = f"MTI{sc[-1]}"
    str_file_name = os.path.basename(str_path_file)
    str_comment = f"{os.path.basename(str_path_file)} updated by CDC for {sc}"

    # Define output filename as per documentation
    str_time_now = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    str_filename = f"PAF_MTI{sc[-1]}+{instrument}_{str_time_now}_{str_file_name[4:-4]}{Which_MM_ID}-ID{identifier}.xml"

    filenamexml = os.path.join(Output_Folder, str_filename)
    # Open template PAF xml file from in-memory cache
    file_bytes = cache.get(str_path_file)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {str_path_file}")
    tree = etree.parse(io.BytesIO(file_bytes))
    # Get whole tree
    root = tree.getroot()

    # Update Header
    header = root.findall(".//header")[0]
    header.find("CREATION_DATE").text = (
        datetime.datetime.now().strftime("%Y-%jT%H:%M:%S.%f")[0:-3] + "Z"
    )
    header.find("ORIGINATOR").text = str_author
    header.find("COMMENT").text = str_comment

    # Update Metadata
    metadata = root.findall(".//metadata")[0]
    metadata.find("OBJECT_NAME").text = str_object_name
    metadata.find("OBJECT_ID").text = str_object_id

    # Update Parameters
    for obj_parameter in root.findall(".//integer"):
        str_name = obj_parameter.find("name").text
        if str_name in Dictionary:
            str_value = str(Dictionary[str_name])
            if str_value == "" or str_value == "4294967295":
                obj_parameter.getparent().remove(obj_parameter)
            else:
                logger.info(
                    f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                )

                obj_parameter.find("value").text = str_value

    for obj_parameter in root.findall(".//hexstring"):
        str_name = obj_parameter.find("name").text
        if str_name in Dictionary:
            str_value = str(Dictionary[str_name])
            if str_value == "":
                obj_parameter.getparent().remove(obj_parameter)
            else:
                logger.info(f"Setting {str_name} in {str_file_name} to {str_value}")

                if (len(Dictionary[str_name]) % 2) == 0:
                    obj_parameter.find("value").text = str_value
                else:
                    obj_parameter.find("value").text = f"0{str_value}"

    lst_parameters = ["long", "double", "string", "boolean", "enumeration"]

    # Loop through all the parameter types to be modify
    for str_step in lst_parameters:
        for obj_parameter in root.findall(f".//{str_step}"):
            str_name = obj_parameter.find("name").text
            if str_name in Dictionary:
                if str_step == "enumeration":
                    dict_enum = {}
                    # Create dictionary of enumValues
                    for obj_enum in obj_parameter.findall(".//enumValue"):
                        dict_enum[obj_enum.find("value").text] = obj_enum.find(
                            "key"
                        ).text

                    key_to_lookup = str(Dictionary[str_name])
                    if key_to_lookup not in dict_enum:
                        logger.warning(f"Value '{key_to_lookup}' for '{str_name}' not in enum, using default '{list(dict_enum.keys())[0]}'")
                        str_value = dict_enum[list(dict_enum.keys())[0]]
                    else:
                        str_value = dict_enum[key_to_lookup]
                else:
                    str_value = str(Dictionary[str_name])

                if str_value == "":
                    obj_parameter.getparent().remove(obj_parameter)
                else:
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                    )

                    obj_parameter.find("value").text = str_value

    # Extra steps to keep the XML Declaration the brute force way (to avoid to add further modules)
    # Write the draft output - without XML Declaration
    os.makedirs(os.path.dirname(filenamexml), exist_ok=True)
    tree.write(filenamexml)
    # Read the template
    rows_temp = []

    # Read the template from in-memory cache
    file_bytes = cache.get(str_path_file)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {str_path_file}")
    with io.BytesIO(file_bytes) as obj_file:
        for row in obj_file:
            rows_temp.append(row.decode("utf-8"))

    XMLDeclaration = rows_temp[0]

    # Read the draft output
    rows_new = []
    with open(filenamexml, "r+") as obj_file:
        for row_new in obj_file:
            rows_new.append(row_new)

    # Put XML Declaration from template together with draft output
    total = [XMLDeclaration] + rows_new
    # Rewrite the output
    output = open(filenamexml, "w+b")
    output.close()
    for rows in total:
        output_final = open(filenamexml, "a")
        output_final.write(rows)
    output_final.close()

    # Lastly, verify the PAF against the xml schema
    check_xml(
        filenamexml,
        f"{config.CONFIG_VALUES['schema']}",
        bln_test,
    )


def PAF_Update_FCI_REPSEQ(
    instrument: str,
    str_path_file: str,
    Dictionary,
    Output_Folder,
    sc,
    identifier,
    bln_test: bool = False,
):
    """UPDATE TEMPLATE PAF FILE FOR REPSEQ WITH THE VALUES REQUIRED FOR PARAMETERS DEFINED IN VSM - FCI
    Specificities requiring a dedicated functions for simplicity"""

    str_author = "OPIT"
    str_object_name = f"MTG-I{sc[-1]}"
    str_object_id = f"MTI{sc[-1]}"
    str_comment = f"{os.path.basename(str_path_file)} updated by CDC for {sc}"
    # Define output filename as per documentation

    str_filename = f"PAF_MTI{sc[-1]}+{instrument}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_{os.path.basename(str_path_file)[4:-4]}-ID{identifier}.xml"
    filenamexml = os.path.join(Output_Folder, str_filename)

    # Initialize Variables
    lst_rows_new = []
    lst_rows_temp = []
    str_file_name = os.path.basename(str_path_file)

    # Open template PAF xml file from in-memory cache
    file_bytes = cache.get(str_path_file)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {str_path_file}")
    tree = etree.parse(io.BytesIO(file_bytes))
    # Get whole tree
    root = tree.getroot()
    # Update Header
    header = root.findall(".//header")[0]
    header.find("CREATION_DATE").text = (
        datetime.datetime.now().strftime("%Y-%jT%H:%M:%S.%f")[0:-3] + "Z"
    )
    header.find("ORIGINATOR").text = str_author
    header.find("COMMENT").text = str_comment
    # Update Metadata
    metadata = root.findall(".//metadata")[0]
    metadata.find("OBJECT_NAME").text = str_object_name
    metadata.find("OBJECT_ID").text = str_object_id

    # Update Parameters
    # This is hardcoded, the PAF should not change variable name
    Number_Of_Cycles = int(Dictionary["N_RC"])
    if Number_Of_Cycles > 3:
        logger.warning(
            f"The input file contains {Number_Of_Cycles} Repeat Cycles in the REPSEQ. Only the first three will be considered. Data should not be trusted."
        )

        basics.pop_up_message(
            "WTH",
            f"The input file contains {Number_Of_Cycles} Repeat Cycles in the REPSEQ. Only the first three will be considered. Data should not be trusted.",
            "warning",
        )

    for parameter in root.findall(".//enumeration"):
        str_name = parameter.find("name").text
        if "N_RC" in str_name:
            str_value = str(int(Dictionary[str_name]))
            parameter.find("value").text = str_value
            logger.info(
                f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
            )

        # PART_ID in PAF is not in VSM/netCDF. It is constructed as follows. PART_ID 1/3/5 correspond to the three COV_ID
        # PART_ID 2/4/6 correspond to the respective TRANS_ID, i.e. PART_ID 1 = 1st coverage, PART_ID 2 = transition
        # from 1st coverage to 2nd coverage, PART_ID 3 = 2nd coverage, etc....
        elif "PART_ID" in str_name:
            int_element = int(str_name[-1])
            if int_element in [1, 3, 5]:
                if int((int_element + 1) / 2) > Number_Of_Cycles:
                    parameter.getparent().remove(parameter)
                else:
                    str_value = str(
                        int(Dictionary[f"COV_ID{int((int_element + 1) / 2)}"])
                    )
                    parameter.find("value").text = str_value
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                    )

            elif int_element in [2, 4, 6]:
                if int(int_element / 2) > Number_Of_Cycles:
                    parameter.getparent().remove(parameter)
                else:
                    str_value = str(
                        int(Dictionary[f"TRANS_ID{int(int_element / 2)}"]) + 4
                    )
                    parameter.find("value").text = str_value
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                    )

    for parameter in root.findall(".//integer"):
        str_name = parameter.find("name").text
        if str_name in Dictionary:
            if "TRANS_ID" in str_name:
                int_element = int(str_name[-1])
                if int_element > Number_Of_Cycles:
                    parameter.getparent().remove(parameter)
                else:
                    str_value = str(int(Dictionary[str_name]) + 6)
                    parameter.find("value").text = str_value
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                    )

            elif "COV_ID" in str_name:
                int_element = int(str_name[-1])
                if int_element > Number_Of_Cycles:
                    parameter.getparent().remove(parameter)
                else:
                    str_value = str(int(Dictionary[str_name]) + 2)
                    parameter.find("value").text = str_value
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                    )

            else:
                str_value = str(int(Dictionary[str_name]))
                parameter.find("value").text = str_value
                logger.info(
                    f"Setting {str_name} in {str_file_name} to {str_value} from netCDF"
                )

    # Extra steps to keep the XML Declaration the brute force way (to avoid to add further modules)
    # Write the draft output - without XML Declaration
    os.makedirs(os.path.dirname(filenamexml), exist_ok=True)
    tree.write(filenamexml)

    # Read the template
    # Read the template from in-memory cache
    file_bytes = cache.get(str_path_file)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {str_path_file}")
    with io.BytesIO(file_bytes) as obj_file:
        for row in obj_file:
            lst_rows_temp.append(row.decode("utf-8"))

    XMLDeclaration = lst_rows_temp[0]

    # Read the draft output
    with open(filenamexml, "r+") as obj_file:
        for row_new in obj_file:
            lst_rows_new.append(row_new)

    # Put XML Declaration from template together with draft output
    lst_total = [XMLDeclaration] + lst_rows_new

    # Rewrite the output
    output = open(filenamexml, "w+b")
    output.close()

    with open(filenamexml, "a") as obj_output:
        for rows in lst_total:
            obj_output.write(rows)

    # Lastly, verify the PAF against the xml schema
    check_xml(
        filenamexml,
        f"{config.CONFIG_VALUES['schema']}",
        bln_test,
    )


def PAF_Update_IRS_REPSEQ(
    instrument: str,
    str_path_file: str,
    Dictionary,
    Output_Folder,
    sc,
    identifier,
    bln_test: bool = False,
):
    """UPDATE TEMPLATE PAF FILE FOR REPSEQ WITH THE VALUES REQUIRED FOR PARAMETERS DEFINED IN VSM - IRS
    Specificities requiring a dedicated functions for simplicity"""

    str_author = "OPIT"
    str_object_name = f"MTG-S{sc[-1]}"
    str_object_id = f"MTS{sc[-1]}"
    str_comment = f"{os.path.basename(str_path_file)} updated by CDC for {sc}"
    # Define output filename as per documentation

    # Custom SAV filename logic
    if instrument == "IRS" and "SAV" in os.path.basename(str_path_file).upper():
        date_str = datetime.datetime.now().strftime("%Y%m%d")
        str_filename = f"PAF_MTS1+IRS_SAV-{date_str}-ID{identifier}.xml"
    else:
        str_filename = f"PAF_MTS{sc[-1]}+{instrument}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_{os.path.basename(str_path_file)[4:-4]}-ID{identifier}.xml"
    filenamexml = os.path.join(Output_Folder, str_filename)

    # Initialize Variables
    lst_rows_new = []
    lst_rows_temp = []
    str_file_name = os.path.basename(str_path_file)

    # Open template PAF xml file from in-memory cache
    file_bytes = cache.get(str_path_file)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {str_path_file}")
    tree = etree.parse(io.BytesIO(file_bytes))
    # Get whole tree
    root = tree.getroot()
    # Update Header
    header = root.findall(".//header")[0]
    header.find("CREATION_DATE").text = (
        datetime.datetime.now().strftime("%Y-%jT%H:%M:%S.%f")[0:-3] + "Z"
    )
    header.find("ORIGINATOR").text = str_author
    header.find("COMMENT").text = str_comment
    # Update Metadata
    metadata = root.findall(".//metadata")[0]
    metadata.find("OBJECT_NAME").text = str_object_name
    metadata.find("OBJECT_ID").text = str_object_id

    # --- Set <date> field if present in Dictionary ---
    if "T_SAV_RS" in Dictionary:
        for date_elem in root.findall(".//date"):
            name_elem = date_elem.find("name")
            if name_elem is not None and name_elem.text == "T_SAV_RS":
                date_elem.find("value").text = Dictionary["T_SAV_RS"]
                logger.info(f"Setting T_SAV_RS in {str_file_name} to {Dictionary['T_SAV_RS']} from netCDF")

    # Update Parameters
    # Handle the LAC pointer index data (corresponds to FCI's N_RC logic)
    if "lac_pointer_index" in Dictionary:
        Number_Of_LACs = len(Dictionary["lac_pointer_index"])
        if Number_Of_LACs > 24:
            logger.warning(
                f"The input file contains {Number_Of_LACs} LAC entries in the REPSEQ. Only the first 24 will be considered."
            )

    # Update enumeration parameters (REPSEQ_SLOT, ACTTAB_SLOT, PART_IDs)
    for parameter in root.findall(".//enumeration"):
        str_name = parameter.find("name").text
        if str_name in Dictionary:
            str_value = str(Dictionary[str_name])
            parameter.find("value").text = str_value
            logger.info(
                f"Setting {str_name} in {str_file_name} to {str_value} from data"
            )

    # Update integer parameters (SL_Length)
    for parameter in root.findall(".//integer"):
        str_name = parameter.find("name").text
        if str_name in Dictionary:
            str_value = str(int(Dictionary[str_name]))
            parameter.find("value").text = str_value
            logger.info(
                f"Setting {str_name} in {str_file_name} to {str_value} from data"
            )

    # Update long parameters (ACTTAB_ID, REPSEQ_GRID, REPSEQ_RCs, MS_SLOT, SL_ID, SCAE_SLOT)
    for parameter in root.findall(".//long"):
        str_name = parameter.find("name").text
        if str_name in Dictionary:
            # Handle REPSEQ_RC parameters with LAC pointer index
            if "REPSEQ_RC" in str_name:
                int_element = int(str_name.replace("REPSEQ_RC", ""))
                if "lac_pointer_index" in Dictionary:
                    if int_element <= len(Dictionary["lac_pointer_index"]):
                        str_value = str(int(Dictionary["lac_pointer_index"][int_element - 1]))
                        parameter.find("value").text = str_value
                        logger.info(
                            f"Setting {str_name} in {str_file_name} to {str_value} from lac_pointer_index"
                        )
                    else:
                        # Remove parameter if no corresponding LAC data
                        parameter.getparent().remove(parameter)
                else:
                    # Use direct value if available
                    str_value = str(int(Dictionary[str_name]))
                    parameter.find("value").text = str_value
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from data"
                    )
            # Handle PART_ID parameters with LAC pointer index (same as REPSEQ_RC for IRS)
            elif "PART_ID_" in str_name:
                int_element = int(str_name.replace("PART_ID_", ""))
                if "lac_pointer_index" in Dictionary:
                    if int_element <= len(Dictionary["lac_pointer_index"]):
                        str_value = str(int(Dictionary["lac_pointer_index"][int_element - 1]))
                        parameter.find("value").text = str_value
                        logger.info(
                            f"Setting {str_name} in {str_file_name} to {str_value} from lac_pointer_index"
                        )
                    else:
                        # Remove parameter if no corresponding LAC data
                        parameter.getparent().remove(parameter)
                else:
                    # Use direct value if available
                    str_value = str(int(Dictionary[str_name]))
                    parameter.find("value").text = str_value
                    logger.info(
                        f"Setting {str_name} in {str_file_name} to {str_value} from data"
                    )
            else:
                # Handle all other long parameters normally
                str_value = str(int(Dictionary[str_name]))
                parameter.find("value").text = str_value
                logger.info(
                    f"Setting {str_name} in {str_file_name} to {str_value} from data"
                )

    # Extra steps to keep the XML Declaration the brute force way (to avoid to add further modules)
    # Write the draft output - without XML Declaration
    os.makedirs(os.path.dirname(filenamexml), exist_ok=True)
    tree.write(filenamexml)

    # Read the template from in-memory cache
    file_bytes = cache.get(str_path_file)
    if file_bytes is None:
        raise FileNotFoundError(f"File not found in cache: {str_path_file}")
    with io.BytesIO(file_bytes) as obj_file:
        for row in obj_file:
            lst_rows_temp.append(row.decode("utf-8"))

    XMLDeclaration = lst_rows_temp[0]

    # Read the draft output
    with open(filenamexml, "r+") as obj_file:
        for row_new in obj_file:
            lst_rows_new.append(row_new)

    # Put XML Declaration from template together with draft output
    lst_total = [XMLDeclaration] + lst_rows_new

    # Rewrite the output
    output = open(filenamexml, "w+b")
    output.close()

    with open(filenamexml, "a") as obj_output:
        for rows in lst_total:
            obj_output.write(rows)

    # Lastly, verify the PAF against the xml schema
    check_xml(
        filenamexml,
        f"{config.CONFIG_VALUES['schema']}",
        bln_test,
    )
