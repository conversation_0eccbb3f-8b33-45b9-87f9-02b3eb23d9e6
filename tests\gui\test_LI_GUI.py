import pytest
from src.gui import create_app
from src.gui.LI_GUI import LI_GUI
from src.gui.frames import InstrumentHandler # Import InstrumentHandler
from src.utils.activity_params import ActivityParams
from src.gui.li_gui_features.basic_conf_gui import BASIC_CONF_GUI

def test_li_gui_handle_activity(mock_app):
    # Create test activity parameters
    act_params = ActivityParams()
    act_params.instrument = "LI"
    act_params.activity = "LOH BASIC Conf"
    
    # Register LI activities with the InstrumentHandler
    # This step is crucial for InstrumentHandler to know about LI_GUI's activities
    LI_GUI.register_activities(InstrumentHandler)
    
    # Call handle_activity on InstrumentHandler
    InstrumentHandler.handle_activity(mock_app, act_params)
    
    # Verify show_frame was called with correct parameters
    mock_app.show_frame.assert_called_once_with(BASIC_CONF_GUI, act_params)
