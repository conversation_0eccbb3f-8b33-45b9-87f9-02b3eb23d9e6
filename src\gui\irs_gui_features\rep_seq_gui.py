import tkinter as tk
from tkinter import ttk
from typing import List, Optional
from src.utils.import_utils import basics, logger
from src import functions
from ..frames import BaseFrame
from ..custom_widgets import PrimaryCard
from src.utils.activity_params import ActivityParams
from ..theme_manager import ThemeManager

class REP_SEQ_GUI(BaseFrame):
    """Frame for IRS Repeat Sequence configuration"""
    def __init__(self, parent: tk.Widget, act_params: ActivityParams, *args, **kwargs):
        """Initialize the Repeat Sequence GUI frame."""
        self.act_params = act_params
        # Initialize widget variable
        self.input_repeat_seq = None
        super().__init__(parent, *args, **kwargs)


    def create_widgets(self):
        """Create and layout GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Main frame using grid
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew") # Use grid

        # Configure main layout rows/columns
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1)

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)


    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1)

        ttk.Label(
            title_frame,
            text=f"IRS Repeat Sequence Configuration - {', '.join(self.act_params.satellites or [])}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew") # Use grid

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5) # Use grid


    def _create_body(self, parent):
        """Creates the body section with the input card, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)

        # Configure grid to center content
        body_frame.columnconfigure(0, weight=1) # Left spacer
        body_frame.columnconfigure(1, weight=0) # Content column
        body_frame.columnconfigure(2, weight=1) # Right spacer
        body_frame.rowconfigure(0, weight=1) # Top spacer
        body_frame.rowconfigure(1, weight=0) # Card row
        body_frame.rowconfigure(2, weight=1) # Bottom spacer

        # Repeat Sequence ID Card - Removed fixed sizes
        repeat_seq_card = PrimaryCard(body_frame, title="Repeat Sequence Configuration", padding=5)
        repeat_seq_card.grid(row=1, column=1, pady=10, sticky="ew") # Center column

        repeat_seq_content = repeat_seq_card.get_content_frame()
        frame_repeat = ttk.Frame(repeat_seq_content, style="CardInner.TFrame")
        frame_repeat.pack(anchor="center", pady=5, padx=5) # Pack inside card is fine

        ttk.Label(frame_repeat, text="Repeat Sequence ID:", style="PrimaryCard.TLabel").pack(side=tk.LEFT, padx=5)

        self.input_repeat_seq = ttk.Entry(frame_repeat, width=10)
        self.input_repeat_seq.pack(side=tk.LEFT, padx=5)
        self.input_repeat_seq.insert(0, "16384")  # Set default value


    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=2)

        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5)

        ThemeManager.create_action_buttons(
            parent=controls_frame,
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )


    def execute(self) -> None:
        """Handle execution of the repeat sequence with validation."""
        # Ensure widgets are created before accessing them
        if self.input_repeat_seq is None:
            logger.error("Execute called before widgets created in IRS REP_SEQ_GUI")
            basics.pop_up_message("Error", "GUI not fully initialized.", "error")
            return

        # Validate and update Repeat Sequence ID
        repeat_seq_id_val = self.input_repeat_seq.get().strip()
        if not repeat_seq_id_val:
            basics.pop_up_message("Error", "Repeat Sequence ID cannot be empty.", "error")
            return
        repeat_seq_id_int = int(repeat_seq_id_val)
        self.act_params.repeat_sequence_id = repeat_seq_id_int

        logger.info(f"Generating Repeat Sequence ID: {self.act_params.repeat_sequence_id}")
        functions.generate_outputs(act_params=self.act_params)
        basics.pop_up_message("Success", "Repeat Sequence configuration generated successfully.")


    def back(self) -> None:
        """Handle back navigation"""
        if self.app is not None and hasattr(self.app, "back"):
            self.app.back()
        else:
            logger.error("No app instance with 'back' method available in REP_SEQ_GUI")
