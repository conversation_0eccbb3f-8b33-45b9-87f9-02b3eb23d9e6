# filepath: c:\Users\<USER>\OneDrive - eumetsat.int\Desktop\workon\CDC-S\src\algorithms\irs_functions\memory_image_file_IRS.py
# Local imports
from src.utils.import_utils import mamut
from src.utils.conversion_utils import dec2hex, hex2dec
from src.logger_wrapper import logger

def memory_image_file_IRS(
    image,
    TC_Type,
    str_path_full: str,
    str_sat: str,
    device,
    lst_bands: list,
    n_bands: int
):
    """MEMORY IMAGE Writing the memory image for IRS instrument
    
    Args:
        image: Memory image data
        TC_Type: Telecommand type
        str_path_full: Full path for output file
        str_sat: Satellite identifier
        device: Device identifier (RAM type)
        lst_bands: List of bands processed
        n_bands: Band number identifier
    """
    # Preparation of data for the Image File header structure
    dict_mem_header = {}
    hex_data = ""

    for int_packets in range(len(image[1:])):
        hex_data += image[int_packets + 1][2]

    # Unit (SAU=2 for VCU RAM)
    if device == "VCU_RAMN" or device == "VCU_RAMR":
        int_SAU = 2
    else:
        int_SAU = 1

    # End Address (of the last memory data unit, in Hex)
    endaddress = dec2hex(
        hex2dec(image[-1][0]) + hex2dec(image[-1][1]) - int_SAU
    )

    # Creation of the Header
    dict_mem_header["ID"] = 1
    dict_mem_header["VERSION"] = 1.0
    dict_mem_header["DOMAIN"] = f"{str_sat[0:2]}{str_sat[4]}{str_sat[6]}"
    dict_mem_header["TYPE"] = "PATCH"
    dict_mem_header["DESCRIPTION"] = (
        f"{device},for_IRS_{TC_Type},band_{','.join(lst_bands).replace('.', '')}"
    )
    dict_mem_header["SOURCE"] = "CDC SOET"
    dict_mem_header["CREATIONDATE"] = ""
    dict_mem_header["MODEL"] = ""
    dict_mem_header["MODELVER"] = ""
    dict_mem_header["DEVICE"] = device
    dict_mem_header["STARTADDR"] = dec2hex(hex2dec(image[1][0]))
    dict_mem_header["ENDADDR"] = dec2hex(hex2dec(endaddress))
    dict_mem_header["LENGTH"] = ""
    dict_mem_header["CHECKSUM"] = ""
    dict_mem_header["UNIT"] = int_SAU

    # Creation of the "body" of the image
    obj_memory_image = mamut.memory_image(
        dict_header=dict_mem_header, hex_image=hex_data, logger_ext=logger
    )

    # Create image memory file
    logger.info(f"$$$$ length: {n_bands}")
    obj_memory_image.create_file(str_path_full, "fletcher16", "1", n_bands % 100 == 0)
