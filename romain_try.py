import sys, os
#Prevent the generation of .pyc files
sys.dont_write_bytecode=True
#Reposition path to project root
PATH_SCRIPT=os.path.dirname(os.path.realpath(sys.argv[0]))
for var in reversed(PATH_SCRIPT.split(os.sep)):
    if(var==NAME_PROJECT): break
    os.chdir('..')
sys.path.append(os.getcwd())




from src.algorithms.uvn_functions.converter.netcdf_Converter import NetcdfConverter
from src.algorithms.uvn_functions.converter.memory_image_converter import MemoryImageConverter
from src.algorithms.uvn_functions.converter.excel_converter import ExcelConverter
from tests.helpers import (assert_ncd_files, assert_img_files)

netcdf_conv = NetcdfConverter()
excel_conv = ExcelConverter()

#input_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'UVNICID_VAL_INSTPAR_MTS1_20250513151826_CDC-PTD-EXPORT_2_2.nc'
#input_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'UVNICID_DEV_INSTPAR_MTS1_20250519073109_CDC-PTD-EXPORT_0001_generationPTD.nc'
#input_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'UVNICID_DEV_INSTPAR_MTS1_20250520132406_PTD2CDC_0001_demo.nc'
#input_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'MTS1_MTS1_UVN_PTD2CDC_ptd_3.7.1.0_export_configuration_update.nc'
#input_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'ptd_3_7_1_0.nc'
#input_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'ptd_3_7_2_0.nc'
input_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'MTS1_UVN_PTD_ref.nc'

#input_excel_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'ptd_3_7_1_0_ref.xlsx'
#input_excel_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'ptd_3_7_2_0_ref.xlsx'
input_excel_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'MTS1_UVN_PTD_ref.xlsx'

#"""
#output_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Output/'+'ptd_3_7_1_0.nc'
#output_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Output/'+'ptd_3_7_2_0.nc'
output_netcdf_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Output/'+'MTS1_UVN_PTD_ref.nc'

input_excel_ptd_dict = excel_conv.convert_excel_to_dict(filename_xls=input_excel_file)

netcdf_conv.convert_dict_to_netcdf(dict_ptd=input_excel_ptd_dict,filename_ncd=output_netcdf_file)

#"""

"""
input_netcdf_file_ref=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'ptd_3_7_1_0.nc'

output_image_file_ref=PATH_SCRIPT+'/tests/tests_assets/UVN/Input/'+'ptd_3_7_1_0_S4AARAM_ref.img'

output_image_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Output/'+'ptd_3_7_1_0_S4AARAM_tst.img'

assert assert_ncd_files([input_netcdf_file],[input_netcdf_file_ref])
#"""

"""
input_netcdf_ptd_dict = netcdf_conv.convert_netcdf_to_dict(input_netcdf_file)
#"""

"""
output_image_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Output/'+'ptd_3_7_1_0_S4AARAM_tst.img'
#output_image_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Output/'+'ptd_3_7_2_0_S4AARAM_tst.img'
image_conv = MemoryImageConverter(memory_id='SDRAM')
image_conv.convert_dict_to_image(dict_ptd=input_netcdf_ptd_dict,filename_img=output_image_file,icu_id="ICU_A",software_id="ASW")
#image_conv.convert_dict_to_image(dict_ptd=input_netcdf_ptd_dict,filename_img=output_image_file,icu_id="ICU_B",software_id="ASW")
#assert assert_img_files([output_image_file_ref], [output_image_file])
#"""

"""
output_excel_file=PATH_SCRIPT+'/tests/tests_assets/UVN/Output/'+'ptd_msm_2026.xlsx'

excel_conv = ExcelConverter()
excel_conv.convert_dict_to_excel(dict_ptd=input_netcdf_ptd_dict,filename_xls=output_excel_file)
#"""







#===============================================================================
# import pandas as pd
# from math import ceil

# def query_chart_api2(
#     str_server: str,
#     str_satellite: str,
#     str_query_type: str,
#     lst_fields: list,
#     dt_start_time: datetime = "",
#     dt_end_time: datetime = "",
#     int_chart_days_limit: int = 14,
# ):
#     """Makes a query to CHART using API 2
#     For information on the str_query_types or the possible fields per type go to https://chart/mtg/api2/

#     Limitation, timeseries of multiple parameters are recommended only for the same SPID to avoid returning packets which only
#     contain a subset of the parameters defined within them

#     Arguments definition:
#     str_server --> chart server e.g:https://chart/mtg/api2/
#     str_satellite --> satellite name
#     str_query_type --> query type as per chart api2 help
#     lst_fields --> query fields as per chart api2 help
#     dt_start_time --> oldest time of the query
#     dt_end_time --> newest time of the query
#     int_chart_days_limit --> chart time limit for queries, the script will chunk the request in smaller step automatically


#     Examples:
#     #Extracting several parameter contain in the same SPID:
#     query_chart_api2("https://chart/mtg/api2/","mtgi1", "timeseries", [["store", "tm"], ["param", "sensing_time"], ["param", "A99E014Z"],  ["param", "PP1E051V"],['filter',"spid:188883621"]], datetime.datetime(2023, 7, 1, 0, 0), datetime.datetime(2023, 7, 1, 0, 1))

#     #Extracting a whole packet:
#         query_chart_api2("https://chart/mtg/api2/","mtgi1","packets", [["store", "tm_store"],["domain", "tm"],["param", "sensing_time"],["param", "spid"], ["param", "payload"],["spid", "90010"]],datetime.datetime(2023, 7, 1, 0, 0), datetime.datetime(2023, 7, 1, 0, 1))

#     #Extracting timeseries in reverse order to find the closest parameter to the end_time provided :
#       (span between start_time and end_time <= 14 days)
#         query_chart_api2("https://chart/mtg/api2/","mtgi1", "timeseries", [["store", "tm"], ["param", "sensing_time"], ["limit", 1"], ["reverse", "true"], ["param", "A99E070T"]], datetime.datetime(2023, 7, 1, 0, 0), datetime.datetime(2023, 7, 1, 0, 1))

#     #Filtering for TM parameters which occur only in the given SPID, without the SPID it will provide all the
#      TM parameters from all packets they are in and provide NaN results where parameter is not in the packet:
#         query_chart_api2("https://chart/mtg/api2/","mtgi1","packets", [["store", "tm_store"],["domain", "tm"],["param", "sensing_time"],["param", "spid"], ["param", "H99F900H"], ["param", "H99FP82X"], ["param", "H99FP86X"], ["filter", "spid:159999000"]],datetime.datetime(2023, 7, 1, 0, 0), datetime.datetime(2023, 7, 1, 0, 1))
        
#     NB: For external CHART access, replace "https://chart/mtg/api2/" by "https://chartext.eumetsat.int/mtgs/api2/"
#     """

#     #logger.info("Start Query to CHART API 2")

#     # Initialize variables
#     dict_output = {
#         "timeseries": "csv",
#         "ts": "csv",
#         "events": "json",
#         "packets": "json",
#         "geolocate": "json",
#         "state": "json",
#         "calibrate": "Calibrated value",
#         "meta/project": "json",
#         "meta/stores": "json",
#         "meta/regions": "json",
#         "meta/store": "json",
#         "meta/param": "json",
#         "meta/packets": "json",
#         "meta/packet": "json",
#         "meta/events": "json",
#         "meta/event": "json",
#     }

#     lst_url = [str_server]
#     lst_columns = []
#     lst_df = []

#     # If the chart limit is exceed then call it multiple times
#     if (dt_end_time - dt_start_time).days >= int_chart_days_limit:  # pragma: no cover
#         # Calculate the number of chunks we need to avoid to reach the limit impose by chart
#         int_chunks = ceil((dt_end_time - dt_start_time).days / int_chart_days_limit) + 1

#         #logger.info(
#         #    f"Chart date limit {int_chart_days_limit} days reached, {int_chunks - 1} chunks created"
#         #)

#         # Create a list of all pairs
#         lst_dates = pd.date_range(
#             start=dt_start_time, end=dt_end_time, periods=int_chunks
#         ).tolist()
#     else:
#         lst_dates = [dt_start_time, dt_end_time]

#     # Introduce query type
#     lst_url.append(str_query_type)

#     # Introduce Satellite
#     lst_url.append(f"?sid={str_satellite}")

#     # Add fields to url
#     for lst_step in lst_fields:
#         lst_url.append(f"&{lst_step[0]}={lst_step[1]}")

#         # Get only the parameter we asked for as column names
#         if lst_step[0] == "param":
#             # Add the column name to the list
#             lst_columns.append(lst_step[1])

#     # Create the partial url
#     str_url = "".join(lst_url)

#     # Call the api n times coz chart want to make our life miserable
#     for int_step in range(len(lst_dates) - 1):
#         # Introduce date in url
#         if dt_start_time != "":
#             str_url_step = (
#                 f"{str_url}&start={lst_dates[int_step].strftime('%Y%m%d%H%M%S')}"
#             )
#         if dt_end_time != "":
#             str_url_step = f"{str_url_step}&stop={lst_dates[int_step + 1].strftime('%Y%m%d%H%M%S')}"

#         # Log the full url for this iteration
#         #logger.info(f"URL for Chart query {str_url_step}")

#         # Make the call to CHART
#         if dict_output[str_query_type] == "csv":  # pragma: no cover
#             # To catch the different the different issues when downloading data from chart
#             try:
#                 df_step = pd.read_csv(str_url_step, header=None)
#             except Exception as error:
#                 # Check if is a server or chart error
#                 if hasattr(error, "code"):
#                     #logger.error(f"Type:{error.code} Reason:{error.reason}")

#                     # Terminate the call
#                     return False

#                 elif hasattr(error, "arg"):
#                     #logger.error(error.args[0])

#                     # If is empty we recover and return the empty dataframe
#                     if error.args[0] == "No columns to parse from file":
#                         # Yes i know we will set it 2 time but is a way to create an empty df with x columns
#                         df_step = pd.DataFrame(columns=lst_columns)
#                     else:
#                         # Terminate the call
#                         return error.args[0]
#                 else:
#                     # If something else happens
#                     #logger.error("Something wrong happen.")

#                     return False

#             # Set columns
#             df_step.columns = lst_columns

#             # To avoid formatting issues, as is a csv if no ms chart do not put the . in the times the length without millisecond is 19
#             df_step.loc[df_step["sensing_time"].str.len() == 19, "sensing_time"] = (
#                 df_step["sensing_time"] + ".000000"
#             )

#         elif dict_output[str_query_type] == "json":
#             # To catch the different the different issues when downloading data from chart
#             try:
#                 df_step = pd.read_json(str_url_step, lines=True)

#                 # Check if is empty and add columns anyway
#                 if df_step.empty:  # pragma: no cover
#                     df_step = pd.DataFrame(columns=lst_columns)
#                 else:
#                     df_step.columns = lst_columns

#             except Exception as error:  # pragma: no cover
#                 #logger.error(error.args[0])

#                 # Terminate the call
#                 return error.args[0]

#         # Add df chunk to list
#         lst_df.append(df_step)

#     # Join the different chunks
#     df_results = pd.concat(lst_df)

#     # Delete duplicates if any due to the chunks
#     df_results.drop_duplicates("sensing_time", inplace=True)

#     # Reindex
#     df_results.reset_index(inplace=True, drop=True)

#     # Convert sensing time to date in the pandas dataframe
#     if "sensing_time" in df_results.columns.values.tolist():
#         df_results["sensing_time"] = pd.to_datetime(df_results["sensing_time"])

#     #logger.info(f"Number of lines {len(df_results.index)}")
#     #logger.info("End Query to CHART API 2")

#     return df_results