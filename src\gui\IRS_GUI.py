"""
IRS GUI module for handling IRS-specific activities
"""

from .frames import BaseFrame

# Import IRS-specific frames
from .irs_gui_features.miss_sce_gui import MISS_SCE_GUI
from .irs_gui_features.rep_seq_gui import REP_SEQ_GUI
from .irs_gui_features.activity_table_gui import ACTIVITY_TABLE_GUI
from .irs_gui_features.sca_enc_gui import SCA_ENC_GUI
from .irs_gui_features.vcu_gui import VCU_GUI
from .irs_gui_features.sav_gui import SAV_GUI
from .irs_gui_features.iwp_gui import IWP_GUI

class IRS_GUI(BaseFrame):
    """Main IRS GUI class"""
    
    @staticmethod
    def register_activities(handler):
        # Register IRS activities with the handler
        handler.register_activity("IRS", "Mission Scenario (SL)", MISS_SCE_GUI)
        handler.register_activity("IRS", "Repeat Sequence", REP_SEQ_GUI)
        handler.register_activity("IRS", "Activity Table", ACTIVITY_TABLE_GUI)
        handler.register_activity("IRS", "Scan Encoder LUT", SCA_ENC_GUI)
        handler.register_activity("IRS", "VCU Update", VCU_GUI)
        handler.register_activity("IRS", "SAV", SAV_GUI)
        handler.register_activity("IRS", "Scan Scenario", IWP_GUI)
        # TODO: Add registration for DPU activity when implemented
