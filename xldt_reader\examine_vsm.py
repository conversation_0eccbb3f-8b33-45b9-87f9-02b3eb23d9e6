#!/usr/bin/env python3
"""
Examine the VSM Excel file to understand field structure for XLDT sections.
"""

import pandas as pd
import os
import sys

def examine_vsm_file():
    """Examine the IRS VSM file to understand field structure."""
    vsm_path = "../config/IRS/MTG IRS IPSSCCC VSM (IRS-IPSSCCC-VSM).xlsx"
    
    if not os.path.exists(vsm_path):
        print(f"❌ VSM file not found: {vsm_path}")
        return
    
    print("🔍 Examining IRS VSM File")
    print("=" * 50)
    
    try:
        # Read the Excel file and get sheet names
        excel_file = pd.ExcelFile(vsm_path)
        print(f"📋 Available sheets: {excel_file.sheet_names}")
        
        # Read the SCANLAW sheet
        if "SCANLAW" in excel_file.sheet_names:
            print(f"\n📊 Reading SCANLAW sheet...")
            df_scanlaw = pd.read_excel(vsm_path, sheet_name="SCANLAW", engine="openpyxl")
            
            print(f"   📏 Shape: {df_scanlaw.shape}")
            print(f"   📋 Columns: {list(df_scanlaw.columns)}")
            
            # Show first few rows
            print(f"\n📄 First 10 rows:")
            print(df_scanlaw.head(10).to_string(index=False))

            # Search for dwell_position fields
            print(f"\n🔍 Searching for dwell_position fields:")
            dwell_fields = df_scanlaw[df_scanlaw['Name'].str.contains('dwell', na=False, case=False)]
            if not dwell_fields.empty:
                print(dwell_fields[['Name', 'XLDT_Section', 'Type', 'NetCDF Element']].to_string(index=False))
            else:
                print("   ❌ No dwell_position fields found")

            # Search for all FDA-related fields
            print(f"\n🔍 All fields containing 'fda' or 'alpha' or 'epsilon':")
            fda_related = df_scanlaw[
                (df_scanlaw['Name'].str.contains('fda', na=False, case=False)) |
                (df_scanlaw['Name'].str.contains('alpha', na=False, case=False)) |
                (df_scanlaw['Name'].str.contains('epsilon', na=False, case=False)) |
                (df_scanlaw['NetCDF Element'].str.contains('dwell', na=False, case=False))
            ]
            if not fda_related.empty:
                print(fda_related[['Name', 'XLDT_Section', 'Type', 'NetCDF Element']].to_string(index=False))
            else:
                print("   ❌ No FDA-related fields found")
            
            # Filter by XLDT sections
            xldt_sections = ['FDA', 'MPA', 'LAC_Pointer', 'Header']
            
            for section in xldt_sections:
                print(f"\n🔍 {section} Section Fields:")
                print("-" * 30)
                
                if 'XLDT_Section' in df_scanlaw.columns:
                    section_fields = df_scanlaw[df_scanlaw['XLDT_Section'].str.contains(section, na=False)]
                    
                    if not section_fields.empty:
                        print(f"   📊 Found {len(section_fields)} fields for {section}")
                        
                        # Show relevant columns
                        relevant_cols = ['Name', 'XLDT_Section', 'Type', 'Description', 'Long name']
                        available_cols = [col for col in relevant_cols if col in section_fields.columns]
                        
                        if available_cols:
                            print(section_fields[available_cols].to_string(index=False))
                        else:
                            print(section_fields.to_string(index=False))
                    else:
                        print(f"   ❌ No fields found for {section}")
                else:
                    print(f"   ❌ XLDT_Section column not found")
            
            # Show unique XLDT sections
            if 'XLDT_Section' in df_scanlaw.columns:
                unique_sections = df_scanlaw['XLDT_Section'].dropna().unique()
                print(f"\n📋 Unique XLDT Sections found:")
                for section in sorted(unique_sections):
                    count = len(df_scanlaw[df_scanlaw['XLDT_Section'] == section])
                    print(f"   • {section}: {count} fields")
        
        else:
            print(f"❌ SCANLAW sheet not found in VSM file")
            print(f"Available sheets: {excel_file.sheet_names}")
    
    except Exception as e:
        print(f"❌ Error reading VSM file: {e}")
        import traceback
        traceback.print_exc()


def show_field_structure():
    """Show the expected field structure based on documentation."""
    print(f"\n📋 Expected Field Structure (from documentation):")
    print("=" * 50)
    
    sections = {
        "FDA": [
            "dwell_position_alpha (float)",
            "dwell_position_epsilon (float)", 
            "fda_mp_pointer_alpha (int/pointer)",
            "fda_mp_pointer_epsilon (int/pointer)"
        ],
        "MPA": [
            "Motion profile parameters",
            "Various motion profile arrays",
            "Scaled and transformed values"
        ],
        "LAC_Pointer": [
            "LAC pointer data",
            "Integer and float fields",
            "Scaled (x1000) float32 values"
        ]
    }
    
    for section, fields in sections.items():
        print(f"\n🔍 {section} Section:")
        for i, field in enumerate(fields, 1):
            print(f"   {i}. {field}")


if __name__ == "__main__":
    examine_vsm_file()
    show_field_structure()
    
    print(f"\n💡 Next Steps:")
    print("   1. Use VSM field definitions to parse binary sections")
    print("   2. Create structured parameter output like CDC system")
    print("   3. Implement field-by-field parsing with proper data types")
    print("   4. Add parameter names, descriptions, and dimensions")
