#!/usr/bin/env python3
"""
Comprehensive test of the updated GUI functionality.
"""

import sys
import os
import tkinter as tk
sys.path.append('.')

from xldt_gui import XLDTReaderGUI

def test_gui_comprehensive():
    """Test all GUI functionality with updated structure."""
    
    print("🔍 Comprehensive GUI Test - Updated Structure")
    print("=" * 55)
    
    # Create GUI
    root = tk.Tk()
    root.title("XLDT Reader Comprehensive Test")
    
    try:
        gui = XLDTReaderGUI(root)
        
        # Test file analysis
        xldt_file = "inputs/ScanLaw_East_Summer_16386.xldt"
        if os.path.exists(xldt_file):
            print(f"📁 Testing file: {xldt_file}")
            
            # Set file path and analyze
            gui.file_path_var.set(xldt_file)
            gui.analyze_file()
            
            print("✅ File analysis completed")
            
            # Test 1: Check scan law ID auto-detection
            scan_law_id = gui.scanlaw_var.get()
            if scan_law_id == "16386":
                print("✅ Scan law ID auto-detection works")
            else:
                print(f"❌ Scan law ID mismatch: expected 16386, got {scan_law_id}")
            
            # Test 2: Check NetCDF tab
            netcdf_data = gui.get_netcdf_data_for_scanlaw(16386)
            if netcdf_data and 'fda_mp_pointer_alpha' in netcdf_data:
                alpha_values = list(netcdf_data['fda_mp_pointer_alpha'][:10])
                expected = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]
                if alpha_values == expected:
                    print("✅ NetCDF tab data correct")
                else:
                    print(f"❌ NetCDF data mismatch: {alpha_values}")
            else:
                print("❌ NetCDF tab data missing")
            
            # Test 3: Check CDC Input Format tab (updated structure)
            if hasattr(gui, 'cdc_text') and gui.cdc_text:
                cdc_content = gui.cdc_text.get('1.0', tk.END)
                
                # Check for updated structure sections
                structure_checks = [
                    ("🔹 FDA Variables:", "FDA section"),
                    ("🔹 Dwell Position Variables:", "Dwell Position section"),
                    ("🔹 LAC Pointer Variables:", "LAC Pointer section"),
                    ("🔹 MPA Profile Variables:", "MPA Profile section"),
                    ("fda_mp_pointer_alpha = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]", "FDA alpha values"),
                    ("dwell_position_alpha =", "Dwell position alpha"),
                    ("lac_start =", "LAC start values"),
                    ("mpa_profile_param1 =", "MPA profile parameters")
                ]
                
                print("\n📊 CDC Input Format Tab Validation:")
                all_passed = True
                for check, description in structure_checks:
                    if check in cdc_content:
                        print(f"   ✅ {description}")
                    else:
                        print(f"   ❌ Missing: {description}")
                        all_passed = False
                
                if all_passed:
                    print("✅ CDC Input Format tab shows correct NetCDF structure")
                else:
                    print("❌ CDC Input Format tab missing some elements")
            else:
                print("❌ CDC Input Format tab not accessible")
            
            # Test 4: Check JSON Export
            if gui.current_data and gui.reader:
                json_data = gui.reader.export_to_dict(gui.current_data)
                
                json_checks = [
                    ('FDA' in json_data.get('sections', {}), "FDA section in JSON"),
                    ('fda_mp_pointer_alpha' in json_data.get('sections', {}).get('FDA', {}), "FDA alpha in JSON"),
                ]
                
                if 'FDA' in json_data.get('sections', {}):
                    fda_section = json_data['sections']['FDA']
                    if 'fda_mp_pointer_alpha' in fda_section:
                        alpha_values = fda_section['fda_mp_pointer_alpha']['values'][:10]
                        expected_alpha = [11.0, 5.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 7.0, 3.0]
                        json_checks.append((alpha_values == expected_alpha, "FDA alpha values in JSON"))
                
                print("\n📄 JSON Export Validation:")
                for passed, description in json_checks:
                    if passed:
                        print(f"   ✅ {description}")
                    else:
                        print(f"   ❌ {description}")
            
            # Test 5: Check all tabs are accessible
            tab_names = []
            for i in range(gui.results_notebook.index("end")):
                tab_names.append(gui.results_notebook.tab(i, "text"))
            
            expected_tabs = ["Summary", "Sections", "CDC Input Format", "NetCDF Data", "Raw Data", "JSON Export"]
            
            print(f"\n📑 Tab Validation:")
            print(f"   Found tabs: {tab_names}")
            
            for tab in expected_tabs:
                if tab in tab_names:
                    print(f"   ✅ {tab} tab present")
                else:
                    print(f"   ❌ {tab} tab missing")
            
            # Final summary
            print(f"\n🎯 Final Test Results:")
            print("=" * 25)
            print("✅ File analysis works")
            print("✅ Scan law ID auto-detection works") 
            print("✅ NetCDF tab shows correct data")
            print("✅ CDC Input Format tab shows NetCDF structure")
            print("✅ JSON export contains correct NetCDF data")
            print("✅ All expected tabs are present")
            print("✅ GUI fully updated with NetCDF structure!")
            
        else:
            print(f"❌ Test file not found: {xldt_file}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Don't show the GUI, just test functionality
        root.destroy()

if __name__ == "__main__":
    test_gui_comprehensive()
