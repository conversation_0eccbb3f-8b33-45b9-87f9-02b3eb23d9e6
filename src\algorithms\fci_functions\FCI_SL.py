# Standard library imports
import datetime
import os
import pandas as pd
from typing import Dict, Any

# Local imports
from src.logger_wrapper import logger
from src.utils.import_utils import basics, config
from src.utils.excel_utils import Read_Sheet
from src.utils.netcdf_utils import read_netCDF
from src.functions import create_XLDT
from src.utils.conversion_utils import hex2dec, dec2hex, float_to_hex
from src.utils.activity_params import ActivityParams

def FCI_SL(act_params: ActivityParams, satellite: str) -> None:
    """Generate FCI Scan Law binary file."""
    # Initialize Variables
    Binary_Section_Hex = {}
    Section_Pos = {}
    Body_Size = 0

    # Load Configuration
    dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
    df_SL_config = pd.read_csv(
        os.path.join(
            config.CONFIG_VALUES["config_folder"], act_params.instrument, "FCI_SL_Conf.csv"
        ),
        keep_default_na=False,
        index_col=None,
    )
    df_SL_config.set_index("Section", inplace=True)

    # Identify the netCDF file in which the "slicer" should be used to identify the configuration
    main_files = used_files = ["SCANLAW"]
    
    # Get the dictionary of values from the netCDF files for the desired slicing parameter
    logger.info("Reading netCDF files for FCI Scan Law")

    # Read netCDF file using updated signature
    dict_netCDF = read_netCDF(
        act_params=act_params,
        satellite=satellite,
        main_files=main_files,
        used_files=used_files,
        instrument_conf=dict_config
    )

    # For each Section, generate the HEX string
    lst_XLDT_body_hex = []

    # Read VSM file
    df_file_tab = Read_Sheet(dict_config["vsm_path"], "SCANLAW")

    # Find only variables needed for the specific Section
    for sections in df_SL_config.index:        
        logger.info(f"Processing section: {sections}")
        df_file_tab_proc = df_file_tab[
            df_file_tab["XLDT_Section"].str.contains(sections)
        ]

        Section_Pos[sections] = df_SL_config.loc[sections]["Order"]
        df_file_tab_proc.set_index("Name", inplace=True)

        # Generate the Section HEX string
        Binary_Section_Hex[Section_Pos[sections]] = FCI_SL_Section_Build(
            df_file_tab_proc,
            sections,
            df_SL_config,
            act_params.scan_law_id,
            act_params.mm_slot,
            dict_netCDF
        )
        # Calculate the size of the XLDT Body
        if sections != "XLDT_Header" and sections != "CRC":
            Body_Size += len(Binary_Section_Hex[Section_Pos[sections]]) / 2

    # Because Scan Law Size in Body Header not yet added and it is two bytes
    Body_Size += 2

    Body_Size_Hex = dec2hex(Body_Size).zfill(4)
    # Finish the XLDT Body HEX String
    Binary_Section_Hex[Section_Pos["Header"]] = (
        Binary_Section_Hex[Section_Pos["Header"]] + Body_Size_Hex
    )
    # Add XLDT Body Length to XLDT Header
    XLDT_Size_Hex = dec2hex(hex2dec(Body_Size_Hex) + 2)  # Because XLDT Length in XLDT Header counts also the CRC (2 bytes)
    Binary_Section_Hex[Section_Pos["XLDT_Header"]] = Binary_Section_Hex[
        Section_Pos["XLDT_Header"]
    ] + XLDT_Size_Hex.zfill(8)

    # Put together the different Sections of the XLDT Body in order in one Hex String
    for Part in range(1, df_SL_config["Order"].max() + 1):
        str_section = df_SL_config[df_SL_config["Order"] == Part].index[0]
        if str_section != "XLDT_Header" and str_section != "CRC":
            lst_XLDT_body_hex.append(Binary_Section_Hex[Part])

    # Join the XLDT_Body_Hex string
    XLDT_Body_Hex = "".join(lst_XLDT_body_hex)

    # Calculate checksum (CRC) on XLDT Body Hex String
    str_CRC = basics.checksum(XLDT_Body_Hex, "CRC16").zfill(4)

    # Put everything together in one Hex String
    XLDT_Hex = "".join(
        [Binary_Section_Hex[Section_Pos["XLDT_Header"]], str_CRC, XLDT_Body_Hex]
    )

    # Create XLDT Binary file for Scan Law
    filename = f"OPIT_NA_MS_OPE+MTI{satellite[-1]}+FCI_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}_SL-MM{act_params.mm_slot}-SLID{str(act_params.scan_law_id).zfill(5)}.bin"

    file_name = os.path.join(config.CONFIG_VALUES["output_folder"], satellite, filename)
    create_XLDT(XLDT_Hex, file_name)

def FCI_SL_Section_Build(
    df_file_tab_proc: pd.DataFrame,
    str_section: str,
    df_SL_config: pd.DataFrame,
    int_slicer_1: int,
    int_MM_Slot: int,
    dict_netCDF: Dict[str, Any],
) -> str:
    """Build binary file content of SL for each section in hex format.
    
    Args:
        df_file_tab_proc: Processed file table for this section
        str_section: Section name ("XLDT_Header", "Header", "LAC_Pointer", etc.)
        df_SL_config: Scan Law configuration dataframe
        int_slicer_1: Scan Law ID
        int_MM_Slot: Mass Memory slot ID
        dict_netCDF: Dictionary containing netCDF file data
        
    Returns:
        String containing hexadecimal data for this section
    """
    logger.info(f"Writing the binary file content of SL for section {str_section}")

    # XLDT Header - without body length
    if str_section == "XLDT_Header":
        str_hex = f"000100{dec2hex(int_MM_Slot).zfill(2)}"

    elif str_section == "CRC":
        str_hex = ""

    # XLDT Body Header - without body length
    elif str_section == "Header":
        str_hex = "".join(
            [
                df_SL_config.loc["Header"]["MSDF_ID_Hex"].zfill(2),
                dec2hex(int(df_SL_config.loc["Header"]["Length"])).zfill(6),
                dec2hex(int_slicer_1).zfill(4),
            ]
        )

    # XLDT LAC Pointers - All as many as there are in one Hex string
    elif str_section == "LAC_Pointer":
        # Initialize Variables
        lst_hex = []

        int_LACs = len(dict_netCDF[df_file_tab_proc.index[0]])

        for int_lac in range(int_LACs):
            # Reset Variables
            dict_LAC = {}

            LAC_Header = dec2hex(
                hex2dec(df_SL_config.loc["LAC_Pointer"]["MSDF_ID_Hex"])
                + int_lac
            ).zfill(2)
            LAC_Length = dec2hex(
                int(df_SL_config.loc["LAC_Pointer"]["Length"])
            ).zfill(6)

            for variable in df_file_tab_proc.index:
                LAC_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])
                LAC_var = dict_netCDF[variable][int_lac]
                logger.info(f"variable: {variable}, LAC_var: {LAC_var}")
                if pd.api.types.is_integer_dtype(LAC_var.dtype):
                    dict_LAC[LAC_str_Pos] = dec2hex(LAC_var).zfill(4)
                elif pd.api.types.is_float_dtype(LAC_var.dtype):
                    dict_LAC[LAC_str_Pos] = float_to_hex(
                        LAC_var * 1000
                    ).zfill(8)

            lst_hex.append(LAC_Header + LAC_Length)

            for int_pos in range(
                1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1
            ):
                lst_hex.append(dict_LAC[int_pos])

        # Join hexadecimal string
        str_hex = "".join(lst_hex)

    # XLDT Retraces - All as many as there are in one Hex string
    elif str_section == "Retrace":
        # Initialize Variables
        Hex_String_Dict = {}
        Ret_List = []
        lst_hex = []

        int_Retraces = len(dict_netCDF[df_file_tab_proc.index[0]])

        for Retrace in range(int_Retraces):
            # Reset Variables
            dict_RET = {}

            int_retrace = (
                hex2dec(df_SL_config.loc["Retrace"]["MSDF_ID_Hex"]) + Retrace
            )

            Ret_List.append(int_retrace)
            RET_Header = dec2hex(int_retrace).zfill(2)
            RET_Length = dec2hex(
                int(df_SL_config.loc["Retrace"]["Length"])
            ).zfill(6)

            for variable in df_file_tab_proc.index:
                RET_var = dict_netCDF[variable][Retrace]
                RET_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])

                if pd.api.types.is_integer_dtype(RET_var.dtype):
                    dict_RET[RET_str_Pos] = dec2hex(RET_var).zfill(8)
                elif pd.api.types.is_float_dtype(RET_var.dtype):
                    if "1" in variable:
                        dict_RET[RET_str_Pos] = float_to_hex(
                            (RET_var * pow(2, 25) / 360) / 1000
                        ).zfill(8)
                    else:
                        dict_RET[RET_str_Pos] = float_to_hex(
                            RET_var * 1000
                        ).zfill(8)

            Hex_String_Ret = RET_Header + RET_Length

            for pos in range(1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1):
                Hex_String_Ret += dict_RET[pos]

            Hex_String_Dict[int_retrace] = Hex_String_Ret

        for Ret_Num in range(min(Ret_List), max(Ret_List) + 1):
            lst_hex.append(Hex_String_Dict[Ret_Num])

        # Join hexadecimal string
        str_hex = "".join(lst_hex)

    # XLDT Rallies - All as many as there are in one Hex string
    elif str_section == "Rally":
        # Initialize Variables
        Hex_String_Dict = {}
        lst_Ral = []
        lst_hex = []

        int_Rallies = len(dict_netCDF[df_file_tab_proc.index[0]])

        for Rally in range(int_Rallies):
            # Reset Variables
            RAL_str = {}
            lst_hex_Ral = []

            Rally_ID = dict_netCDF["rally_id"][Rally]
            Rally_Num = hex2dec(df_SL_config.loc["Rally"]["MSDF_ID_Hex"]) + (
                Rally_ID - 1
            )
            lst_Ral.append(Rally_Num)

            RAL_Header = dec2hex(Rally_Num).zfill(2)
            RAL_Length = dec2hex(
                int(df_SL_config.loc["Rally"]["Length"])
            ).zfill(6)

            logger.info(f"RAL_Length: {RAL_Length}")
            
            for variable in df_file_tab_proc.index:
                RAL_var = dict_netCDF[variable][Rally]
                RAL_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])

                if pd.api.types.is_integer_dtype(RAL_var.dtype):
                    RAL_str[RAL_str_Pos] = dec2hex(RAL_var).zfill(8)
                elif pd.api.types.is_float_dtype(RAL_var.dtype):
                    if "1" in variable:
                        RAL_str[RAL_str_Pos] = float_to_hex(
                            (RAL_var * pow(2, 25) / 360) / 1000
                        ).zfill(8)
                    else:
                        RAL_str[RAL_str_Pos] = float_to_hex(
                            RAL_var * 1000
                        ).zfill(8)

            lst_hex_Ral.append(RAL_Header)
            lst_hex_Ral.append(RAL_Length)

            for pos in range(1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1):
                lst_hex_Ral.append(RAL_str[pos])

            Hex_String_Dict[Rally_Num] = "".join(lst_hex_Ral)

        for Ral_Num in range(min(lst_Ral), max(lst_Ral) + 1):
            lst_hex.append(Hex_String_Dict[Ral_Num])

        # Join hexadecimal string
        str_hex = "".join(lst_hex)

    # XLDT FDA - All as many as there are in one Hex string
    elif str_section == "FDA":
        # Initialize Variables
        lst_hex = []

        number_of_Pos = len(dict_netCDF[df_file_tab_proc.index[0]])
        FDA_Header = dec2hex(
            hex2dec(df_SL_config.loc["FDA"]["MSDF_ID_Hex"])
        ).zfill(2)
        FDA_Length_Max = int(df_SL_config.loc["FDA"]["Length"])

        for Pos in range(number_of_Pos):
            FDA_str = {}

            for variable in df_file_tab_proc.index:
                FDA_var = dict_netCDF[variable][Pos]
                FDA_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])

                if pd.api.types.is_integer_dtype(FDA_var.dtype):
                    FDA_str[FDA_str_Pos] = dec2hex(FDA_var).zfill(4)
                elif pd.api.types.is_float_dtype(FDA_var.dtype):
                    FDA_str[FDA_str_Pos] = float_to_hex(FDA_var).zfill(8)

            for pos in range(1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1):
                lst_hex.append(FDA_str[pos])

        FDA_Length_Dec = int(len("".join(lst_hex)) / 2)

        if FDA_Length_Dec > FDA_Length_Max:
            logger.error(
                "The size of the FDA Section of the Scan Law is larger than the maximum allowed. The generated binary should not be trusted."
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        FDA_Length = dec2hex(FDA_Length_Dec).zfill(6)

        # Join hexadecimal string
        str_hex = "".join([FDA_Header, FDA_Length] + lst_hex)

    # XLDT MPA - All as many as there are in one Hex string
    elif str_section == "MPA":
        # Initialize Variables
        lst_hex = []

        int_Prof = len(dict_netCDF[df_file_tab_proc.index[0]])
        MPA_Header = dec2hex(
            hex2dec(df_SL_config.loc["MPA"]["MSDF_ID_Hex"])
        ).zfill(2)
        MPA_Length_Max = int(df_SL_config.loc["MPA"]["Length"])

        for Prof in range(int_Prof):
            MPA_str = {}

            for variable in df_file_tab_proc.index:
                MPA_var = dict_netCDF[variable][Prof]
                MPA_str_Pos = int(df_file_tab_proc.loc[variable, "XLDT_Section_Order"])

                if pd.api.types.is_integer_dtype(MPA_var.dtype):
                    MPA_str[MPA_str_Pos] = dec2hex(MPA_var).zfill(8)
                elif pd.api.types.is_float_dtype(MPA_var.dtype):
                    if "1" in variable:
                        MPA_str[MPA_str_Pos] = float_to_hex(
                            (MPA_var * pow(2, 25) / 360) / 1000
                        ).zfill(8)
                    else:
                        MPA_str[MPA_str_Pos] = float_to_hex(
                            MPA_var * 1000
                        ).zfill(8)

            for pos in range(1, int(df_file_tab_proc["XLDT_Section_Order"].max()) + 1):
                lst_hex.append(MPA_str[pos])

        MPA_Length_Dec = int(len("".join(lst_hex)) / 2)

        if MPA_Length_Dec > MPA_Length_Max:
            logger.error(
                "The size of the MPA Section of the Scan Law is larger than the maximum allowed. The generated binary should not be trusted."
            )
            basics.pop_up_message(
                "Ouch!", "An error Occurred please check the logs.", "error"
            )
            raise RuntimeError()

        MPA_Length = dec2hex(MPA_Length_Dec).zfill(6)

        # Join Hexadecimal string
        str_hex = "".join([MPA_Header, MPA_Length] + lst_hex)

    return str_hex
