import tkinter as tk
from tkinter import ttk
from src.utils.import_utils import basics
from src import functions
from ..frames import BaseFrame
from ..theme_manager import ThemeManager
from ..custom_widgets import PrimaryCard, SecondaryCard
from src.logger_wrapper import logger
from src.utils.netcdf_utils import get_netcdf_variables_value

class SAV_GUI(BaseFrame):
    """Frame for IRS SAV (Sun Avoidance) configuration"""
    def __init__(self, parent, act_params, *args, **kwargs):
        self.act_params = act_params
        # Initialize widget variables to None, they will be created in _create_body
        self.input_activity_table_id = None
        super().__init__(parent, *args, **kwargs)

    def create_widgets(self):
        """Create SAV GUI components using grid layout."""
        # Configure self (BaseFrame) to allow main_frame to expand
        self.rowconfigure(0, weight=1)
        self.columnconfigure(0, weight=1)

        # Use grid for the main_frame within self (BaseFrame already configures row/col 0)
        main_frame = ttk.Frame(self, padding=(5, 5, 5, 0))
        main_frame.grid(row=0, column=0, sticky="nsew")

        # Configure rows/columns for main_frame's internal layout
        main_frame.rowconfigure(0, weight=0) # Title
        main_frame.rowconfigure(1, weight=1) # Body (expands)
        main_frame.rowconfigure(2, weight=0) # Footer
        main_frame.columnconfigure(0, weight=1) # Allow content to expand horizontally

        self._create_title(main_frame)
        self._create_body(main_frame)
        self._create_footer(main_frame)

    def _create_title(self, parent):
        """Creates the title section."""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=0, column=0, sticky="ew", pady=(0, 5))
        title_frame.columnconfigure(0, weight=1) # Allow label/separator to fill

        ttk.Label(
            title_frame,
            text=f"IRS SAV (Sun Avoidance) Configuration - {', '.join(self.act_params.satellites)}",
            style="Title.TLabel"
        ).grid(row=0, column=0, sticky="ew") # Use grid

        ttk.Separator(title_frame, orient='horizontal').grid(row=1, column=0, sticky="ew", pady=5) # Use grid

    def _create_body(self, parent):
        """Creates the body section with input cards, centered using grid."""
        body_frame = ttk.Frame(parent)
        body_frame.grid(row=1, column=0, sticky="nsew", pady=5)

        # Configure body_frame grid to center content vertically and horizontally
        body_frame.columnconfigure(0, weight=1)  # Left spacer
        body_frame.columnconfigure(1, weight=0)  # Content column
        body_frame.columnconfigure(2, weight=1)  # Right spacer
        body_frame.rowconfigure(0, weight=1)     # Top spacer
        body_frame.rowconfigure(1, weight=0)     # Card
        body_frame.rowconfigure(2, weight=1)     # Bottom spacer

        # Activity Table ID Card - Centered in the middle
        activity_table_card = SecondaryCard(body_frame, title="Activity Table Configuration", padding=5)
        activity_table_card.grid(row=1, column=1, pady=5, sticky="ew")

        activity_table_content = activity_table_card.get_content_frame()
        
        ttk.Label(activity_table_content, text="Activity Table ID:", style="SecondaryCard.TLabel").pack(side=tk.LEFT, padx=(0, 5))

        # Get activity table ID values from netCDF files
        try:
            activity_table_id_values = list(get_netcdf_variables_value('activity_table_id', self.act_params, "REPSEQ"))
            activity_table_id_values = sorted(set(int(x) for x in activity_table_id_values))
        except Exception as e:
            logger.warning(f"Could not load activity table IDs: {e}")
            activity_table_id_values = ["1", "2", "3", "4", "5"]  # Default values

        self.input_activity_table_id = ttk.Combobox(
            activity_table_content,
            width=8,
            values=activity_table_id_values,
            state="readonly"
        )
        self.input_activity_table_id.pack(side=tk.LEFT, padx=(0, 5))
        if activity_table_id_values:
            self.input_activity_table_id.current(0)

    def _create_footer(self, parent):
        """Creates the footer section with action buttons."""
        bottom_frame = ttk.Frame(parent)
        bottom_frame.grid(row=2, column=0, sticky="ew")
        bottom_frame.columnconfigure(0, weight=1)

        ttk.Separator(bottom_frame, orient='horizontal').grid(row=0, column=0, sticky="ew", pady=2)

        controls_frame = ttk.Frame(bottom_frame)
        controls_frame.grid(row=1, column=0, sticky="ew", padx=5)

        ThemeManager.create_action_buttons(
            parent=controls_frame, # Pass the frame where buttons should be packed/gridded
            execute_func=self.execute,
            next_text="Generate",
            back_text="Back",
            back_func=self.back
        )

    def execute(self):
        """Handle execution of the SAV configuration with validation."""
        # Ensure widgets are created before accessing them
        if self.input_activity_table_id is None:
            logger.error("Execute called before widgets created in SAV_GUI")
            basics.pop_up_message("Error", "GUI not fully initialized.", "error")
            return

        activity_table_id_val = self.input_activity_table_id.get()

        if not activity_table_id_val:
            basics.pop_up_message("Error", "Activity Table ID cannot be empty.", "error")
            return

        self.act_params.activity_table_id = activity_table_id_val

        # Fetch all repeat_sequence_id values from REPSEQ where activity_table_id matches
        try:
            repseq_ids = list(get_netcdf_variables_value('repeat_sequence_id', self.act_params, "REPSEQ"))
            acttab_ids = list(get_netcdf_variables_value('activity_table_id', self.act_params, "REPSEQ"))
            # Ensure both lists are the same length
            if len(repseq_ids) != len(acttab_ids):
                raise ValueError("Mismatch in length of repeat_sequence_id and activity_table_id arrays in REPSEQ.")
            # Find all repeat_sequence_ids matching the selected activity_table_id
            matching_repseq_ids = [repseq for repseq, acttab in zip(repseq_ids, acttab_ids) if str(acttab) == str(activity_table_id_val)]
        except Exception as e:
            logger.error(f"Error fetching repeat_sequence_id/activity_table_id from REPSEQ: {e}")
            basics.pop_up_message("Error", f"Failed to fetch repeat sequence IDs: {str(e)}", "error")
            return

        if not matching_repseq_ids:
            basics.pop_up_message("Warning", f"No Repeat Sequence IDs found for Activity Table ID {activity_table_id_val}.", "warning")
            return

        errors = []
        for repseq_id in matching_repseq_ids:
            self.act_params.repeat_sequence_id = repseq_id
            try:
                functions.generate_outputs(act_params=self.act_params)
            except Exception as e:
                logger.error(f"Error generating SAV outputs for Repeat Sequence ID {repseq_id}: {e}")
                errors.append(str(repseq_id))
        if errors:
            basics.pop_up_message("Partial Success", f"Some outputs failed for Repeat Sequence IDs: {', '.join(errors)}", "warning")
        else:
            basics.pop_up_message("Success", f"SAV (Sun Avoidance) outputs generated for all matching Repeat Sequence IDs.", "info")

    def back(self):
        """Handle back navigation"""
        if self.app is not None and hasattr(self.app, "back"):
            self.app.back()
        else:
            logger.error("No app instance with 'back' method available in SAV_GUI")
            basics.pop_up_message("Navigation Error", "No previous screen to return to.", "warning")
