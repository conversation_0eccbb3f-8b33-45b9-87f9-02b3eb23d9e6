# Standard library imports
import os
import glob
import pytest
from lxml import etree

# Local imports
from tests.helpers import load_input_files
from src.utils.import_utils import config
from src.functions import generate_outputs
from src.utils.activity_params import ActivityParams


@pytest.fixture()
def test_params():
    """Fixture providing default test parameters for IRS tests."""
    # Load input files
    load_input_files("IRS")

    return ActivityParams(
        activity="Activity Table",
        satellites=["MTG-S 1"],
        instrument="IRS",
        activity_table_slot=1,
        activity_table_id=16384,
        test_mode=True
    )

@pytest.fixture(params=[
        {1, 128},
        {1, 256},
        {1, 16384},
        {1, 16640},
        {1, 16512},
        {2, 128},
        {2, 256},
        {2, 16384},
        {2, 16640},
        {2, 16512},
    ])
def test_params_value(request):
    """Fixture providing default test parameters for IRS tests."""
    # Load input files
    load_input_files("IRS")
    y, x = request.param
    return ActivityParams(
        activity="Activity Table",
        satellites=["MTG-S 1"],
        instrument="IRS",
        activity_table_slot=x,
        activity_table_id=y,
        test_mode=True
    )
  
class Test_IRS:
    def test_IRS_ACTTAB(self, test_params, monkeypatch):

        # Define output directory for this test case
        new_output_base_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_IRS_ACTTAB_and_PAF")
        output_dir_for_satellite = os.path.join(new_output_base_dir, test_params.satellites[0])

        # Ensure the output directory exists
        os.makedirs(output_dir_for_satellite, exist_ok=True)

        # Temporarily change the output folder configuration for this test
        monkeypatch.setitem(config.CONFIG_VALUES, "output_folder", new_output_base_dir)

        # Generate CDC output
        generate_outputs(act_params=test_params)

        # Get the output directory
        output_dir = os.path.join(new_output_base_dir, test_params.satellites[0])

        # Test SSF file generation and content
        ssf_files = glob.glob(os.path.join(output_dir, "MTS*_ACTTAB_*.ssf"))
        assert len(ssf_files) == 1, f"Expected 1 SSF file, found {len(ssf_files)}"
        ssf_file = ssf_files[0]

        # Read current SSF output
        with open(ssf_file, 'r') as f:
            current_content = f.read().splitlines()

        # Read expected SSF output
        test_output_file = os.path.join("tests", "tests_assets", "IRS", "Input", "test_IRS_ACTTAB_and_PAF", "expected_output.ssf")
        with open(test_output_file, 'r') as f:
            expected_content = f.read().splitlines()

        # Compare SSF length and basic header
        assert len(current_content) == len(expected_content), "Output file has wrong number of lines"
        assert current_content[0].startswith("2|OPIT|"), "Invalid SSF file header"

        # Verify TC commands excluding timestamp
        current_commands = [line.split("|") for line in current_content if line.startswith("C|DSWC")]
        expected_commands = [line.split("|") for line in expected_content if line.startswith("C|DSWC")]
        assert len(current_commands) == len(expected_commands), "Wrong number of TC commands"

        for current, expected in zip(current_commands, expected_commands):
            # Compare command parts excluding timestamp
            assert current[1] == expected[1], "TC command mismatch"
            assert current[3:] == expected[3:], "TC parameters mismatch"

        # Get checksum from SSF file
        current_checksum = None
        for line in current_content:
            if "DSWH0AYX" in line:
                current_checksum = int(line.split("|")[5])
                break

        expected_checksum = None
        for line in expected_content:
            if "DSWH0AYX" in line:
                expected_checksum = int(line.split("|")[5])
                break

        assert current_checksum is not None, "Checksum parameter not found"
        assert current_checksum == expected_checksum, f"Expected checksum {expected_checksum}, got {current_checksum}"


    def test_IRS_SELUT_scae1_lut1(self, monkeypatch):
        # The test_params fixture is passed to ensure load_input_files("IRS") is called via its setup.
        # We define specific ActivityParams for this SELUT test.
        selut_params = ActivityParams(
            activity="Scan Encoder LUT",  # This should trigger IRS_SELUT via generate_outputs
            satellites=["MTG-S 1"],
            instrument="IRS",
            side="0",  # Default SCAE ID from sca_enc_gui.py
            scan_encode_correction_lut_id=3,  # Default LUT Index from sca_enc_gui.py
            # Ensure all other mandatory ActivityParams fields have defaults or are set here
            test_mode=True
        )

        # Define the new output directory for this test
        new_output_base_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_IRS_SELUT_scae1_lut1")
        output_dir_for_satellite = os.path.join(new_output_base_dir, selut_params.satellites[0])

        # Ensure the specific output directory for the satellite exists before generating outputs
        os.makedirs(output_dir_for_satellite, exist_ok=True)

        # Temporarily change the output folder configuration for this test
        monkeypatch.setitem(config.CONFIG_VALUES, "output_folder", new_output_base_dir)

        # Generate CDC output
        generate_outputs(act_params=selut_params)

        # Verify output file
        # The output_dir for glob should now be new_output_base_dir + satellite_name
        output_dir_for_glob = output_dir_for_satellite # Use the already defined and created path

        # Construct filename pattern based on IRS_SELUT.py
        # Example: MTS1_SCANENCLUT_TC_Stack_YYYYMMDDHHMMSS_ID0001.ssf
        satellite_number = selut_params.satellites[0].split(' ')[1]
        expected_file_pattern = os.path.join(
            output_dir_for_glob,  # Use the modified output directory for glob
            f"MTS{satellite_number}_SCANENCLUT_TC_Stack_*_ID{str(selut_params.scan_encode_correction_lut_id).zfill(4)}.ssf"
        )

        ssf_files = glob.glob(expected_file_pattern)

        assert len(ssf_files) == 1, (
            f"Expected 1 SSF file matching '{expected_file_pattern}', found {len(ssf_files)}.\n"
            f"Files found: {ssf_files}"
        )
        generated_ssf_file = ssf_files[0]

        # Verify the generated SSF file exists and is not empty
        assert os.path.exists(generated_ssf_file), f"Generated SSF file not found: {generated_ssf_file}"
        assert os.path.getsize(generated_ssf_file) > 0, f"Generated SSF file is empty: {generated_ssf_file}"



    def test_IRS_SL_generation(self, monkeypatch):
        """Test IRS Scan Law (SL) generation with specific parameters.

        This test verifies that:
        1. An SL binary file is generated with the correct naming pattern.
        2. The SL binary file is not empty.
        The generated output for this test will be saved in:
              tests/tests_assets/IRS/Output/test_IRS_SL_generation/MTG-S 1/
        """
        # Load input files for IRS - ensure this is done if not covered by a broader fixture
        load_input_files("IRS")

        # Define specific ActivityParams for this SL test
        sl_params = ActivityParams(
            activity="Mission Scenario (SL)",
            satellites=["MTG-S 1"],
            instrument="IRS",
            scan_law_id=0,
            mm_slot=1,
            activity_table_slot=1,
            activity_table_id=16384,
            test_mode=True
        )

        # Define the new output directory for this test
        new_output_base_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_IRS_SL_generation")
        output_dir_for_satellite = os.path.join(new_output_base_dir, sl_params.satellites[0])

        # Ensure the specific output directory for the satellite exists before generating outputs
        os.makedirs(output_dir_for_satellite, exist_ok=True)

        # Temporarily change the output folder configuration for this test
        monkeypatch.setitem(config.CONFIG_VALUES, "output_folder", new_output_base_dir)

        # Generate CDC output (which should include SL generation)
        generate_outputs(act_params=sl_params)

        # Verify output file
        output_dir_for_glob = output_dir_for_satellite

        # Construct filename pattern based on IRS_SL.py
        # Example: MTS1_IRS_YYYYMMDDHHMMSS_SL-MM1-SLID00000.bin
        satellite_number = sl_params.satellites[0].split(' ')[1]
        expected_file_pattern = os.path.join(
            output_dir_for_glob,
            f"MTS{satellite_number}_IRS_*_SL-MM{sl_params.mm_slot}-SLID{str(sl_params.scan_law_id).zfill(5)}.bin"
        )

        sl_files = glob.glob(expected_file_pattern)

        assert len(sl_files) == 1, (
            f"Expected 1 SL file matching '{expected_file_pattern}', found {len(sl_files)}.\n"
            f"Files found in '{output_dir_for_glob}': {glob.glob(os.path.join(output_dir_for_glob, '*'))}"
        )
        generated_sl_file = sl_files[0]

        # Verify the generated SL file exists and is not empty
        assert os.path.exists(generated_sl_file), f"Generated SL file not found: {generated_sl_file}"
        assert os.path.getsize(generated_sl_file) > 0, f"Generated SL file is empty: {generated_sl_file}"


    def test_IRS_SL_generation_16384_5(self, monkeypatch):
        """Test IRS Scan Law (SL) generation with scan_law_id=16384 and mm_slot=7.

        This test verifies that:
        1. An SL binary file is generated with the correct naming pattern.
        2. The SL binary file matches the expected output file byte-for-byte.
        The generated output for this test will be saved in:
              tests/tests_assets/IRS/Output/test_IRS_SL_generation_16384_5/MTG-S 1/
        The expected output is:
              tests/tests_assets/IRS/Input/test_IRS_SL_generation_16384_5/ScanLaw_West_Summer_16385.xldt
        """
        # Load input files for IRS
        load_input_files("IRS")

        # Define specific ActivityParams for this SL test
        sl_params = ActivityParams(
            activity="Mission Scenario (SL)",
            satellites=["MTG-S 1"],
            instrument="IRS",
            scan_law_id=16384,
            mm_slot=7,
            activity_table_slot=1,
            activity_table_id=16384,
            test_mode=True
        )

        # Define the new output directory for this test
        new_output_base_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_IRS_SL_generation_16384_5")
        output_dir_for_satellite = os.path.join(new_output_base_dir, sl_params.satellites[0])

        # Ensure the specific output directory for the satellite exists before generating outputs
        os.makedirs(output_dir_for_satellite, exist_ok=True)

        # Temporarily change the output folder configuration for this test
        monkeypatch.setitem(config.CONFIG_VALUES, "output_folder", new_output_base_dir)

        # Generate CDC output (which should include SL generation)
        generate_outputs(act_params=sl_params)

        # Verify output file
        output_dir_for_glob = output_dir_for_satellite

        # Construct filename pattern based on IRS_SL.py
        # Example: MTS1_IRS_YYYYMMDDHHMMSS_SL-MM7-SLID16384.bin
        satellite_number = sl_params.satellites[0][-1]
        expected_file_pattern = os.path.join(
            output_dir_for_glob,
            f"MTS{satellite_number}_IRS_*_SL-MM{sl_params.mm_slot}-SLID{str(sl_params.scan_law_id).zfill(5)}.bin"
        )

        sl_files = glob.glob(expected_file_pattern)

        assert len(sl_files) == 1, (
            f"Expected 1 SL file matching '{expected_file_pattern}', found {len(sl_files)}.\n"
            f"Files found in '{output_dir_for_glob}': {glob.glob(os.path.join(output_dir_for_glob, '*'))}"
        )
        generated_sl_file = sl_files[0]

        # Verify the generated SL file exists and is not empty
        assert os.path.exists(generated_sl_file), f"Generated SL file not found: {generated_sl_file}"
        assert os.path.getsize(generated_sl_file) > 0, f"Generated SL file is empty: {generated_sl_file}"

        # Compare with expected output file
        expected_file = os.path.join(
            "tests", "tests_assets", "IRS", "Input", "test_IRS_SL_generation_16384_5", "ScanLaw_West_Summer_16385.xldt"
        )
        assert os.path.exists(expected_file), f"Expected output file not found: {expected_file}"

        with open(generated_sl_file, "rb") as f_gen, open(expected_file, "rb") as f_exp:
            gen_bytes = f_gen.read()
            exp_bytes = f_exp.read()
            assert gen_bytes == exp_bytes, (
                f"Generated SL binary does not match expected output.\n"
                f"Generated: {generated_sl_file}\nExpected: {expected_file}\n"
                f"First difference at byte: {next((i for i, (a, b) in enumerate(zip(gen_bytes, exp_bytes)) if a != b), 'EOF' if len(gen_bytes) == len(exp_bytes) else min(len(gen_bytes), len(exp_bytes)))}"
            )


    def test_IRS_SAV_generation(self, monkeypatch):
        """Test SAV activity generation with a specific repeat sequence ID.

        This test verifies that for a given repeat_sequence_id:
        1. The correct activity_table_id is determined and used to generate an ACTTAB SSF file.
        2. A corresponding SAV PAF XML file is generated with the correct ID in its name.
        3. The content of the XML file correctly references the mapped activity_table_id.
        The generated output for this test will be saved in:
              tests/tests_assets/IRS/Output/test_IRS_SAV_generation/MTG-S 1/
        """
        # Load input files for IRS
        load_input_files("IRS")

        # Define specific ActivityParams for this SAV test
        sav_params = ActivityParams(
            activity="SAV",
            satellites=["MTG-S 1"],
            instrument="IRS",
            repeat_sequence_id=16513,
            test_mode=True
        )

        # Define the new output directory for this test
        new_output_base_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_IRS_SAV_generation")
        output_dir_for_satellite = os.path.join(new_output_base_dir, sav_params.satellites[0])

        # Ensure the specific output directory for the satellite exists and is empty
        os.makedirs(output_dir_for_satellite, exist_ok=True)
        self.empty_directory(output_dir_for_satellite)

        # Temporarily change the output folder configuration for this test
        monkeypatch.setitem(config.CONFIG_VALUES, "output_folder", new_output_base_dir)

        # Generate CDC output
        generate_outputs(act_params=sav_params)

        # --- Verification Step 1: Check for the generated ACTTAB SSF file and get the mapped ID ---
        ssf_files = glob.glob(os.path.join(output_dir_for_satellite, "MTS*_ACTTAB_ID*_*.ssf"))
        assert len(ssf_files) == 1, f"Expected 1 ACTTAB SSF file, found {len(ssf_files)}"
        
        # Extract the mapped activity_table_id from the SSF filename
        ssf_filename = os.path.basename(ssf_files[0])
        try:
            mapped_acttab_id_str = ssf_filename.split('_ID')[1].split('_')[0]
            mapped_acttab_id = int(mapped_acttab_id_str)
        except (IndexError, ValueError) as e:
            pytest.fail(f"Could not parse activity_table_id from SSF filename '{ssf_filename}': {e}")

        # --- Verification Step 2: Check for the generated SAV PAF XML file ---
        satellite_number = sav_params.satellites[0][-1]
        expected_xml_pattern = os.path.join(
            output_dir_for_satellite,
            f"PAF_MTS{satellite_number}+IRS_*_PAF_template-ID{sav_params.repeat_sequence_id}.xml"
        )
        xml_files = glob.glob(expected_xml_pattern)
        assert len(xml_files) == 1, f"Expected 1 SAV PAF XML file matching '{expected_xml_pattern}', found {len(xml_files)}"
        
        generated_xml_file = xml_files[0]
        assert os.path.exists(generated_xml_file), f"Generated XML file not found: {generated_xml_file}"
        assert os.path.getsize(generated_xml_file) > 0, f"Generated XML file is empty: {generated_xml_file}"

        # --- Verification Step 3: Verify content of the generated XML file ---
        tree = etree.parse(generated_xml_file)
        root = tree.getroot()

        # Find the value of REPSEQ_GRID using the correct XPath for the template
        repseq_grid_element = root.find(".//long[name='REPSEQ_GRID']/value")
        assert repseq_grid_element is not None, "REPSEQ_GRID parameter not found in generated XML"
        assert int(repseq_grid_element.text) == sav_params.repeat_sequence_id, "REPSEQ_GRID in XML does not match repeat_sequence_id"


    @staticmethod
    def empty_directory(path):
        for filename in os.listdir(path):
            file_path = os.path.join(path, filename)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)  # Remove file or symlink
            except Exception as e:
                logger.info(f"Failed to delete {file_path}: {e}")
