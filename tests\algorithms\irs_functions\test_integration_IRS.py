# Standard library imports
import os
import glob
import pytest
from lxml import etree
import logging
import tempfile
import shutil
from pathlib import Path
import netCDF4

import glob
# Local imports
from tests.helpers import load_input_files
def load_fci_excel_configs():
    """Load all FCI Excel config files into the cache for FCI tests."""
    config_dir = os.path.join("config", "FCI")
    excel_files = glob.glob(os.path.join(config_dir, "*.xlsx"))
    for file_path in excel_files:
        try:
            with open(file_path, "rb") as f:
                from src.utils.data_cache import cache
                cache.add_file(file_path, f.read())
        except Exception as e:
            print(f"Warning: Could not load {file_path} into cache: {e}")
from src.utils.import_utils import config
from src.functions import generate_outputs
from src.utils.activity_params import ActivityParams
from src.algorithms.irs_functions.IRS_PAF import IRS_SAV_PAF
from src.algorithms.irs_functions.IRS_ACT_TAB import IRS_ACT_TAB
from src.algorithms.irs_functions.IRS_launcher import determine_acttab_id_from_repseq_id, irs_functions_launcher


@pytest.fixture()
def test_params():
    """Fixture providing default test parameters for IRS tests."""
    # Load input files
    load_input_files("IRS", test_files_folder="tests/tests_assets")
    # Also load FCI Excel configs for any FCI-related tests
    load_fci_excel_configs()

    return ActivityParams(
        activity="Activity Table",
        satellites=["MTG-S 1"],
        instrument="IRS",
        activity_table_slot=1,
        activity_table_id=16384,
        test_mode=True
    )

@pytest.fixture(params=[
        {1, 128},
        {1, 256},
        {1, 16384},
        {1, 16640},
        {1, 16512},
        {2, 128},
        {2, 256},
        {2, 16384},
        {2, 16640},
        {2, 16512},
    ])
def test_params_value(request):
    """Fixture providing default test parameters for IRS tests."""
    # Load input files
    load_input_files("IRS", test_files_folder="tests/tests_assets")
    # Also load FCI Excel configs for any FCI-related tests
    load_fci_excel_configs()
    y, x = request.param
    return ActivityParams(
        activity="Activity Table",
        satellites=["MTG-S 1"],
        instrument="IRS",
        activity_table_slot=x,
        activity_table_id=y,
        test_mode=True
    )
  
class Test_IRS:
    def test_IRS_ACTTAB(self, test_params, monkeypatch):

        # Define output directory for this test case
        new_output_base_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_IRS_ACTTAB_and_PAF")
        output_dir_for_satellite = os.path.join(new_output_base_dir, test_params.satellites[0])

        # Ensure the output directory exists
        os.makedirs(output_dir_for_satellite, exist_ok=True)

        # Temporarily change the output folder configuration for this test
        monkeypatch.setitem(config.CONFIG_VALUES, "output_folder", new_output_base_dir)

        # Generate CDC output
        generate_outputs(act_params=test_params)

        # Get the output directory
        output_dir = os.path.join(new_output_base_dir, test_params.satellites[0])

        # Test SSF file generation and content
        ssf_files = glob.glob(os.path.join(output_dir, "MTS*_ACTTAB_*.ssf"))
        assert len(ssf_files) == 1, f"Expected 1 SSF file, found {len(ssf_files)}"
        ssf_file = ssf_files[0]

        # Read current SSF output
        with open(ssf_file, 'r') as f:
            current_content = f.read().splitlines()

        # Read expected SSF output
        test_output_file = os.path.join("tests", "tests_assets", "IRS", "Input", "test_IRS_ACTTAB", "expected_output.ssf")
        with open(test_output_file, 'r') as f:
            expected_content = f.read().splitlines()

        # Compare SSF length and basic header
        assert len(current_content) == len(expected_content), "Output file has wrong number of lines"
        assert current_content[0].startswith("2|OPIT|"), "Invalid SSF file header"

        # Verify TC commands excluding timestamp
        current_commands = [line.split("|") for line in current_content if line.startswith("C|DSWC")]
        expected_commands = [line.split("|") for line in expected_content if line.startswith("C|DSWC")]
        assert len(current_commands) == len(expected_commands), "Wrong number of TC commands"

        for current, expected in zip(current_commands, expected_commands):
            # Compare command parts excluding timestamp
            assert current[1] == expected[1], "TC command mismatch"
            assert current[3:] == expected[3:], "TC parameters mismatch"

        # Get checksum from SSF file
        current_checksum = None
        for line in current_content:
            if "DSWH0AYX" in line:
                current_checksum = int(line.split("|")[5])
                break

        expected_checksum = None
        for line in expected_content:
            if "DSWH0AYX" in line:
                expected_checksum = int(line.split("|")[5])
                break

        assert current_checksum is not None, "Checksum parameter not found"
        assert current_checksum == expected_checksum, f"Expected checksum {expected_checksum}, got {current_checksum}"


    def test_IRS_SELUT_scae1_lut1(self, monkeypatch):
        # The test_params fixture is passed to ensure load_input_files("IRS") is called via its setup.

        # We define specific ActivityParams for this SELUT test.
        selut_params = ActivityParams(
            activity="Scan Encoder LUT",  # This should trigger IRS_SELUT via generate_outputs
            satellites=["MTG-S 1"],
            instrument="IRS",
            side="0",  # Default SCAE ID from sca_enc_gui.py
            scan_encode_correction_lut_id=3,  # Default LUT Index from sca_enc_gui.py
            # Ensure all other mandatory ActivityParams fields have defaults or are set here
            test_mode=True
        )

        # Define the new output directory for this test
        new_output_base_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_IRS_SELUT_scae1_lut1")
        output_dir_for_satellite = os.path.join(new_output_base_dir, selut_params.satellites[0])

        # Ensure the specific output directory for the satellite exists before generating outputs
        os.makedirs(output_dir_for_satellite, exist_ok=True)
        self.empty_directory(output_dir_for_satellite)  # Clean up before running

        # Temporarily change the output folder configuration for this test
        monkeypatch.setitem(config.CONFIG_VALUES, "output_folder", new_output_base_dir)

        # Generate CDC output
        generate_outputs(act_params=selut_params)

        # Verify output file
        # The output_dir for glob should now be new_output_base_dir + satellite_name
        output_dir_for_glob = output_dir_for_satellite # Use the already defined and created path

        # Construct filename pattern based on IRS_SELUT.py
        # Example: MTS1_SCANENCLUT_TC_Stack_YYYYMMDDHHMMSS_ID0001.ssf
        satellite_number = selut_params.satellites[0].split(' ')[1]
        expected_file_pattern = os.path.join(
            output_dir_for_glob,  # Use the modified output directory for glob
            f"MTS{satellite_number}_SCANENCLUT_TC_Stack_*_ID{str(selut_params.scan_encode_correction_lut_id).zfill(4)}.ssf"
        )

        ssf_files = glob.glob(expected_file_pattern)

        assert len(ssf_files) == 1, (
            f"Expected 1 SSF file matching '{expected_file_pattern}', found {len(ssf_files)}.\n"
            f"Files found: {ssf_files}"
        )
        generated_ssf_file = ssf_files[0]

        # Verify the generated SSF file exists and is not empty
        assert os.path.exists(generated_ssf_file), f"Generated SSF file not found: {generated_ssf_file}"
        assert os.path.getsize(generated_ssf_file) > 0, f"Generated SSF file is empty: {generated_ssf_file}"


    def test_IRS_SAV_generation(self, monkeypatch):
        """Test SAV activity generation for all nominal (LAC_SKIP) repeat_sequence_ids using real mapping from activity_table_id."""
        # Load input files for IRS
        load_input_files("IRS", test_files_folder="tests/tests_assets")

        # Use a known activity_table_id that maps to all nominal repeat_sequence_ids
        activity_table_id = 16555
        nominal_repseq_ids = {0, 128, 256, 16384, 16512, 16640}
        tested_ids = []
        # Prepare ActivityParams
        sav_params = ActivityParams(
            activity="SAV",
            satellites=["MTG-S 1"],
            instrument="IRS",
            activity_table_id=activity_table_id,
            test_mode=True
        )
        # Use the real mapping function from the launcher
        from src.algorithms.irs_functions.IRS_launcher import determine_repseq_ids_from_acttab_id
        repseq_ids = determine_repseq_ids_from_acttab_id(sav_params, "MTG-S 1")
        # Only test the intersection with nominal
        for repseq_id in repseq_ids:
            if repseq_id not in nominal_repseq_ids:
                continue
            sav_params.repeat_sequence_id = repseq_id
            new_output_base_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_IRS_SAV_generation")
            output_dir_for_satellite = os.path.join(new_output_base_dir, sav_params.satellites[0])
            os.makedirs(output_dir_for_satellite, exist_ok=True)
            self.empty_directory(output_dir_for_satellite)
            monkeypatch.setitem(config.CONFIG_VALUES, "output_folder", new_output_base_dir)
            generate_outputs(act_params=sav_params)
            # Check for the generated SAV PAF XML file (should use LAC_SKIP template)
            satellite_number = sav_params.satellites[0][-1]
            xml_files = glob.glob(os.path.join(output_dir_for_satellite, f"PAF_MTS{satellite_number}+IRS_*.xml"))
            assert xml_files, f"No SAV PAF XML file generated for repeat_sequence_id {repseq_id}"
            for xml_file in xml_files:
                tree = etree.parse(xml_file)
                root = tree.getroot()
                # Check REPSEQ_GRID
                repseq_grid_element = root.find(".//long[name='REPSEQ_GRID']/value")
                assert repseq_grid_element is not None, "REPSEQ_GRID parameter not found in generated XML"
                assert int(repseq_grid_element.text) == repseq_id, f"REPSEQ_GRID in XML does not match repeat_sequence_id {repseq_id}"
                # Check <date> field is set (not default)
                date_elem = root.find(".//date[name='T_SAV_RS']/value")
                assert date_elem is not None, f"<date> element with name T_SAV_RS not found in {xml_file}"
                assert date_elem.text != "1970-001T00:00:00.000Z", f"<date> value was not updated: {date_elem.text} in {xml_file}"
            tested_ids.append(repseq_id)
        print(f"Tested nominal repeat_sequence_ids (via real mapping): {tested_ids}")


    @staticmethod
    def empty_directory(path):
        for filename in os.listdir(path):
            file_path = os.path.join(path, filename)
            try:
                if os.path.isfile(file_path):
                    os.unlink(file_path)  # Remove file or symlink
            except Exception as e:
                logger.info(f"Failed to delete {file_path}: {e}")


# Set up logging to capture logs for the test
@pytest.fixture(autouse=True)
def setup_logging(monkeypatch):
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    logger.addHandler(handler)
    yield
    logger.removeHandler(handler)


def test_IRS_SAV_acttabid_16555(tmp_path, caplog):
    # Prepare minimal ActivityParams for a nominal case
    act_params = ActivityParams()
    act_params.instrument = "IRS"
    act_params.activity = "SAV"
    act_params.satellites = ["MTS 1"]
    act_params.get_slicer_name = lambda: "activity_table_id"
    act_params.test_mode = True
    act_params.activity_table_id = 16555  # Set activity_table_id, let code determine repeat_sequence_id
    act_params.input_folder = str(tmp_path)

    # Set output directory to the test assets output folder
    output_dir = os.path.join("tests", "tests_assets", "IRS", "Output", "test_irs_sav_paf_date_update")
    os.makedirs(output_dir, exist_ok=True)
    config.CONFIG_VALUES["output_folder"] = output_dir

    # Pre-create the satellite subdirectory as in the working test
    output_dir_for_satellite = os.path.join(output_dir, act_params.satellites[0])
    os.makedirs(output_dir_for_satellite, exist_ok=True)

    # Copy required NetCDF files to the input folder
    test_asset_files = [
        "tests/tests_assets/IRS/Input/MTS1_IRS_REPSEQ_SAV_0001_1.nc",
        "tests/tests_assets/IRS/Input/MTS1_IRS_ACTTAB_0001_1.nc"
    ]
    for file in test_asset_files:
        shutil.copy(file, tmp_path)

    # Run the launcher
    with caplog.at_level(logging.INFO):
        irs_functions_launcher(act_params)

    # Check for output files
    output_path = Path(output_dir)
    acttab_files = list(output_path.rglob("*.ssf"))
    sav_paf_files = list(output_path.rglob("*.xml"))
    assert acttab_files, "No ACTTAB output generated"
    assert sav_paf_files, "No SAV PAF output generated"

    # Load the REPSEQ NetCDF file to get valid t_ref values
    repseq_nc_path = tmp_path / "MTS1_IRS_REPSEQ_SAV_0001_1.nc"
    with netCDF4.Dataset(repseq_nc_path, "r") as ds:
        repseq_ids = ds.variables["repeat_sequence_id"][:]
        t_refs = ds.variables["t_ref"][:]
        repseqid_to_tref = {int(repseq_id): int(t_ref) for repseq_id, t_ref in zip(repseq_ids, t_refs) if int(t_ref) != 0}

    # Optionally, check the <date> field in the SAV PAF output
    for xml_file in sav_paf_files:
        tree = etree.parse(str(xml_file))
        root = tree.getroot()
        # Find the REPSEQ_GRID value in the XML to match to repeat_sequence_id
        repseq_elem = root.find(".//parameter[name='REPSEQ_GRID']/value")
        if repseq_elem is not None:
            repseq_id = int(repseq_elem.text)
            if repseq_id in repseqid_to_tref:
                date_elem = root.find(".//date[name='T_SAV_RS']/value")
                assert date_elem is not None, f"<date> element with name T_SAV_RS not found in {xml_file}"
                assert date_elem.text != "1970-001T00:00:00.000Z", f"<date> value was not updated: {date_elem.text} in {xml_file}"
            else:
                # No valid t_ref for this repseq_id, skip assertion
                print(f"Skipping <date> check for repseq_id {repseq_id} in {xml_file} (no valid t_ref)")
