"""
FCI Functions Package
===================

This package contains functions for the Flexible Combined Imager (FCI) instrument.
"""

# Import individual functions to avoid circular imports
from .FCI_SELUT import FCI_SELUT
from .FCI_APC import FCI_APC
from .FCI_SL import FCI_SL
from .FCI_PAF import FCI_PAF
from .FCI_VCU_MEM_NUMOFF import FCI_VCU_MEM_NUMOFF
from .FCI_VCU_MEM_PIXMAP import FCI_VCU_MEM_PIXMAP

# Import launcher last to avoid circular imports
from .FCI_launcher import fci_functions_launcher

__all__ = [
    'fci_functions_launcher',
    'FCI_SELUT',
    'FCI_APC',
    'FCI_SL',
    'FCI_PAF',
    'FCI_VCU_MEM_NUMOFF',
    'FCI_VCU_MEM_PIXMAP'
]