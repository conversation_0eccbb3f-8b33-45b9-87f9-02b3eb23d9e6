#!/usr/bin/env python3
"""
Test the XLDT reader with the correct scan law file (16386) that matches expected pattern.
"""

import os
import sys
from xldt_reader_standalone import <PERSON><PERSON><PERSON><PERSON>er

def test_correct_scanlaw():
    """Test XLDT reader with scan law 16386."""
    
    xldt_file = "inputs/ScanLaw_East_Summer_16386.xldt"
    config_path = "../config/IRS/IRS_SL_Conf.csv"
    
    if not os.path.exists(xldt_file):
        print(f"❌ XLDT file not found: {xldt_file}")
        return
    
    print("🔍 Testing XLDT Reader with Correct Scan Law (16386)")
    print("=" * 60)
    
    try:
        reader = XLDTReader(config_path=config_path)
        parsed_data = reader.read_xldt_file(xldt_file)
        
        # Format in CDC style
        from xldt_reader_standalone import format_xldt_cdc_input_style
        cdc_output = format_xldt_cdc_input_style(parsed_data)
        
        print(cdc_output)
        
        print(f"\n✅ Expected pattern verification:")
        print(f"   Expected: [11, 5, 1, 1, 1, 1, 1, 1, 7, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 7, 2, 1, 1, 1, 1, 1, 1, 1, 10, 11, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255, 255]")
        
        # Load the saved matching data
        if os.path.exists("matching_scanlaw_16386_data.txt"):
            print(f"\n📄 Saved NetCDF data for scan law 16386:")
            with open("matching_scanlaw_16386_data.txt", "r") as f:
                content = f.read()
                print(content)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_correct_scanlaw()
