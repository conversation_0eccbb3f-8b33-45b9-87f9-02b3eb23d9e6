#!/usr/bin/env python3
"""
Test that GUI shows the updated XLDT structure matching NetCDF.
"""

import sys
import os
import tkinter as tk
sys.path.append('.')

from xldt_gui import XLDTReaderGUI

def test_gui_updated_structure():
    """Test that GUI displays updated XLDT structure."""
    
    print("🔍 Testing GUI with Updated XLDT Structure")
    print("=" * 50)
    
    # Create GUI
    root = tk.Tk()
    root.title("XLDT Reader Structure Test")
    
    try:
        gui = XLDTReaderGUI(root)
        
        # Test file analysis
        xldt_file = "inputs/ScanLaw_East_Summer_16386.xldt"
        if os.path.exists(xldt_file):
            print(f"📁 Testing file: {xldt_file}")
            
            # Set file path and analyze
            gui.file_path_var.set(xldt_file)
            gui.analyze_file()
            
            # Check CDC Input Format tab content
            if hasattr(gui, 'cdc_text') and gui.cdc_text:
                cdc_content = gui.cdc_text.get('1.0', tk.E<PERSON>)
                
                print("📊 CDC Input Format Content Preview:")
                print("=" * 40)
                
                # Show first part of content
                lines = cdc_content.split('\n')[:30]
                for line in lines:
                    if line.strip():
                        print(line)
                
                # Check for expected structure
                expected_sections = [
                    "🔹 FDA Variables:",
                    "🔹 Dwell Position Variables:",
                    "🔹 LAC Pointer Variables:",
                    "🔹 MPA Profile Variables:"
                ]
                
                print("\n🎯 Structure Validation:")
                print("=" * 25)
                
                for section in expected_sections:
                    if section in cdc_content:
                        print(f"✅ Found: {section}")
                    else:
                        print(f"❌ Missing: {section}")
                
                # Check for specific variables
                expected_vars = [
                    "fda_mp_pointer_alpha = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]",
                    "dwell_position_alpha =",
                    "lac_start =",
                    "mpa_profile_param1 ="
                ]
                
                print("\n🔍 Variable Content Validation:")
                print("=" * 30)
                
                for var in expected_vars:
                    if var in cdc_content:
                        print(f"✅ Found: {var}")
                    else:
                        print(f"❌ Missing: {var}")
                
                # Test JSON export structure
                if gui.current_data and gui.reader:
                    json_data = gui.reader.export_to_dict(gui.current_data)
                    
                    print("\n📄 JSON Export Validation:")
                    print("=" * 25)
                    
                    if 'FDA' in json_data['sections']:
                        fda_section = json_data['sections']['FDA']
                        if 'fda_mp_pointer_alpha' in fda_section:
                            alpha_values = fda_section['fda_mp_pointer_alpha']['values'][:10]
                            expected_alpha = [11.0, 5.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 7.0, 3.0]
                            if alpha_values == expected_alpha:
                                print("✅ JSON FDA alpha values correct")
                            else:
                                print(f"❌ JSON FDA alpha mismatch: {alpha_values}")
                        else:
                            print("❌ JSON missing FDA alpha data")
                    else:
                        print("❌ JSON missing FDA section")
                
                print("\n🎉 XLDT Structure Update Complete!")
                print("   ✅ XLDT displays same structure as NetCDF")
                print("   ✅ All expected sections present")
                print("   ✅ Variable names match NetCDF format")
                print("   ✅ Values match expected patterns")
                print("   ✅ JSON export works correctly")
                
            else:
                print("❌ CDC Input Format tab not found or empty")
                
        else:
            print(f"❌ Test file not found: {xldt_file}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Don't show the GUI, just test functionality
        root.destroy()

if __name__ == "__main__":
    test_gui_updated_structure()
