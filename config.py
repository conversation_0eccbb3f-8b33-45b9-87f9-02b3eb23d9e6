import ast
import os
import platform  # Added import
from configparser import Config<PERSON>arser
from src.logger_wrapper import logger

CONFIG_FILE = os.path.join(os.path.dirname(__file__), "config", "config.ini")


def populate_config_variables():
    """Reads configuration file config_file.ini and populate a global dictionary with the values"""

    global CONFIG_VALUES
    CONFIG_VALUES = {}

    logger.info("Load configuration file Start")

    # Get location this file
    str_path = os.path.dirname(__file__)

    # Read and parse Configuration File, reading from the same folder of this wonderful script
    parser = ConfigParser()
    parser.read(CONFIG_FILE)

    # Introduce information
    CONFIG_VALUES["tool_name"] = parser["tool"]["tool_name"]
    CONFIG_VALUES["input_source"] = ast.literal_eval(parser["tool"]["input_source"])
    CONFIG_VALUES["instruments"] = ast.literal_eval(parser["tool"]["instruments"])
    CONFIG_VALUES["dict_mem_header"] = ast.literal_eval(
        parser["tool"]["dict_mem_header"]
    )

    # Get info for the external connections
    # Determine ipssr_url based on hostname
    hostname = platform.uname().node
    # if hostname in ["esoeopd-v01", "esoeopd-v02", "esoeopd-v03"]:
    #     CONFIG_VALUES["ipssr_url"] = "https://esoeifd-v01.tt.mtg/"
    # elif hostname in ["esoeopv-v01", "esoeopv-v02", "NB02-CJLVB94"]:
    #     CONFIG_VALUES["ipssr_url"] = "https://esoeifv-v01.tt.mtg/"
    # elif hostname in ["esoeopo-v01", "esoeopo-v02"]:
    CONFIG_VALUES["ipssr_url"] = "https://esoeifo-v01.tt.mtg/"
    # else:
    #     # Default URL if no match, or you can raise an error or use a default from config.ini
    #     CONFIG_VALUES["ipssr_url"] = parser["connections"]["ipssr_url"]  # Fallback to config.ini
    #     logger.warning(
    #         f"Hostname {hostname} not in defined mappings, using default ipssr_url from config.ini: {CONFIG_VALUES['ipssr_url']}"
    #     )

    CONFIG_VALUES["gitlab_fci"] = parser["connections"]["gitlab_fci"]
    CONFIG_VALUES["gitlab_li"] = parser["connections"]["gitlab_li"]
    CONFIG_VALUES["gitlab_irs"] = parser["connections"]["gitlab_irs"]
    CONFIG_VALUES["gitlab_uvn"] = parser["connections"]["gitlab_uvn"]

    # Input files
    CONFIG_VALUES["output_folder"] = parser["inputs"]["output_folder"]
    CONFIG_VALUES["log_folder"] = parser["inputs"]["log_folder"]
    CONFIG_VALUES["input_folder"] = parser["inputs"]["input_folder"]
    CONFIG_VALUES["instrument_config"] = os.path.join(
        str_path, parser["inputs"]["instrument_config"]
    )
    CONFIG_VALUES["paf_folder"] = os.path.join(str_path, parser["inputs"]["paf_folder"])
    CONFIG_VALUES["config_folder"] = os.path.join(
        str_path, parser["inputs"]["config_folder"]
    )
    CONFIG_VALUES["test_files"] = os.path.join(str_path, parser["inputs"]["test_files"])

    # Python helpers location
    CONFIG_VALUES["basics"] = parser["python_helpers"]["basics"]
    CONFIG_VALUES["schema"] = parser["python_helpers"]["schema"]

    # Get templates names
    CONFIG_VALUES["li_icid_ram_paf"] = parser["templates"]["li_icid_ram_paf"]
    CONFIG_VALUES["li_patch_ram_paf"] = parser["templates"]["li_patch_ram_paf"]
    CONFIG_VALUES["li_icid_flash_paf"] = parser["templates"]["li_icid_flash_paf"]
    CONFIG_VALUES["li_patch_flash_paf"] = parser["templates"]["li_patch_flash_paf"]
    CONFIG_VALUES["li_lme_patch_paf"] = parser["templates"]["li_lme_patch_paf"]

    logger.info("Load configuration file End")


# Read Config variables
populate_config_variables()


def populate_config_instrument():
    """Read configuration from the specific instrument file"""
    # Initialize dictionary
    global CONFIG_INSTRUMENT
    CONFIG_INSTRUMENT = {}

    logger.info("Load configuration file for instrument Start")

    # Read and parse Configuration File, reading from the same folder of this wonderful script
    parser = ConfigParser()
    parser.read(CONFIG_VALUES["instrument_config"])

    for str_instrument in CONFIG_VALUES["instruments"]:
        # Reset Variables
        dict_step = {}

        # Introduce information
        dict_step["count"] = int(parser[str_instrument]["count"])
        dict_step["count_max"] = int(parser[str_instrument]["count_max"])
        dict_step["file_class"] = parser[str_instrument]["file_class"]
        dict_step["slicer_dict"] = ast.literal_eval(
            parser[str_instrument]["slicer_dict"]
        )
        dict_step["common_dict"] = ast.literal_eval(
            parser[str_instrument]["common_dict"]
        )
        dict_step["input_folder"] = os.path.join(
            CONFIG_VALUES["input_folder"], parser[str_instrument]["input_folder"]
        )
        dict_step["vsm_path"] = os.path.join(
            CONFIG_VALUES["config_folder"],
            str_instrument,
            parser[str_instrument]["vsm_file"],
        )
        dict_step["satellites"] = ast.literal_eval(parser[str_instrument]["satellites"])

        dict_step["cdc_options"] = ast.literal_eval(
            parser[str_instrument]["cdc_options"]
        )

        # Some instrument specific info
        if str_instrument == "FCI":
            dict_step["channel_names"] = ast.literal_eval(
                parser[str_instrument]["channel_names"]
            )
        if str_instrument == "LI":
            dict_step["par_name"] = ast.literal_eval(parser[str_instrument]["par_name"])
            dict_step["tc_name"] = ast.literal_eval(parser[str_instrument]["tc_name"])

        # Add instrument configuration to global dictionary
        CONFIG_INSTRUMENT[str_instrument] = dict_step

    logger.info("Load configuration file Instrument End")


# Read Instrument configuration
populate_config_instrument()
