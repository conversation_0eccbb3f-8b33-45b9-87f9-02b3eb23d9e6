[ptd]

# uvn netcat file syntax
uvn_input_file_pattern='*PTD2CDC*.nc'

# uvn possible icu values
uvn_icu=['ICU_A','ICU_B']

# uvn gui possible values for software
uvn_software=['ASW','SSW']

# uvn gui possible values for memory device
uvn_memory_device=['SDRAM','EEPROM_A','EEPROM_B']

#memory image header values
dict_mem_header = {"ID":1,"VERSION":1.0,"DOMAIN":"","TYPE":"PATCH","DESCRIPTION":"","SOURCE":"CDC SOET","CREATIONDATE":"","MODEL":"","MODELVER":"","DEVICE":"","STARTADDR":"","ENDADDR":"","LENGTH":"","CHECKSUM":"","UNIT":""}

# mapping of types from netcdf to execel type
map_type_xls2={"Uns8":["unsigned integer",8],"Uns16":["unsigned integer",16],"Uns32":["unsigned integer",32],"Int32":["signed integer",32],"Float32":["float",32]}

#ptd starting memory address
ptd_start={"EEPROM_B":'0x10100000',"EEPROM_A":'0x10200000',"SDRAM":'0x400FF810'}#to allow address conversion from SDRAM to EEPROM img0=A/img1=B addresses


#netcdf header
lst_head_print=['DOMAIN','TYPE','DESCRIPTION','CREATIONDATE','MODEL','MODELVER','DEVICE','STARTADDR','ENDADDR','LENGTH','CHECKSUM','UNIT']

#min:8, max:15
var_format="{:.8e}"

#mapping of types from excel to netCDF
map_type_xls2net={"Uns8":'uint8',"Uns16":'uint16',"Uns32":'uint32',"Int32":'int32',"Float32":'float32'}

#mapping of types from excel to memory image
map_type_xls2img={"Uns8":['unsigned integer',8],"Uns16":['unsigned integer',16], "Uns32":['unsigned integer',32],"Int32":['signed integer',32],"Float32":['float',32]}

#ptd mapping tables
map_ptd_tables=['7-1','7-2','7-3','7-4','7-5','7-6','7-7','7-8','7-9']

#ptd layout description
ptd_layout={ '2-6':{'title':'00_Redundancy_Definition_Table','addr':'0x400FF810','N_pad':0,'N_row':8,'N_col':1,'N_len':16,'def':'Value:Uns16[0..10]'},
    '4-1':{'title':'01_Mode_Transition_Table','addr':'0x400FF820','N_pad':0,'N_row':12,'N_col':12,'N_len':144,'def':'SBY_Allow:Uns8[0..1],SBY_Mode_Transition_Symbol:Uns8[min..max],SBR_Allow:Uns8[0..1],SBR_Mode_Transition_Symbol:Uns8[min..max],IDL_Allow:Uns8[0..1],IDL_Mode_Transition_Symbol:Uns8[min..max],IDR_Allow:Uns8[0..1],IDR_Mode_Transition_Symbol:Uns8[min..max],DCM_Allow:Uns8[0..1],DCM_Mode_Transition_Symbol:Uns8[min..max],MSM_Allow:Uns8[0..1],MSM_Mode_Transition_Symbol:Uns8[min..max]'},
    '4-2':{'title':'02_Allowed_TC_Table','addr':'0x400FF8B0','N_pad':0,'N_row':200,'N_col':4,'N_len':1600,'def':'TC_Type:Uns8[min..max],TC_Subtype:Uns8[min..max],Function_ID:Uns32[min..max],Mode_Word:Uns16[min..max]'},
    '2-7':{'title':'03_Critical_Command_Table','addr':'0x400FFEF0','N_pad':0,'N_row':20,'N_col':3,'N_len':120,'def':'TC_Type:Uns8[min..max],TC_Subtype:Uns8[min..max],Function_ID:Uns32[min..max]'},
    '5-1':{'title':'04_Thermal_Calibration_Coefficient_Table','addr':'0x400FFF68','N_pad':0,'N_row':380,'N_col':3,'N_len':2280,'def':'Coef_Set:Uns8[min..max],Coef_ID:Uns8[min..max],Coef_Value:Float32[min..max]'},
    '5-2':{'title':'05_Thermistor_Calibration_Table','addr':'0x40100850','N_pad':0,'N_row':76,'N_col':2,'N_len':152,'def':'Algorithm_ID:Uns8[min..max],Calib_Coef_Set:Uns8[min..max]'},
    '5-3':{'title':'06_Thermal_Control_Definition_Table','addr':'0x401008E8','N_pad':0,'N_row':144,'N_col':14,'N_len':4608,'def':'Thermistor_ID_1:Uns8[1..76],Thermistor_ID_2:Uns8[1..76],Thermistor_ID_3:Uns8[1..76],Assigned_Heater_Chan:Uns8[101..124],Heater_Chan_Output_Type:Uns8[1..2],Max_Heater_Power_Output:Float32[0.000..Float32_TLast],Thermistor_Input_Preproc:Uns8[1..7],Control_Law_Coef_Alpha:Float32[min..max],Control_Law_Coef_Beta:Float32[min..max],Control_Law_Coef_Gamma:Float32[min..max],Controller_Set_Point:Float32[min..max],Ctrl_Loop_Validity_Cond:Uns8[0..6],Heater_Cmd_Source:Uns8[0..1],Heater_Static_Setting:Float32[0.000..1.000]'},
    '5-4':{'title':'07_Thermistor_Resistance_Coefficients_Table','addr':'0x40101AE8','N_pad':0,'N_row':76,'N_col':3,'N_len':912,'def':'Coefficient0:Float32[min..max],Coefficient1:Float32[min..max],Coefficient2:Float32[min..max]'},
    '6-1':{'title':'08_CAA_General_Control_Parameters_Table','addr':'0x40101E78','N_pad':0,'N_row':1,'N_col':11,'N_len':44,'def':'WLSMirrorAngle:Float32[0.000..359.999],NominalDiffuserAngle:Float32[0.000..359.999],ReferenceDiffuserAngle:Float32[0.000..359.999],ThermistorAdcMin:Int32[0..2480],ThermistorAdcMax:Int32[0..2480],MovementTimeout:Uns32[0..3000],VelocityLimit:Uns32[0..1954],MotorGain:Uns32[0..64],CreepSpeed:Uns32[0..63],MaxCorrectionAngle:Float32[0.000..10.000],CorrTimePerStep:Uns32[0..3000]'},
    '6-2':{'title':'09_CAA_Resolver_Lookup_Table','addr':'0x40101EA4','N_pad':0,'N_row':360,'N_col':4,'N_len':5040,'def':'Data_Pair_ID:Uns16[1..360],Raw_Reading:Float32[min..max],Corrected_Reading:Float32[min..max],Resolution:Float32[min..max]'},
    '6-3':{'title':'10_CaaNominalMotionProfile','addr':'0x40103254','N_pad':1,'N_row':3,'N_col':5,'N_len':51,'def':'AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]'},
    '6-4':{'title':'11_CaaFastMotionProfile','addr':'0x40103288','N_pad':1,'N_row':3,'N_col':5,'N_len':51,'def':'AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]'},
    '6-7':{'title':'12_ACV_General_Control_Parameters_Table','addr':'0x401032BC','N_pad':0,'N_row':1,'N_col':35,'N_len':140,'def':'SummerClosedDefaultPos:Uns32[0..2047],SummerOpenDefaultPos:Uns32[0..2047],WinterClosedDefaultPos:Uns32[0..2047],WinterOpenDefaultPos:Uns32[0..2047],PositionLimitMax:Uns32[0..2047],PositionLimitMin:Uns32[0..2047],RecoveryVelocity:Uns32[0..2000000],CreepSpeed:Uns32[0..63],LowVelocityLimit:Uns32[0..2000000],ProfileVelocityLimit:Uns32[0..1954],ConstantVelocityLimit:Uns32[0..1954],RecoveryTimeout:Uns32[0..3000],NominalTimeout:Uns32[0..3000],EQSOLTimeout:Uns32[0..3000],NomSummerClosedSwitchPos:Uns32[0..131008],NomSummerOpenSwitchPos:Uns32[0..131008],NomWinterClosedSwitchPos:Uns32[0..131008],NomWinterOpenSwitchPos:Uns32[0..131008],RedSummerClosedSwitchPos:Uns32[0..131008],RedSummerOpenSwitchPos:Uns32[0..131008],RedWinterClosedSwitchPos:Uns32[0..131008],RedWinterOpenSwitchPos:Uns32[0..131008],ClosedPosRangeMin:Uns32[0..2047],ClosedPosRangeMax:Uns32[0..2047],OpenPosRangeMin:Uns32[0..2047],OpenPosRangeMax:Uns32[0..2047],SummerClosedTargetPos:Uns32[0..2047],SummerOpenTargetPos:Uns32[0..2047],WinterClosedTargetPos:Uns32[0..2047],WinterOpenTargetPos:Uns32[0..2047],SummerCoilResistance:Uns32[0..1999],WinterCoilResistance:Uns32[0..1999],MotorPower:Float32[0.000..19.900],ThermistorAdcMin:Int32[0..2480],ThermistorAdcMax:Int32[0..2480]'},
    '6-8':{'title':'13_AcvSummerNormalClose','addr':'0x40103348','N_pad':1,'N_row':3,'N_col':5,'N_len':51,'def':'AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]'},
    '6-9':{'title':'14_AcvSummerNormalOpen','addr':'0x4010337C','N_pad':1,'N_row':3,'N_col':5,'N_len':51,'def':'AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]'},
    '6-10':{'title':'15_AcvWinterNormalClose','addr':'0x401033B0','N_pad':1,'N_row':3,'N_col':5,'N_len':51,'def':'AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]'},
    '6-11':{'title':'16_AcvWinterNormalOpen','addr':'0x401033E4','N_pad':1,'N_row':3,'N_col':5,'N_len':51,'def':'AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]'},
    '6-12':{'title':'17_AcvSummerEmergencyClose','addr':'0x40103418','N_pad':1,'N_row':3,'N_col':5,'N_len':51,'def':'AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]'},
    '6-13':{'title':'18_AcvWinterEmergencyClose','addr':'0x4010344C','N_pad':1,'N_row':3,'N_col':5,'N_len':51,'def':'AccelerationDerivative:Uns32[0..4095],AccelerationRampTime:Uns32[0..31],AccelerationFlatTime:Uns32[0..511],TriggerOrLength:Uns32[0..131071],AccelDir:Uns8[0..1]'},
    '6-14':{'title':'19_Light_Sources_Control_Parameters','addr':'0x40103480','N_pad':0,'N_row':2,'N_col':5,'N_len':32,'def':'LedOff:Float32[0.0..20.0],LedOn:Float32[0.0..20.0],WlsOn:Uns16[0..4095],WlsDelta:Uns16[0..4095],WlsMinOnTime:Float32[0.0..3600.0]'},
    '6-15':{'title':'20_CCD_Operating_Voltages','addr':'0x401034A0','N_pad':2,'N_row':3,'N_col':5,'N_len':30,'def':'Clock1High:Uns16[16#0000#..16#3FFF#],Clock2High:Uns16[16#0000#..16#3FFF#],BiasOutputDrain:Uns16[16#0000#..16#3FFF#],BiasResetDrain:Uns16[16#0000#..16#3FFF#],BiasOutputGate:Uns16[16#0000#..16#3FFF#]'},
    '6-16':{'title':'21_EwAxisAcceleration','addr':'0x401034C0','N_pad':0,'N_row':60,'N_col':2,'N_len':360,'def':'EwAxisAcceleration_ID:Uns16[min..max],EwAxisAcceleration:Float32[0.0..Float32_TLast]'},
    '6-17':{'title':'22_EwAxisVelocity','addr':'0x40103628','N_pad':0,'N_row':60,'N_col':2,'N_len':360,'def':'EwAxisVelocity_ID:Uns16[min..max],EwAxisVelocity:Float32[0.0..Float32_TLast]'},
    '6-18':{'title':'23_EwAxisDeceleration','addr':'0x40103790','N_pad':0,'N_row':60,'N_col':2,'N_len':360,'def':'EwAxisDeceleration_ID:Uns16[min..max],EwAxisDeceleration:Float32[0.0..Float32_TLast]'},
    '6-19':{'title':'24_NsAxisAcceleration','addr':'0x401038F8','N_pad':0,'N_row':60,'N_col':2,'N_len':360,'def':'NsAxisAcceleration_ID:Uns16[min..max],NsAxisAcceleration:Float32[0.0..Float32_TLast]'},
    '6-20':{'title':'25_NsAxisVelocity','addr':'0x40103A60','N_pad':0,'N_row':60,'N_col':2,'N_len':360,'def':'NsAxisVelocity_ID:Uns16[min..max],NsAxisVelocity:Float32[0.0..Float32_TLast]'},
    '6-21':{'title':'26_NsAxisDeceleration','addr':'0x40103BC8','N_pad':0,'N_row':60,'N_col':2,'N_len':360,'def':'NsAxisDeceleration_ID:Uns16[min..max],NsAxisDeceleration:Float32[0.0..Float32_TLast]'},
    '6-22':{'title':'27_EwAxisAngleMapping','addr':'0x40103D30','N_pad':0,'N_row':500,'N_col':2,'N_len':3000,'def':'EwAxisAngleMapping_ID:Uns16[min..max],EwAxisAngleMapping_Value:Float32[min..max]'},
    '6-23':{'title':'28_NsAxisAngleMapping','addr':'0x401048E8','N_pad':0,'N_row':256,'N_col':2,'N_len':1280,'def':'NsAxisAngleMapping_ID:Uns8[min..max],NsAxisAngleMapping_Value:Float32[min..max]'},
    '6-24':{'title':'29_EwDeltaAngle','addr':'0x40104DE8','N_pad':0,'N_row':16,'N_col':2,'N_len':96,'def':'EwDeltaAngle_Delta_Angle_ID:Uns16[min..max],EwDeltaAngle_Delta_Angle_Value:Float32[min..max]'},
    '6-25':{'title':'30_NsDeltaAngle','addr':'0x40104E48','N_pad':0,'N_row':16,'N_col':2,'N_len':96,'def':'NsDeltaAngle_Delta_Angle_ID:Uns16[min..max],NsDeltaAngle_Delta_Angle_Value:Float32[min..max]'},
    '6-26':{'title':'31_RawDataValidityCheckLimits','addr':'0x40104EA8','N_pad':0,'N_row':6,'N_col':1,'N_len':12,'def':'Value:Uns16[min..max]'},
    '6-27':{'title':'32_SDE_Alarms_Configuration_Table','addr':'0x40104EB4','N_pad':3,'N_row':5,'N_col':3,'N_len':25,'def':'Configuration_ID:Uns8[min..max],Mask_Word:Uns16[min..max],Enable_Disable_Word:Uns16[min..max]'},
    '7-1':{'title':'33_ICID_Configurator_Table','addr':'0x40104ED0','N_pad':0,'N_row':1000,'N_col':10,'N_len':14000,'def':'ICID_No:Uns16[min..max],ICID_Ver:Uns16[min..max],PSN1:Uns16[min..max],PSN2:Uns8[min..max],PSN3:Uns8[min..max],PSN4:Uns8[min..max],PSN5:Uns8[min..max],PSN6:Uns16[min..max],PSN7:Uns8[min..max],PSN8:Uns8[min..max]'},
    '7-2':{'title':'34_FPS_Electronics_State_Parameters','addr':'0x40108580','N_pad':0,'N_row':100,'N_col':8,'N_len':1500,'def':'PSN:Uns16[min..max],Usage:Uns8[min..max],Frame_Period_Duration_UVVIS1:Uns16[min..max],Frame_Period_Duration_UVVIS2:Uns16[min..max],Frame_Period_Duration_NIR:Uns16[min..max],UVVIS1_Usage:Uns16[min..max],UVVIS2_Usage:Uns16[min..max],NIR_Usage:Uns16[min..max]'},
    '7-3':{'title':'35_NIR_Binning_Zones_Definition_Table','addr':'0x40108B5C','N_pad':0,'N_row':100,'N_col':13,'N_len':2500,'def':'PSN:Uns8[min..max],Binning_Line_Zone1_Start:Uns16[min..max],Binning_Line_Zone1_Rng:Uns16[min..max],Binning_Line_Zone1_Value:Uns16[min..max],Binning_Line_Zone2_Start:Uns16[min..max],Binning_Line_Zone2_Rng:Uns16[min..max],Binning_Line_Zone2_Value:Uns16[min..max],Binning_Line_Zone3_Start:Uns16[min..max],Binning_Line_Zone3_Rng:Uns16[min..max],Binning_Line_Zone3_Value:Uns16[min..max],Binning_Line_Zone4_Start:Uns16[min..max],Binning_Line_Zone4_Rng:Uns16[min..max],Binning_Line_Zone4_Value:Uns16[min..max]'},
    '7-4':{'title':'36_UVVIS1_Binning_Zones_Definition_Table','addr':'0x40109520','N_pad':0,'N_row':100,'N_col':13,'N_len':2500,'def':'PSN:Uns8[min..max],Binning_Line_Zone1_Start:Uns16[min..max],Binning_Line_Zone1_Rng:Uns16[min..max],Binning_Line_Zone1_Value:Uns16[min..max],Binning_Line_Zone2_Start:Uns16[min..max],Binning_Line_Zone2_Rng:Uns16[min..max],Binning_Line_Zone2_Value:Uns16[min..max],Binning_Line_Zone3_Start:Uns16[min..max],Binning_Line_Zone3_Rng:Uns16[min..max],Binning_Line_Zone3_Value:Uns16[min..max],Binning_Line_Zone4_Start:Uns16[min..max],Binning_Line_Zone4_Rng:Uns16[min..max],Binning_Line_Zone4_Value:Uns16[min..max]'},
    '7-5':{'title':'37_UVVIS2_Binning_Zones_Definition_Table','addr':'0x40109EE4','N_pad':0,'N_row':100,'N_col':13,'N_len':2500,'def':'PSN:Uns8[min..max],Binning_Line_Zone1_Start:Uns16[min..max],Binning_Line_Zone1_Rng:Uns16[min..max],Binning_Line_Zone1_Value:Uns16[min..max],Binning_Line_Zone2_Start:Uns16[min..max],Binning_Line_Zone2_Rng:Uns16[min..max],Binning_Line_Zone2_Value:Uns16[min..max],Binning_Line_Zone3_Start:Uns16[min..max],Binning_Line_Zone3_Rng:Uns16[min..max],Binning_Line_Zone3_Value:Uns16[min..max],Binning_Line_Zone4_Start:Uns16[min..max],Binning_Line_Zone4_Rng:Uns16[min..max],Binning_Line_Zone4_Value:Uns16[min..max]'},
    '7-6':{'title':'38_UVVIS1_Readout_Gain_Zones_Configuration_Table','addr':'0x4010A8A8','N_pad':0,'N_row':100,'N_col':9,'N_len':1700,'def':'PSN:Uns8[min..max],Gain_Line_Zone1_Start:Uns16[min..max],Gain_Line_Zone1_Rng:Uns16[min..max],Gain_Line_Zone2_Start:Uns16[min..max],Gain_Line_Zone2_Rng:Uns16[min..max],Gain_Line_Zone3_Start:Uns16[min..max],Gain_Line_Zone3_Rng:Uns16[min..max],Gain_Line_Zone4_Start:Uns16[min..max],Gain_Line_Zone4_Rng:Uns16[min..max]'},
    '7-7':{'title':'39_Science_Data_Processing_Parameters_Table','addr':'0x4010AF4C','N_pad':0,'N_row':1024,'N_col':13,'N_len':26624,'def':'PSN:Uns16[min..max],Coadding_Factor_UVVIS1:Uns8[min..max],Coadding_Factor_UVVIS2:Uns8[min..max],Coadding_Factor_NIR:Uns8[min..max],Data_Width_UVVIS1:Uns8[min..max],Data_Width_UVVIS2:Uns8[min..max],Data_Width_NIR:Uns8[min..max],Video_Size_UVVIS1:Uns32[min..max],Video_Size_UVVIS2:Uns32[min..max],Video_Size_NIR:Uns32[min..max],Exposure_Count_UVVIS1:Uns16[min..max],Exposure_Count_UVVIS2:Uns16[min..max],Exposure_Count_NIR:Uns16[min..max]'},
    '7-8':{'title':'40_Raw_Line_Transmission_Selections_Table','addr':'0x4011174C','N_pad':2,'N_row':50,'N_col':115,'N_len':22850,'def':'PSN:Uns8[min..max],NIR_Filter_0:Uns32[min..max],NIR_Filter_1:Uns32[min..max],NIR_Filter_2:Uns32[min..max],NIR_Filter_3:Uns32[min..max],NIR_Filter_4:Uns32[min..max],NIR_Filter_5:Uns32[min..max],NIR_Filter_6:Uns32[min..max],NIR_Filter_7:Uns32[min..max],NIR_Filter_8:Uns32[min..max],NIR_Filter_9:Uns32[min..max],NIR_Filter_10:Uns32[min..max],NIR_Filter_11:Uns32[min..max],NIR_Filter_12:Uns32[min..max],NIR_Filter_13:Uns32[min..max],NIR_Filter_14:Uns32[min..max],NIR_Filter_15:Uns32[min..max],NIR_Filter_16:Uns32[min..max],NIR_Filter_17:Uns32[min..max],NIR_Filter_18:Uns32[min..max],NIR_Filter_19:Uns32[min..max],NIR_Filter_20:Uns32[min..max],NIR_Filter_21:Uns32[min..max],NIR_Filter_22:Uns32[min..max],NIR_Filter_23:Uns32[min..max],NIR_Filter_24:Uns32[min..max],NIR_Filter_25:Uns32[min..max],NIR_Filter_26:Uns32[min..max],NIR_Filter_27:Uns32[min..max],NIR_Filter_28:Uns32[min..max],NIR_Filter_29:Uns32[min..max],NIR_Filter_30:Uns32[min..max],NIR_Filter_31:Uns32[min..max],NIR_Filter_32:Uns32[min..max],NIR_Filter_33:Uns32[min..max],NIR_Filter_34:Uns32[min..max],NIR_Filter_35:Uns32[min..max],NIR_Filter_36:Uns32[min..max],NIR_Filter_37:Uns32[min..max],UVVIS1_Filter_0:Uns32[min..max],UVVIS1_Filter_1:Uns32[min..max],UVVIS1_Filter_2:Uns32[min..max],UVVIS1_Filter_3:Uns32[min..max],UVVIS1_Filter_4:Uns32[min..max],UVVIS1_Filter_5:Uns32[min..max],UVVIS1_Filter_6:Uns32[min..max],UVVIS1_Filter_7:Uns32[min..max],UVVIS1_Filter_8:Uns32[min..max],UVVIS1_Filter_9:Uns32[min..max],UVVIS1_Filter_10:Uns32[min..max],UVVIS1_Filter_11:Uns32[min..max],UVVIS1_Filter_12:Uns32[min..max],UVVIS1_Filter_13:Uns32[min..max],UVVIS1_Filter_14:Uns32[min..max],UVVIS1_Filter_15:Uns32[min..max],UVVIS1_Filter_16:Uns32[min..max],UVVIS1_Filter_17:Uns32[min..max],UVVIS1_Filter_18:Uns32[min..max],UVVIS1_Filter_19:Uns32[min..max],UVVIS1_Filter_20:Uns32[min..max],UVVIS1_Filter_21:Uns32[min..max],UVVIS1_Filter_22:Uns32[min..max],UVVIS1_Filter_23:Uns32[min..max],UVVIS1_Filter_24:Uns32[min..max],UVVIS1_Filter_25:Uns32[min..max],UVVIS1_Filter_26:Uns32[min..max],UVVIS1_Filter_27:Uns32[min..max],UVVIS1_Filter_28:Uns32[min..max],UVVIS1_Filter_29:Uns32[min..max],UVVIS1_Filter_30:Uns32[min..max],UVVIS1_Filter_31:Uns32[min..max],UVVIS1_Filter_32:Uns32[min..max],UVVIS1_Filter_33:Uns32[min..max],UVVIS1_Filter_34:Uns32[min..max],UVVIS1_Filter_35:Uns32[min..max],UVVIS1_Filter_36:Uns32[min..max],UVVIS1_Filter_37:Uns32[min..max],UVVIS2_Filter_0:Uns32[min..max],UVVIS2_Filter_1:Uns32[min..max],UVVIS2_Filter_2:Uns32[min..max],UVVIS2_Filter_3:Uns32[min..max],UVVIS2_Filter_4:Uns32[min..max],UVVIS2_Filter_5:Uns32[min..max],UVVIS2_Filter_6:Uns32[min..max],UVVIS2_Filter_7:Uns32[min..max],UVVIS2_Filter_8:Uns32[min..max],UVVIS2_Filter_9:Uns32[min..max],UVVIS2_Filter_10:Uns32[min..max],UVVIS2_Filter_11:Uns32[min..max],UVVIS2_Filter_12:Uns32[min..max],UVVIS2_Filter_13:Uns32[min..max],UVVIS2_Filter_14:Uns32[min..max],UVVIS2_Filter_15:Uns32[min..max],UVVIS2_Filter_16:Uns32[min..max],UVVIS2_Filter_17:Uns32[min..max],UVVIS2_Filter_18:Uns32[min..max],UVVIS2_Filter_19:Uns32[min..max],UVVIS2_Filter_20:Uns32[min..max],UVVIS2_Filter_21:Uns32[min..max],UVVIS2_Filter_22:Uns32[min..max],UVVIS2_Filter_23:Uns32[min..max],UVVIS2_Filter_24:Uns32[min..max],UVVIS2_Filter_25:Uns32[min..max],UVVIS2_Filter_26:Uns32[min..max],UVVIS2_Filter_27:Uns32[min..max],UVVIS2_Filter_28:Uns32[min..max],UVVIS2_Filter_29:Uns32[min..max],UVVIS2_Filter_30:Uns32[min..max],UVVIS2_Filter_31:Uns32[min..max],UVVIS2_Filter_32:Uns32[min..max],UVVIS2_Filter_33:Uns32[min..max],UVVIS2_Filter_34:Uns32[min..max],UVVIS2_Filter_35:Uns32[min..max],UVVIS2_Filter_36:Uns32[min..max],UVVIS2_Filter_37:Uns32[min..max]'},
    '7-9':{'title':'41_Measurement_Start_Parameters_Table','addr':'0x40117090','N_pad':0,'N_row':100,'N_col':5,'N_len':500,'def':'PSN:Uns8[min..max],Meas_Start_Ref_Chan_Selection:Uns8[min..max],NIR_Meas_Start_Shift:Uns8[min..max],UVVIS1_Meas_Start_Shift:Uns8[min..max],UVVIS2_Meas_Start_Shift:Uns8[min..max]'},
    '9-1':{'title':'42_VASP_Configuration_Table','addr':'0x40117284','N_pad':0,'N_row':9,'N_col':88,'N_len':792,'def':'VASP_ID:Uns8[min..max],Selection_Cmd_1:Uns8[min..max],Selection_Cmd_2:Uns8[min..max],Selection_Cmd_3:Uns8[min..max],Selection_Cmd_4:Uns8[min..max],Selection_Cmd_5:Uns8[min..max],Selection_Cmd_6:Uns8[min..max],Selection_Cmd_7:Uns8[min..max],Config_Cmd_1:Uns8[min..max],Config_Cmd_2:Uns8[min..max],Config_Cmd_3:Uns8[min..max],Config_Cmd_4:Uns8[min..max],Config_Cmd_5:Uns8[min..max],Config_Cmd_6:Uns8[min..max],Config_Cmd_7:Uns8[min..max],Config_Cmd_8:Uns8[min..max],Config_Cmd_9:Uns8[min..max],Config_Cmd_10:Uns8[min..max],Config_Cmd_11:Uns8[min..max],Config_Cmd_12:Uns8[min..max],Config_Cmd_13:Uns8[min..max],Config_Cmd_14:Uns8[min..max],Config_Cmd_15:Uns8[min..max],Config_Cmd_16:Uns8[min..max],Config_Cmd_17:Uns8[min..max],Config_Cmd_18:Uns8[min..max],Config_Cmd_19:Uns8[min..max],Config_Cmd_20:Uns8[min..max],Config_Cmd_21:Uns8[min..max],Config_Cmd_22:Uns8[min..max],Config_Cmd_23:Uns8[min..max],Config_Cmd_24:Uns8[min..max],Config_Cmd_25:Uns8[min..max],Config_Cmd_26:Uns8[min..max],Config_Cmd_27:Uns8[min..max],Config_Cmd_28:Uns8[min..max],Config_Cmd_29:Uns8[min..max],Config_Cmd_30:Uns8[min..max],Config_Cmd_31:Uns8[min..max],Config_Cmd_32:Uns8[min..max],Config_Cmd_33:Uns8[min..max],Config_Cmd_34:Uns8[min..max],Config_Cmd_35:Uns8[min..max],Config_Cmd_36:Uns8[min..max],Config_Cmd_37:Uns8[min..max],Config_Cmd_38:Uns8[min..max],Config_Cmd_39:Uns8[min..max],Config_Cmd_40:Uns8[min..max],Config_Cmd_41:Uns8[min..max],Config_Cmd_42:Uns8[min..max],Config_Cmd_43:Uns8[min..max],Config_Cmd_44:Uns8[min..max],Config_Cmd_45:Uns8[min..max],Config_Cmd_46:Uns8[min..max],Config_Cmd_47:Uns8[min..max],Config_Cmd_48:Uns8[min..max],Config_Cmd_49:Uns8[min..max],Config_Cmd_50:Uns8[min..max],Config_Cmd_51:Uns8[min..max],Config_Cmd_52:Uns8[min..max],Config_Cmd_53:Uns8[min..max],Config_Cmd_54:Uns8[min..max],Config_Cmd_55:Uns8[min..max],Config_Cmd_56:Uns8[min..max],Config_Cmd_57:Uns8[min..max],Config_Cmd_58:Uns8[min..max],Config_Cmd_59:Uns8[min..max],Config_Cmd_60:Uns8[min..max],Config_Cmd_61:Uns8[min..max],Config_Cmd_62:Uns8[min..max],Config_Cmd_63:Uns8[min..max],Config_Cmd_64:Uns8[min..max],Config_Cmd_65:Uns8[min..max],Config_Cmd_66:Uns8[min..max],Config_Cmd_67:Uns8[min..max],Config_Cmd_68:Uns8[min..max],Config_Cmd_69:Uns8[min..max],Config_Cmd_70:Uns8[min..max],Config_Cmd_71:Uns8[min..max],Config_Cmd_72:Uns8[min..max],Config_Cmd_73:Uns8[min..max],Config_Cmd_74:Uns8[min..max],Config_Cmd_75:Uns8[min..max],Config_Cmd_76:Uns8[min..max],Config_Cmd_77:Uns8[min..max],Config_Cmd_78:Uns8[min..max],Config_Cmd_79:Uns8[min..max],Config_Cmd_80:Uns8[min..max]'},
    '2-9':{'title':'43_Util_Table','addr':'0x4011759C','N_pad':0,'N_row':1,'N_col':20,'N_len':80,'def':'SDE_NS_Axis_Motion_Min_Time:Uns32[0..3000],SDE_EW_Axis_Motion_Min_Time:Uns32[0..3000],Spare_02:Uns32[min..max],Spare_03:Uns32[min..max],Spare_04:Uns32[min..max],Spare_05:Uns32[min..max],Spare_06:Uns32[min..max],Spare_07:Uns32[min..max],Spare_08:Uns32[min..max],Spare_09:Uns32[min..max],Spare_10:Uns32[min..max],Spare_11:Uns32[min..max],Spare_12:Uns32[min..max],Spare_13:Uns32[min..max],Spare_14:Uns32[min..max],Spare_15:Uns32[min..max],Spare_16:Uns32[min..max],Spare_17:Uns32[min..max],Spare_18:Uns32[min..max],Spare_19:Uns32[min..max]'},
    '2-1':{'title':'44_Default_HK_Table','addr':'0x401175EC','N_pad':0,'N_row':100,'N_col':111,'N_len':43300,'def':'HK_SID:Uns32[min..max],Default_Coll_Interval:Uns16[min..max],Default_Generation_Status:Uns8[min..max],Gen_Cond_Operator_1:Uns8[min..max],Gen_Cond_Generation_Par_ID_1:Uns32[min..max],Gen_Cond_Mask_1:Uns32[min..max],Gen_Cond_Expected_Value_1:Uns32[min..max],Gen_Cond_Operator_2:Uns8[min..max],Gen_Cond_Generation_Par_ID_2:Uns32[min..max],Gen_Cond_Mask_2:Uns32[min..max],Gen_Cond_Expected_Value_2:Uns32[min..max],Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max]'},
    '2-2':{'title':'45_Default_Event_Reports','addr':'0x40121F10','N_pad':0,'N_row':1000,'N_col':2,'N_len':5000,'def':'Event_RID:Uns32[min..max],Enabled:Uns8[min..max]'},
    '2-3':{'title':'46_Default_Parameter_Monitoring_Table','addr':'0x40123298','N_pad':0,'N_row':550,'N_col':13,'N_len':25300,'def':'Enabled:Uns8[min..max],Monitoring_ID:Uns32[min..max],Param_ID:Uns32[min..max],Validity_Param_ID:Uns32[min..max],Validity_Mask:Uns32[min..max],Validity_Value:Uns32[min..max],Check_Interval:Uns32[min..max],Check_Repetition:Uns32[min..max],Check_Type:Uns8[min..max],Check_Def_Word_1:Uns32[min..max],Check_Def_Word_2:Uns32[min..max],Check_Def_Word_3:Uns32[min..max],Check_Def_Word_4:Uns32[min..max]'},
    '2-4':{'title':'47_Default_Event_Action_Table','addr':'0x4012956C','N_pad':0,'N_row':100,'N_col':10,'N_len':3700,'def':'Event_RID:Uns32[min..max],Enabled:Uns8[min..max],TC_Word_1:Uns32[min..max],TC_Word_2:Uns32[min..max],TC_Word_3:Uns32[min..max],TC_Word_4:Uns32[min..max],TC_Word_5:Uns32[min..max],TC_Word_6:Uns32[min..max],TC_Word_7:Uns32[min..max],TC_Word_8:Uns32[min..max]'},
    '2-5':{'title':'48_Default_Diagnostics_Report_Table','addr':'0x4012A3E0','N_pad':0,'N_row':50,'N_col':105,'N_len':20600,'def':'Is_HRDM:Uns8[min..max],Enabled:Uns8[min..max],SID:Uns32[min..max],Collection_Interval:Uns16[min..max],NREP:Uns32[min..max],Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max]'},
    '2-8a':{'title':'49_Init_And_Specific_Ancillary_Packets_Table','addr':'0x4012F458','N_pad':0,'N_row':6,'N_col':100,'N_len':2400,'def':'Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max]'},
    '2-8b':{'title':'50_Standard_Ancillary_Packet_Table','addr':'0x4012FDB8','N_pad':2,'N_row':1,'N_col':258,'N_len':1026,'def':'Gen_Cond_Operator_1:Uns8[min..max],Gen_Cond_Generation_Par_ID_1:Uns32[min..max],Gen_Cond_Mask_1:Uns32[min..max],Gen_Cond_Expected_Value_1:Uns32[min..max],Gen_Cond_Operator_2:Uns8[min..max],Gen_Cond_Generation_Par_ID_2:Uns32[min..max],Gen_Cond_Mask_2:Uns32[min..max],Gen_Cond_Expected_Value_2:Uns32[min..max],Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max],Param_ID_101:Uns32[min..max],Param_ID_102:Uns32[min..max],Param_ID_103:Uns32[min..max],Param_ID_104:Uns32[min..max],Param_ID_105:Uns32[min..max],Param_ID_106:Uns32[min..max],Param_ID_107:Uns32[min..max],Param_ID_108:Uns32[min..max],Param_ID_109:Uns32[min..max],Param_ID_110:Uns32[min..max],Param_ID_111:Uns32[min..max],Param_ID_112:Uns32[min..max],Param_ID_113:Uns32[min..max],Param_ID_114:Uns32[min..max],Param_ID_115:Uns32[min..max],Param_ID_116:Uns32[min..max],Param_ID_117:Uns32[min..max],Param_ID_118:Uns32[min..max],Param_ID_119:Uns32[min..max],Param_ID_120:Uns32[min..max],Param_ID_121:Uns32[min..max],Param_ID_122:Uns32[min..max],Param_ID_123:Uns32[min..max],Param_ID_124:Uns32[min..max],Param_ID_125:Uns32[min..max],Param_ID_126:Uns32[min..max],Param_ID_127:Uns32[min..max],Param_ID_128:Uns32[min..max],Param_ID_129:Uns32[min..max],Param_ID_130:Uns32[min..max],Param_ID_131:Uns32[min..max],Param_ID_132:Uns32[min..max],Param_ID_133:Uns32[min..max],Param_ID_134:Uns32[min..max],Param_ID_135:Uns32[min..max],Param_ID_136:Uns32[min..max],Param_ID_137:Uns32[min..max],Param_ID_138:Uns32[min..max],Param_ID_139:Uns32[min..max],Param_ID_140:Uns32[min..max],Param_ID_141:Uns32[min..max],Param_ID_142:Uns32[min..max],Param_ID_143:Uns32[min..max],Param_ID_144:Uns32[min..max],Param_ID_145:Uns32[min..max],Param_ID_146:Uns32[min..max],Param_ID_147:Uns32[min..max],Param_ID_148:Uns32[min..max],Param_ID_149:Uns32[min..max],Param_ID_150:Uns32[min..max],Param_ID_151:Uns32[min..max],Param_ID_152:Uns32[min..max],Param_ID_153:Uns32[min..max],Param_ID_154:Uns32[min..max],Param_ID_155:Uns32[min..max],Param_ID_156:Uns32[min..max],Param_ID_157:Uns32[min..max],Param_ID_158:Uns32[min..max],Param_ID_159:Uns32[min..max],Param_ID_160:Uns32[min..max],Param_ID_161:Uns32[min..max],Param_ID_162:Uns32[min..max],Param_ID_163:Uns32[min..max],Param_ID_164:Uns32[min..max],Param_ID_165:Uns32[min..max],Param_ID_166:Uns32[min..max],Param_ID_167:Uns32[min..max],Param_ID_168:Uns32[min..max],Param_ID_169:Uns32[min..max],Param_ID_170:Uns32[min..max],Param_ID_171:Uns32[min..max],Param_ID_172:Uns32[min..max],Param_ID_173:Uns32[min..max],Param_ID_174:Uns32[min..max],Param_ID_175:Uns32[min..max],Param_ID_176:Uns32[min..max],Param_ID_177:Uns32[min..max],Param_ID_178:Uns32[min..max],Param_ID_179:Uns32[min..max],Param_ID_180:Uns32[min..max],Param_ID_181:Uns32[min..max],Param_ID_182:Uns32[min..max],Param_ID_183:Uns32[min..max],Param_ID_184:Uns32[min..max],Param_ID_185:Uns32[min..max],Param_ID_186:Uns32[min..max],Param_ID_187:Uns32[min..max],Param_ID_188:Uns32[min..max],Param_ID_189:Uns32[min..max],Param_ID_190:Uns32[min..max],Param_ID_191:Uns32[min..max],Param_ID_192:Uns32[min..max],Param_ID_193:Uns32[min..max],Param_ID_194:Uns32[min..max],Param_ID_195:Uns32[min..max],Param_ID_196:Uns32[min..max],Param_ID_197:Uns32[min..max],Param_ID_198:Uns32[min..max],Param_ID_199:Uns32[min..max],Param_ID_200:Uns32[min..max],Param_ID_201:Uns32[min..max],Param_ID_202:Uns32[min..max],Param_ID_203:Uns32[min..max],Param_ID_204:Uns32[min..max],Param_ID_205:Uns32[min..max],Param_ID_206:Uns32[min..max],Param_ID_207:Uns32[min..max],Param_ID_208:Uns32[min..max],Param_ID_209:Uns32[min..max],Param_ID_210:Uns32[min..max],Param_ID_211:Uns32[min..max],Param_ID_212:Uns32[min..max],Param_ID_213:Uns32[min..max],Param_ID_214:Uns32[min..max],Param_ID_215:Uns32[min..max],Param_ID_216:Uns32[min..max],Param_ID_217:Uns32[min..max],Param_ID_218:Uns32[min..max],Param_ID_219:Uns32[min..max],Param_ID_220:Uns32[min..max],Param_ID_221:Uns32[min..max],Param_ID_222:Uns32[min..max],Param_ID_223:Uns32[min..max],Param_ID_224:Uns32[min..max],Param_ID_225:Uns32[min..max],Param_ID_226:Uns32[min..max],Param_ID_227:Uns32[min..max],Param_ID_228:Uns32[min..max],Param_ID_229:Uns32[min..max],Param_ID_230:Uns32[min..max],Param_ID_231:Uns32[min..max],Param_ID_232:Uns32[min..max],Param_ID_233:Uns32[min..max],Param_ID_234:Uns32[min..max],Param_ID_235:Uns32[min..max],Param_ID_236:Uns32[min..max],Param_ID_237:Uns32[min..max],Param_ID_238:Uns32[min..max],Param_ID_239:Uns32[min..max],Param_ID_240:Uns32[min..max],Param_ID_241:Uns32[min..max],Param_ID_242:Uns32[min..max],Param_ID_243:Uns32[min..max],Param_ID_244:Uns32[min..max],Param_ID_245:Uns32[min..max],Param_ID_246:Uns32[min..max],Param_ID_247:Uns32[min..max],Param_ID_248:Uns32[min..max],Param_ID_249:Uns32[min..max],Param_ID_250:Uns32[min..max]'},
    '2-8':{'title':'51_Diagnostic_Ancillary_Packet_Table','addr':'0x401301BC','N_pad':2,'N_row':50,'N_col':111,'N_len':21650,'def':'Anc_Diag_SID:Uns32[min..max],Default_Coll_Interval:Uns16[min..max],Default_Generation_Status:Uns8[min..max],Gen_Cond_Operator_1:Uns8[min..max],Gen_Cond_Generation_Par_ID_1:Uns32[min..max],Gen_Cond_Mask_1:Uns32[min..max],Gen_Cond_Expected_Value_1:Uns32[min..max],Gen_Cond_Operator_2:Uns8[min..max],Gen_Cond_Generation_Par_ID_2:Uns32[min..max],Gen_Cond_Mask_2:Uns32[min..max],Gen_Cond_Expected_Value_2:Uns32[min..max],Param_ID_1:Uns32[min..max],Param_ID_2:Uns32[min..max],Param_ID_3:Uns32[min..max],Param_ID_4:Uns32[min..max],Param_ID_5:Uns32[min..max],Param_ID_6:Uns32[min..max],Param_ID_7:Uns32[min..max],Param_ID_8:Uns32[min..max],Param_ID_9:Uns32[min..max],Param_ID_10:Uns32[min..max],Param_ID_11:Uns32[min..max],Param_ID_12:Uns32[min..max],Param_ID_13:Uns32[min..max],Param_ID_14:Uns32[min..max],Param_ID_15:Uns32[min..max],Param_ID_16:Uns32[min..max],Param_ID_17:Uns32[min..max],Param_ID_18:Uns32[min..max],Param_ID_19:Uns32[min..max],Param_ID_20:Uns32[min..max],Param_ID_21:Uns32[min..max],Param_ID_22:Uns32[min..max],Param_ID_23:Uns32[min..max],Param_ID_24:Uns32[min..max],Param_ID_25:Uns32[min..max],Param_ID_26:Uns32[min..max],Param_ID_27:Uns32[min..max],Param_ID_28:Uns32[min..max],Param_ID_29:Uns32[min..max],Param_ID_30:Uns32[min..max],Param_ID_31:Uns32[min..max],Param_ID_32:Uns32[min..max],Param_ID_33:Uns32[min..max],Param_ID_34:Uns32[min..max],Param_ID_35:Uns32[min..max],Param_ID_36:Uns32[min..max],Param_ID_37:Uns32[min..max],Param_ID_38:Uns32[min..max],Param_ID_39:Uns32[min..max],Param_ID_40:Uns32[min..max],Param_ID_41:Uns32[min..max],Param_ID_42:Uns32[min..max],Param_ID_43:Uns32[min..max],Param_ID_44:Uns32[min..max],Param_ID_45:Uns32[min..max],Param_ID_46:Uns32[min..max],Param_ID_47:Uns32[min..max],Param_ID_48:Uns32[min..max],Param_ID_49:Uns32[min..max],Param_ID_50:Uns32[min..max],Param_ID_51:Uns32[min..max],Param_ID_52:Uns32[min..max],Param_ID_53:Uns32[min..max],Param_ID_54:Uns32[min..max],Param_ID_55:Uns32[min..max],Param_ID_56:Uns32[min..max],Param_ID_57:Uns32[min..max],Param_ID_58:Uns32[min..max],Param_ID_59:Uns32[min..max],Param_ID_60:Uns32[min..max],Param_ID_61:Uns32[min..max],Param_ID_62:Uns32[min..max],Param_ID_63:Uns32[min..max],Param_ID_64:Uns32[min..max],Param_ID_65:Uns32[min..max],Param_ID_66:Uns32[min..max],Param_ID_67:Uns32[min..max],Param_ID_68:Uns32[min..max],Param_ID_69:Uns32[min..max],Param_ID_70:Uns32[min..max],Param_ID_71:Uns32[min..max],Param_ID_72:Uns32[min..max],Param_ID_73:Uns32[min..max],Param_ID_74:Uns32[min..max],Param_ID_75:Uns32[min..max],Param_ID_76:Uns32[min..max],Param_ID_77:Uns32[min..max],Param_ID_78:Uns32[min..max],Param_ID_79:Uns32[min..max],Param_ID_80:Uns32[min..max],Param_ID_81:Uns32[min..max],Param_ID_82:Uns32[min..max],Param_ID_83:Uns32[min..max],Param_ID_84:Uns32[min..max],Param_ID_85:Uns32[min..max],Param_ID_86:Uns32[min..max],Param_ID_87:Uns32[min..max],Param_ID_88:Uns32[min..max],Param_ID_89:Uns32[min..max],Param_ID_90:Uns32[min..max],Param_ID_91:Uns32[min..max],Param_ID_92:Uns32[min..max],Param_ID_93:Uns32[min..max],Param_ID_94:Uns32[min..max],Param_ID_95:Uns32[min..max],Param_ID_96:Uns32[min..max],Param_ID_97:Uns32[min..max],Param_ID_98:Uns32[min..max],Param_ID_99:Uns32[min..max],Param_ID_100:Uns32[min..max]'},
    '3-1':{'title':'52_RTCS_Table','addr':'0x40135650','N_pad':0,'N_row':32768,'N_col':3,'N_len':196608,'def':'Delta_Time:Uns16[min..max],Microcommand_Code:Uns16[min..max],Microcommand_Parameter:Uns16[min..max]'},
    '3-2':{'title':'53_RTCS_Start_Index_Table','addr':'0x40165650','N_pad':0,'N_row':256,'N_col':2,'N_len':768,'def':'RTCS_ID:Uns8[min..max],Start_Index:Uns16[min..max]'},
    '3-3':{'title':'54_Mode_Transition_RTCS_Table','addr':'0x40165950','N_pad':0,'N_row':256,'N_col':2,'N_len':512,'def':'Mode_Transition_Symbol:Uns8[min..max],RTCS_ID:Uns8[min..max]'},
    '3-4':{'title':'55_Measurement_Sequence_Table','addr':'0x40165B50','N_pad':0,'N_row':32768,'N_col':6,'N_len':393216,'def':'Delta_Time:Uns32[min..max],ICID_Number:Uns16[min..max],ICID_Version:Uns16[min..max],Scanner_EW_Target_Angle_ID:Uns16[min..max],Scanner_NS_Target_Angle_ID:Uns8[min..max],Calibration_Equipment:Uns8[min..max]'},
    '3-5':{'title':'56_Measurement_Sequence_Start_Index_Table','addr':'0x401C5B50','N_pad':0,'N_row':512,'N_col':3,'N_len':2048,'def':'Sequence_No:Uns8[min..max],Version_No:Uns8[min..max],Start_Index:Uns16[min..max]'},
    '3-6':{'title':'57_State_RTCS_Reference_Table','addr':'0x401C6350','N_pad':0,'N_row':256,'N_col':3,'N_len':1280,'def':'ICID_Number:Uns16[min..max],ICID_Version:Uns16[min..max],RTCS_ID:Uns8[min..max]'},
    '8-1':{'title':'58_PTD_Version_Table','addr':'0x401C6850','N_pad':4,'N_row':1,'N_col':10,'N_len':12,'def':'Version_No:Uns8[min..max],Revision_No:Uns8[min..max],Branch_No:Uns8[min..max],Patch_No:Uns8[min..max],Year:Uns16[min..max],Month:Uns8[1..12],Day:Uns8[1..31],Hour:Uns8[0..23],Minute:Uns8[0..59],ISO_Checksum:Uns16[min..max]'}}
