#!/usr/bin/env python3
"""
Test the NetCDF functionality in the GUI.
"""

import sys
import os
sys.path.append('.')

from xldt_gui import XLDTReaderGUI
import tkinter as tk

def test_netcdf_functionality():
    """Test NetCDF data loading functionality."""
    
    print("🔍 Testing NetCDF Functionality")
    print("=" * 50)
    
    # Create a minimal GUI instance for testing
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    try:
        gui = XLDTReaderGUI(root)
        
        # Test NetCDF data loading for scan law 16386
        print("Testing NetCDF data loading for scan law 16386...")
        netcdf_data = gui.get_netcdf_data_for_scanlaw(16386)
        
        if netcdf_data:
            print("✅ NetCDF data loaded successfully!")
            print(f"   Scan Law ID: {netcdf_data.get('scan_law_id')}")
            print(f"   Index: {netcdf_data.get('index')}")
            print(f"   NetCDF Path: {netcdf_data.get('netcdf_path')}")
            
            # Check FDA data
            if 'fda_mp_pointer_alpha' in netcdf_data:
                fda_alpha = netcdf_data['fda_mp_pointer_alpha']
                print(f"   FDA Alpha first 10: {list(fda_alpha[:10])}")
                
                # Check if it matches expected pattern
                expected = [11, 5, 1, 1, 1, 1, 1, 1, 7, 3]
                actual = list(fda_alpha[:10])
                if actual == expected:
                    print("   ✅ FDA data matches expected pattern!")
                else:
                    print(f"   ❌ FDA data mismatch. Expected: {expected}, Got: {actual}")
            
            # Test display formatting
            print("\nTesting display formatting...")
            gui.netcdf_text = type('MockText', (), {
                'delete': lambda *args: None,
                'insert': lambda *args: None
            })()
            
            gui.display_netcdf_data(netcdf_data, 16386)
            print("✅ Display formatting completed without errors")
            
        else:
            print("❌ Failed to load NetCDF data")
            
        # Test other scan law IDs
        for scan_id in [0, 1, 2, 16384, 16385]:
            data = gui.get_netcdf_data_for_scanlaw(scan_id)
            if data:
                print(f"✅ Scan law {scan_id}: Data loaded")
            else:
                print(f"❌ Scan law {scan_id}: No data")
                
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        root.destroy()

if __name__ == "__main__":
    test_netcdf_functionality()
