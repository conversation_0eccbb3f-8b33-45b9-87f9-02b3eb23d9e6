# Standard library imports
import os
import pandas as pd

# Local imports
from src.utils.import_utils import config
from src.logger_wrapper import logger
from src.utils.netcdf_utils import read_netCDF
from src.utils.excel_utils import Read_Sheet
from src.utils.xml_utils import PAF_Update, PAF_Update_FCI_REPSEQ, PAF_Update_SSL
from src.utils.activity_params import ActivityParams

# Import NOF data from VCU module
from .FCI_VCU_MEM_NUMOFF import glob_NOF_BlkLen, glob_NOF_StartAdd

def FCI_PAF(
    act_params: ActivityParams,
    satellite: str,
    Which_VCU_Update="",
    channel="",
):
    # Load Configuration
    dict_config = config.CONFIG_INSTRUMENT[act_params.instrument]
    df_PAF_config = pd.read_csv(
        os.path.join(
            config.CONFIG_VALUES["config_folder"], act_params.instrument, "FCI_PAF_Config.csv"
        ),
        keep_default_na=False,
        index_col="Activity_Type",
    )
    
    # Initialize Variables
    new_dict = {}
    chanlist = ["VIS", "NIR", "IR1", "IR2", "IR3"]
    main_files=[]
    used_files=[]
    procedure=df_PAF_config.loc[act_params.activity, "Procedure_Name"]

    if act_params.activity == "Repeat Sequence":
        used_files=main_files=["REPSEQ"]
    elif act_params.activity == "Scan Encoder LUT":
        used_files=main_files=["SCANENC"]
    elif act_params.activity == "Mission Scenario (SL+APC+SSL)":
        used_files=main_files=["SSCANLAW"]
    elif act_params.activity == "VCU Update":
        used_files=["INSTCONF", "DETCONF"]
        main_files=["INSTCONF"]
    
    # Get appropriate slicer values based on activity type
    slicer_1 = getattr(act_params, act_params.get_slicer_name()) or 0
    slicer_2 = act_params.icid_ver if act_params.activity == "VCU Update" else 10000000
    
    str_output_folder = os.path.join(config.CONFIG_VALUES["output_folder"], satellite)

    # Create PAF name
    str_paf_name = f"PAF_{procedure}.xml"
    str_path_paf_template = os.path.join(
        config.CONFIG_VALUES["paf_folder"], act_params.instrument, str_paf_name
    )

    # Get the dictionary of values from the netCDF files for the desired slicing parameter #
    logger.info(f"Reading netCDF files for {procedure}")

    # Read netCDF file - Updated parameter order
    dict_netCDF = read_netCDF(
        act_params=act_params,
        satellite=satellite,
        main_files=main_files,
        used_files=used_files,
        instrument_conf=dict_config,
        fee_id=""
    )

    # Get the dictionary of values from the VSM file for the needed netCDF files
    if act_params.activity == "VCU Update":
        used_files = ["INSTCONF"]

    for File in used_files:
        logger.info(f"Reading VSM file for {procedure}")

        File_Tab = Read_Sheet(dict_config["vsm_path"], File)
        # Find only variables needed for the specific procedureedure
        df_file_tab_procedure = File_Tab[File_Tab["Proc_Used"].str.contains(procedure)]

        # Create dictionary with PAF variable name to netCDF value correspondence
        # This new dictionary replaces the original one with netCDF variable name to netCDF value correspondence
        # Based on VSM file containing PAF variable name to netCDF variable name correspondence
        df_file_tab_procedure.set_index("Name", inplace=True)

        for variable in df_file_tab_procedure.index:
            PAF_var_name = df_file_tab_procedure.loc[variable, "PAF_name"]

            # '#' marks the variables with dimensions >1, which in the VSM are a single entry but multiple entries in the PAF
            if "#" in PAF_var_name:
                name_counter = 1

                for specific in dict_netCDF[variable]:
                    PAF_var_spec_name = PAF_var_name.replace("#", str(name_counter))
                    new_dict[PAF_var_spec_name] = specific
                    name_counter += 1

            if "xxx" in PAF_var_name:
                # 'xxx' marks the variables with dimension > 1 specific for channel (VIS, NIR, IR1, IR2, IR3)
                chan_counter = 0
                for specific in dict_netCDF[variable]:
                    PAF_var_spec_name = PAF_var_name.replace(
                        "xxx", chanlist[chan_counter]
                    )
                    new_dict[PAF_var_spec_name] = specific
                    chan_counter += 1
            else:
                new_dict[PAF_var_name] = dict_netCDF[variable]

    if act_params.activity == "Mission Scenario (SL+APC+SSL)":
        new_dict["MISS_SCEN"] = act_params.mm_slot

    # Prepare channel name format for potential use in filename later
    formatted_channel_name_for_file = ""
    if act_params.activity == "VCU Update" and channel:
         # Always format the channel name for the filename part, regardless of NOF update status
         formatted_channel_name_for_file = f"-{channel}".replace(".", "")

    if act_params.activity == "VCU Update":
        if "Numerical Offset" in Which_VCU_Update:
            # Check if data exists for this channel before accessing global dicts
            if channel in glob_NOF_BlkLen and channel in glob_NOF_StartAdd:
                logger.info(f"Found NUMOFF data for channel {channel}. Setting NOF_Update to YES.")
                new_dict["NOF_Update"] = "YES"
                new_dict["NOF_BlkLen"] = glob_NOF_BlkLen[channel]
                new_dict["NOF_StartAdd"] = glob_NOF_StartAdd[channel]
                # Note: We use formatted_channel_name_for_file later for the PAF_Update call
            else:
                # If no data was generated for this channel, set update to NO
                logger.warning(f"No NUMOFF data found for channel {channel}. Setting NOF_Update to NO.")
                new_dict["NOF_Update"] = "NO"
                # Do not add BlkLen or StartAdd if update is NO

    # Create new PAF file with new values for each needed variable
    if act_params.activity == "Repeat Sequence":
        PAF_Update_FCI_REPSEQ(
            act_params.instrument,
            str_path_paf_template,
            new_dict,
            str_output_folder,
            satellite,
            str(slicer_1),
        )
    elif act_params.activity == "Mission Scenario (SL+APC+SSL)":
        PAF_Update_SSL(
            str_path_paf_template,
            new_dict,
            str_output_folder,
            satellite,
            act_params.instrument,
            str(slicer_1),
            str(act_params.mm_slot),
        )
    else:
        if str(slicer_2) == "10000000":
            str_identifier = str(slicer_1)
            str_MM_id = str(act_params.mm_slot)
            str_channel = ""
        else:
            str_identifier = f"{slicer_1}-Ver{slicer_2}"
            str_MM_id = ""
            # Use the consistently formatted channel name for the filename part
            str_channel = formatted_channel_name_for_file

        PAF_Update(
            str_path_paf_template,
            new_dict,
            str_output_folder,
            satellite,
            "",
            act_params.instrument,
            str_identifier,
            str_MM_id,
            str_channel,
            "",
            act_params.test_mode,
        )
